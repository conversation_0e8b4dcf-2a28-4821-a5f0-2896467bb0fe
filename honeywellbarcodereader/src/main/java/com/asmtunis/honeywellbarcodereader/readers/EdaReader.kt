package com.asmtunis.honeywellbarcodereader.readers

import android.app.Activity
import com.asmtunis.honeywellbarcodereader.listener.BarcodeListener
import com.asmtunis.honeywellbarcodereader.listener.IBareCodeReader
import com.honeywell.aidc.AidcManager
import com.honeywell.aidc.BarcodeFailureEvent
import com.honeywell.aidc.BarcodeReadEvent
import com.honeywell.aidc.BarcodeReader

class EdaReader(var activity: Activity, var listener: BarcodeListener) : IBareCodeReader {
    private var barcodeReader: BarcodeReader? = null
    private var manager: AidcManager? = null


    override fun startListening() {
        startHoneywelllListener(activity, listener)
    }

    private fun startHoneywelllListener(context: Activity, listener: BarcodeListener) {
        AidcManager.create(context) { aidcManager: AidcManager? ->
            try {
                manager = aidcManager
                barcodeReader = manager!!.createBarcodeReader()

                val properties = properties

                barcodeReader?.apply {
                    // Apply the settings
                   setProperties(properties)
                    // set the trigger mode to automatic control
                    setProperty(
                        BarcodeReader.PROPERTY_TRIGGER_CONTROL_MODE,
                        BarcodeReader.TRIGGER_CONTROL_MODE_AUTO_CONTROL
                    )
                    claim()

                    addBarcodeListener(object : BarcodeReader.BarcodeListener {
                        override fun onBarcodeEvent(barcodeReadEvent: BarcodeReadEvent) {
                            if (!context.isDestroyed) {
                                context.runOnUiThread { listener.onSuccess(barcodeReadEvent.barcodeData) }
                            }
                        }

                        override fun onFailureEvent(barcodeFailureEvent: BarcodeFailureEvent) {
                            listener.onFail("Error EDA reader")
                        }
                    })
                }
            } catch (e: Exception) {
                listener.onFail(e.message?: "Error Exception EDA reader")
            }
        }
    }

    override fun resume() {
        if (barcodeReader != null) {
            try {
                barcodeReader!!.claim()
            } catch (e: Exception) {
                startListening()
                e.printStackTrace()
            }
        }
    }

    override fun destroy() {
        if (barcodeReader != null) {
            barcodeReader!!.release()
            barcodeReader!!.close()
            barcodeReader = null
        }
    }

    companion object {
        private val properties: Map<String, Any>
            get() {
                val properties: MutableMap<String, Any> = HashMap()
                // Set Symbologies On/Off
                properties[BarcodeReader.PROPERTY_CODE_128_ENABLED] = true
                properties[BarcodeReader.PROPERTY_GS1_128_ENABLED] = true
                properties[BarcodeReader.PROPERTY_QR_CODE_ENABLED] = true
                properties[BarcodeReader.PROPERTY_CODE_39_ENABLED] = true
                properties[BarcodeReader.PROPERTY_DATAMATRIX_ENABLED] = true
                properties[BarcodeReader.PROPERTY_UPC_A_ENABLE] = true
                properties[BarcodeReader.PROPERTY_EAN_13_ENABLED] = true //false
                properties[BarcodeReader.PROPERTY_UPC_A_TRANSLATE_EAN13] = true //false
                properties[BarcodeReader.PROPERTY_EAN_13_CHECK_DIGIT_TRANSMIT_ENABLED] =
                    true //false
                properties[BarcodeReader.PROPERTY_AZTEC_ENABLED] = true //false
                properties[BarcodeReader.PROPERTY_CODABAR_ENABLED] = true //false
                properties[BarcodeReader.PROPERTY_INTERLEAVED_25_ENABLED] = true //false
                properties[BarcodeReader.PROPERTY_PDF_417_ENABLED] = true //false
                // Set Max Code 39 barcode length
                properties[BarcodeReader.PROPERTY_CODE_39_MAXIMUM_LENGTH] = 10
                // Turn on center decoding
                properties[BarcodeReader.PROPERTY_CENTER_DECODE] = true
                // Enable bad read response
                properties[BarcodeReader.PROPERTY_NOTIFICATION_BAD_READ_ENABLED] = true
                // Sets time period for decoder timeout in any mode
                properties[BarcodeReader.PROPERTY_DECODER_TIMEOUT] = 400
                return properties
            }
    }
}
