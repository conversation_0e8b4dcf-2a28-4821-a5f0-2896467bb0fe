


plugins {
    id("com.android.library")
    alias(libs.plugins.kotlin)
}

android {
    namespace = "com.asmtunis.honeywellsdk"
    compileSdk = libs.versions.targetSdk.get().toInt()

    defaultConfig {
        minSdk =  libs.versions.minSdk.get().toInt()


        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get()
    }

    testOptions {
        unitTests.isIncludeAndroidResources = true
        unitTests.all {
            it.useJUnitPlatform()
        }
    }


}

dependencies {

    implementation(libs.core.ktx)
    configurations.maybeCreate("default")
    artifacts.add("default", file("DataCollection.aar"))

    // implementation(project(":honeywel", configuration = "default"))
    implementation(libs.appcompat)
    implementation(libs.material)
   // testImplementation(libs.junit)
    implementation(libs.kotlin.test.junit)
    androidTestImplementation(libs.androidx.test.ext.junit)
    androidTestImplementation(libs.espresso.core)
}