<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="model"
            type="com.sunmi.samples.printerx.ui.vm.TicketViewModel" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="32px"
            android:paddingTop="24px"
            android:paddingEnd="32px">

            <TextView
                android:id="@+id/ticket_text"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{()->model.printText()}"
                android:text="@string/text_ticket_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/ticket_texts"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{()->model.printTexts()}"
                android:text="@string/text_ticket_texts"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ticket_text" />

            <TextView
                android:id="@+id/ticket_bar"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{()->model.printBar()}"
                android:text="@string/text_ticket_bar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ticket_texts" />

            <TextView
                android:id="@+id/ticket_qr"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{()->model.printQr()}"
                android:text="@string/text_ticket_qr"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ticket_bar" />


            <TextView
                android:id="@+id/ticket_logo"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{(view)->model.printBitmap(view)}"
                android:text="@string/text_ticket_logo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ticket_qr" />


            <TextView
                android:id="@+id/ticket_line"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{()->model.printLine()}"
                android:text="@string/text_ticket_line"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ticket_logo" />

            <TextView
                android:id="@+id/ticket_all"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{(view)->model.printTicket(view)}"
                android:text="@string/text_ticket_all"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ticket_line" />


            <TextView
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{(view)->model.printTicketAndListen(view)}"
                android:text="@string/text_ticket_result"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ticket_all" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>