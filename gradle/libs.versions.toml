[versions]
minSdk = "24"
targetSdk = "35"

jvmTarget = "17"

versionMajor = "0"
versionMinor = "0"
versionPatch = "8"

packagename = "com.asmtunis.procaisseinventory"

escposkmp = "0.0.6"
kmpUiKitAndroid = "0.0.59"

devtools-ksp = "2.1.20-2.0.0"
kotlin = "2.1.20"


slf4j = "2.0.17"


composeBom = "2025.04.01"
core-ktx = "1.16.0"
activity-compose = "1.10.1"
androidx-test-ext-junit = "1.2.1"
espresso-core = "3.6.1"
appcompat = "1.7.0"
material = "1.12.0"



androidGradlePlugin = "8.9.2"
vanniktechPublishing = "0.31.0"
kotest = "5.9.1"
ktor = "3.1.3"
coroutines = "1.10.2"

kotlin-serialization = "1.8.1"

#WorkManager
work-runtime = "2.10.1"
hilt-work = "1.2.0"


mockkVersion = "1.14.2"

# DI
dagger = "2.56.2"
dagger-compose = "1.2.0"

#compose view model
androidxnavigation = "2.8.9"
androidxlifecycle = "2.8.7"

androidx-window = "1.3.0"


adaptiveNavigationSuite = "1.3.2"
material3-adaptive = "1.1.0"

#firebase
bom-firebase = "33.13.0"

#in app update
app-update = "2.1.0"

# services-google
services-google = "4.4.2"

#crashlytics-firebase
crashlytics-firebase ="3.0.3"
#camera

camera-vision = "1.5.0-alpha06"
barcode = "17.3.0"
zxing-core = "3.5.3"



#kotlin date time
datetime = "0.6.2"

desugar-jdk = "2.1.5"





#wifi print
itextpdf = "9.1.0"
#coil
coil-compose = "3.1.0"



#phone number lib
libnumber-phone = "9.0.4"


#splashscreen
splashscreen = "1.0.1"

#DataStore
datastore-preferences = "1.1.5" # 1.1.5 sometime app dont build !!

#Room
room = "2.7.1"
pagingRuntime = "3.3.6"

#Custom Toast
sonnertoast = "0.3.8"

#Location Services
services-location = "21.3.0"

#google map
map = "19.2.0"
map-compose = "6.6.0"
mapsplatform-plugin = "2.0.1"

#chart
composableGraphs = "v1.2.3"



#lottiefiles
lottie = "6.6.6"

#permission handling in compose

runner = "1.6.2"

accompanist = "0.37.3"
junitVersion = "4.13.2"

[libraries]
accompanist-adaptive = { module = "com.google.accompanist:accompanist-adaptive", version.ref = "accompanist" }
#permission handling in compose
permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanist" }

escposkmp = { module = "io.github.simapps:escposkmp", version.ref = "escposkmp" }
kmp-ui-kit-android = { module = "io.github.simapps:kmp-ui-kit", version.ref = "kmpUiKitAndroid" }

kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "coroutines" }


kotlinx-serialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlin-serialization" }

#KTOR
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-android = { module = "io.ktor:ktor-client-android", version.ref = "ktor" }
ktor-serialization = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-client-contentNegotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-network = { module = "io.ktor:ktor-network", version.ref = "ktor" }

#KOTEST
kotest-assertions-core = { module = "io.kotest:kotest-assertions-core", version.ref = "kotest" }
kotest-framework-datatest = { module = "io.kotest:kotest-framework-datatest", version.ref = "kotest" }
kotest-framework-engine = { module = "io.kotest:kotest-framework-engine", version.ref = "kotest" }
kotest-runner-junit5 = { module = "io.kotest:kotest-runner-junit5", version.ref = "kotest" }
#kotest-runner-junit5-jvm = { module = "io.kotest:kotest-runner-junit5-jvm", version.ref = "kotest" }
kotest-property = { module = "io.kotest:kotest-property", version.ref = "kotest" }

kotlin-test-common-annotations = { module = "org.jetbrains.kotlin:kotlin-test-annotations-common", version.ref = "kotlin" }
kotlin-test-common-assertions = { module = "org.jetbrains.kotlin:kotlin-test-common", version.ref = "kotlin" }




slf4j-simple = { module = "org.slf4j:slf4j-simple", version.ref = "slf4j" }
mockk = { module = "io.mockk:mockk", version.ref = "mockkVersion" }

kotlin-test-junit = { module = "org.jetbrains.kotlin:kotlin-test-junit", version.ref = "kotlin" }

androidx-runner = { group = "androidx.test", name = "runner", version.ref = "runner" }
androidx-test-ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidx-test-ext-junit" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espresso-core" }


core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "core-ktx" }
lifecycle-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "androidxlifecycle" }


material = { group = "com.google.android.material", name = "material", version.ref = "material" }

#androidX
androidx-activity = { group = "androidx.activity", name = "activity-compose", version.ref = "activity-compose" }

appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-navigation = { module = "androidx.navigation:navigation-compose", version.ref = "androidxnavigation" }
androidx-lifecycle = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "androidxlifecycle" }
androidx-lifecycle-runtime-compose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version.ref = "androidxlifecycle" }
androidx-window-core = { module = "androidx.window:window-core", version.ref = "androidx-window" }



compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
compose-ui = { group = "androidx.compose.ui", name = "ui" }
compose-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
compose-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
compose-ui-util = { group = "androidx.compose.ui", name = "ui-util" }
compose-material-iconsExtended = { group = "androidx.compose.material", name = "material-icons-extended" }
compose-material3 = { group = "androidx.compose.material3", name = "material3" }
compose-material3-window-core = { group = "androidx.compose.material3", name = "material3-window-size-class" }


androidx-compose-material3-adaptive = { module = "androidx.compose.material3.adaptive:adaptive", version.ref = "material3-adaptive" }
androidx-compose-material3-adaptive-navigation = { module = "androidx.compose.material3.adaptive:adaptive-navigation", version.ref = "material3-adaptive" }
androidx-compose-material3-adaptive-navigation-layout = { module = "androidx.compose.material3.adaptive:adaptive-layout", version.ref = "material3-adaptive" }
androidx-compose-material3-adaptive-navigation-suite = { module = "androidx.compose.material3:material3-adaptive-navigation-suite", version.ref = "adaptiveNavigationSuite" }







#Camerax dependencies

# TODO Implement the CameraXViewfinder composable
# androidx-camera-compose = { module = "androidx.camera:camera-compose", version.ref = "camera-vision" }
camera-core = { module = "androidx.camera:camera-core", version.ref = "camera-vision" }
camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "camera-vision" }
camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camera-vision" }
camera-view = { module = "androidx.camera:camera-view", version.ref = "camera-vision" }
camera-extensions = { module = "androidx.camera:camera-extensions", version.ref = "camera-vision" }
camera-mlkit-vision = { module = "androidx.camera:camera-mlkit-vision", version.ref = "camera-vision" }

barcode-scanning = { module = "com.google.mlkit:barcode-scanning", version.ref = "barcode" }
zxing = { module = "com.google.zxing:core", version.ref = "zxing-core" }


#coil
coil = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coil-compose" }


#phone number lib
libphonenumber = { module = "com.googlecode.libphonenumber:libphonenumber", version.ref = "libnumber-phone" }


#Room
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-paging-compose = { module = "androidx.paging:paging-compose", version.ref = "pagingRuntime" }
androidx-paging-runtime = { module = "androidx.paging:paging-runtime", version.ref = "pagingRuntime" }
androidx-room-paging = { module = "androidx.room:room-paging", version.ref = "room" }
#room-paging-compiler = { group = "androidx.paging", name = "paging-compiler", version.ref = "room" }

# DI
dagger-hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "dagger" }
dagger-hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "dagger" }
dagger-hilt-navigation-compose = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "dagger-compose" }
dagger-hilt-androidx-compiler = { module = "androidx.hilt:hilt-compiler", version.ref = "dagger-compose" }



#DataStore
datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastore-preferences" }

#Custom Toast
sonnerToast = { module = "io.github.dokar3:sonner", version.ref = "sonnertoast" }
#Location Services
android-gms-location = { module = "com.google.android.gms:play-services-location", version.ref = "services-location" }


#google map
android-gms-map = { module = "com.google.android.gms:play-services-maps", version.ref = "map" }
android-gms-map-compose = { module = "com.google.maps.android:maps-compose", version.ref = "map-compose" }
android-gms-map-compose-utils = { module = "com.google.maps.android:maps-compose-utils", version.ref = "map-compose" }
android-gms-map-compose-widgets = { module = "com.google.maps.android:maps-compose-widgets", version.ref = "map-compose" }



#Work Manager
workruntime = { module = "androidx.work:work-runtime-ktx", version.ref = "work-runtime" }
hiltwork = { module = "androidx.hilt:hilt-work", version.ref = "hilt-work" }



#charts
#charts = { module = "com.github.tehras:charts", version.ref = "chart" }
# you can consider using this kmp lib io.github.bytebeats:compose-charts
graphscomposable = { module = "com.github.jaikeerthick:Composable-Graphs", version.ref = "composableGraphs" }






#lottiefiles
lottie = { module = "com.airbnb.android:lottie-compose", version.ref = "lottie" }





#splashscreen
core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "splashscreen" }


#kotlin date Time
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "datetime" }
desugar = { module = "com.android.tools:desugar_jdk_libs", version.ref = "desugar-jdk" }


#WIFI PRINT
itextpdf = { module = "com.itextpdf:io", version.ref = "itextpdf" }
itextpdf-kernel = { module = "com.itextpdf:kernel", version.ref = "itextpdf" }
itextpdf-layout = { module = "com.itextpdf:layout", version.ref = "itextpdf" }


#firebase
firebas-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "bom-firebase" }
#implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging" }





#in app update
update-app = { module = "com.google.android.play:app-update", version.ref = "app-update" }
update-app-ktx = { module = "com.google.android.play:app-update-ktx", version.ref = "app-update" }







gradle = { module = "com.android.tools.build:gradle", version.ref = "androidGradlePlugin" }
serializationK = { module = "org.jetbrains.kotlin:kotlin-serialization", version.ref = "kotlin" }
gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }



#material3
androidx-lifecycle-service = { group = "androidx.lifecycle", name = "lifecycle-service", version.ref = "androidxlifecycle" }
junit = { group = "junit", name = "junit", version.ref = "junitVersion" }











[bundles]
compose = [
    "compose-ui",
    "compose-ui-util",
    "compose-graphics",
    "compose-preview",
    "compose-material3",
    "compose-material-iconsExtended",
    "compose-material3-window-core"
]

firebase = [
    "firebase-analytics",
    "firebase-crashlytics",
    "firebase-messaging"
]









[plugins]

androidApplication = { id = "com.android.application", version.ref = "androidGradlePlugin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "devtools-ksp" }
hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "dagger" }
mapsplatform = { id = "com.google.android.libraries.mapsplatform.secrets-gradle-plugin", version.ref = "mapsplatform-plugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }
vanniktechPublishingBase = { id = "com.vanniktech.maven.publish.base", version.ref = "vanniktechPublishing" }

kotest-multiplatform = { id = "io.kotest.multiplatform", version.ref = "kotest" }

kotlin = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-multiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
google-services = { id = "com.google.gms.google-services", version.ref = "services-google" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics-firebase" }

compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }


#serializationX = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }

