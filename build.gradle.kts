
plugins {
    alias(libs.plugins.androidApplication) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.hilt.android) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.mapsplatform) apply false
    alias(libs.plugins.google.services) apply false
    alias(libs.plugins.firebase.crashlytics) apply false

    // kotlin("android").version("1.9.0").apply(false)
    // kotlin("multiplatform").version("1.9.10").apply(false)
    alias(libs.plugins.kotlin.multiplatform).apply(false)
    alias(libs.plugins.kotlin) apply false

    alias(libs.plugins.compose.compiler) apply false



}

buildscript {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()


    }
    dependencies {
        classpath(libs.gradle)
        classpath(libs.serializationK)
        classpath(libs.gradle.plugin)
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()

        maven { url = uri("https://jitpack.io") }


    }
}

tasks.register("clean", Delete::class) {
    delete(rootProject.layout.buildDirectory)
}
