package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.filter

import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch



data class BonLivraisonFilterListState(
    val lists: Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>> = emptyMap(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch(),
    val filterByTicketEtat: String = "",
    val filterBySessionCaisse: String = "",
    val filterByTicketSource: String = ""
)