package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api.ImmobilisationApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api.ImmobilisationApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object ImmobilisationRemoteModule {

    @Provides
    @Singleton
    fun provideImmobilisationApi(client: HttpClient): ImmobilisationApi = ImmobilisationApiImpl(client)


}