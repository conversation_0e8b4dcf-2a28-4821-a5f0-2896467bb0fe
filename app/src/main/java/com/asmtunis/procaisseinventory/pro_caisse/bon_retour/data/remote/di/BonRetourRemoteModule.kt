package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.bon_retour.BonRetourApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.bon_retour.BonRetourApiImpl
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.ligne_bon_retour.LigneBonRetourApi
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.ligne_bon_retour.LigneBonRetourApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object BonRetourRemoteModule {

    @Provides
    @Singleton
    fun provideBonRetourApi(client: HttpClient): BonRetourApi = BonRetourApiImpl(client)


    @Provides
    @Singleton
    fun provideLigneBonRetourApi(client: HttpClient): LigneBonRetourApi = LigneBonRetourApiImpl(client)

}