package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.ligne_bon_commande

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class LigneBonCommandeApiImpl(private val client: HttpClient) : LigneBonCommandeApi {


    override suspend fun getLigneBonCommande(
        baseConfig: String
    ): Flow<DataResult<List<LigneBonCommande>>> = flow {


        val result = executePostApiCall<List<LigneBonCommande>>(
            client = client,
            endpoint = Urls.GET_LIGNE_COMMANDE,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun addBatchLigneCommande(baseConfig: String): Flow<DataResult<List<LigneBonCommande>>> = flow {

        val result = executePostApiCall<List<LigneBonCommande>>(
            client = client,
            endpoint = Urls.ADD_BATCH_LIGNE_COMMANDE,
            baseConfig = baseConfig
        )

        emitAll(result)

    }
}