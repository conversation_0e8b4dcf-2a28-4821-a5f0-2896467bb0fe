package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementUpdate
import kotlinx.coroutines.flow.Flow


interface ReglementCaisseApi {
    suspend fun getReglementCaisseByTicket(baseConfig: String): Flow<DataResult<List<ReglementCaisse>>>
    suspend fun getReglementCaisseByTickets(baseConfig: String): Flow<DataResult<List<List<ReglementCaisse>>>>
    suspend fun getReglementCaisseBySession(baseConfig: String, exercice :String, archive : Boolean): Flow<DataResult<List<ReglementCaisse>>>
    suspend fun addBatchPayments(baseConfig: String): Flow<DataResult<List<ReglementUpdate>>>
}