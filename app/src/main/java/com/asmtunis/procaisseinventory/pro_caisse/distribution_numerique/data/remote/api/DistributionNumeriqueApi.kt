package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.ResponseAddBatchVisiteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import kotlinx.coroutines.flow.Flow

interface DistributionNumeriqueApi {



    suspend fun getAllTypeService(baseConfig: String): Flow<DataResult<List<TypeServicesDn>>>
    suspend fun getAllFamille(baseConfig: String): Flow<DataResult<List<FamilleDn>>>
    suspend fun getAllSuperficie(baseConfig: String): Flow<DataResult<List<SuperficieDn>>>
    suspend fun getAllTypePVente(baseConfig: String): Flow<DataResult<List<TypePointVenteDn>>>
    suspend fun getAllVisiteByUser(baseConfig: String): Flow<DataResult<List<VisitesDn>>>
    suspend fun getAllLigneVisite(baseConfig: String): Flow<DataResult<List<LigneVisitesDn>>>
    suspend fun getVisiteByCode(baseConfig: String): Flow<DataResult<LigneVisitesDn>>
    suspend fun addBatchVisite(baseConfig: String): Flow<DataResult<List<ResponseAddBatchVisiteDn>>>
    suspend fun deleteVisite(baseConfig: String): Flow<DataResult<List<ResponseAddBatchVisiteDn>>>
}