package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ticket

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import kotlinx.coroutines.flow.Flow


interface TicketApi {



    suspend fun getTicketsByCaisseId(baseConfig: String,
                                     ddm : String,
                                     exercice : String,
                                     archive : Boolean,
                                     zone : Boolean): Flow<DataResult<List<Ticket>>>


    suspend fun getMaxNumTicket(baseConfig: String,
                                idExerc : String,
                                idCarnet : String
                                ): Flow<DataResult<Int>>



    suspend fun addBatchTicketWithLignesTicketAndPayment(
        baseConfig: String,
        autoFacture : Boolean
    ): Flow<DataResult<List<TicketUpdate>>>


    suspend fun addBatchFactureWithLines(baseConfig: String): Flow<DataResult<List<TicketUpdate>>>

}
