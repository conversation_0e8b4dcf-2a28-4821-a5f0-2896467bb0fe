package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.repository

import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import kotlinx.coroutines.flow.Flow


interface EtatOrdreMissionLocalRepository {
    fun upsertAll(value: List<EtatOrdreMission>)
    fun getOneByCode(code: String): Flow<EtatOrdreMission?>
    fun getAll(): Flow<List<EtatOrdreMission>?>

    fun deleteAll()
}