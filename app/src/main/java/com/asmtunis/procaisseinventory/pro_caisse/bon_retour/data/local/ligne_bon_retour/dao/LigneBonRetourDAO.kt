package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LIGNE_BON_RETOUR_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneBonRetourDAO {
    @get:Query("SELECT * FROM $LIGNE_BON_RETOUR_TABLE")
    val all: Flow<List<LigneBonRetour>>

    @Query("SELECT * FROM $LIGNE_BON_RETOUR_TABLE where NumBon_Retour=:code")
    fun getByBRNum(code: String): Flow<List<LigneBonRetour>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: LigneBonRetour)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<LigneBonRetour>)

    @Query("UPDATE $LIGNE_BON_RETOUR_TABLE SET isSync = 1, Status= 'SELECTED', NumBon_Retour = :newNum where NumBon_Retour = :oldNum")
    fun setSynced(newNum: String, oldNum: String)

    @Query("DELETE FROM $LIGNE_BON_RETOUR_TABLE")
    fun deleteAll()


    @Query("DELETE FROM $LIGNE_BON_RETOUR_TABLE where NumBon_Retour = :codeRetour and LIG_BonEntree_Exerc =:exercice")
    fun deleteById(codeRetour: String, exercice: String)

    @Query("SELECT sum(lIGBonEntreeMntTTC) FROM $LIGNE_BON_RETOUR_TABLE where NumBon_Retour=:bonRetourNum and LIG_BonEntree_Exerc=:exercie ")
    fun getSumPriceByBr(bonRetourNum: String, exercie: String): Flow<Double>
}