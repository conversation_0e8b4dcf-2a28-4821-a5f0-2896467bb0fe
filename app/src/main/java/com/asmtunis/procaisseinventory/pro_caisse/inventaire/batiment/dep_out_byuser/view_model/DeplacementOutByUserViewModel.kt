package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui.DeplacementOutByUserListState
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class DeplacementOutByUserViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork
) : ViewModel() {



    var showCustomFilter: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }
    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var floatingBtnIsVisible: Boolean by mutableStateOf(true)
        private set
    fun onFloatingBtnIsVisibleChange(value: Boolean) {
        floatingBtnIsVisible = value
    }




    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

     
 


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }

    var depOutByUserListstate: DeplacementOutByUserListState by mutableStateOf(
        DeplacementOutByUserListState()
    )
        private set

    fun setInvPatrimoineListstateFilter(state : ListEvent.FirstCustomFilter){
        depOutByUserListstate = depOutByUserListstate.copy(
            filterByTypeInventaire = state.firstFilter
        )
    }

    var selectedLigneInvPatrimoine: LigneBonCommande by mutableStateOf(LigneBonCommande())
        private set
    fun onSelectedLigneInvPatrimoineChange(value: LigneBonCommande) {
        selectedLigneInvPatrimoine = value
    }
    var selectedListLg = mutableStateListOf<LigneBonCommande>()
        private set
    var selectedDeplacementOutByUser: DeplacementOutByUserWithImmobilisation by mutableStateOf(DeplacementOutByUserWithImmobilisation())
        private set
    fun onSelectedDeplacementOutByUserChange(value: Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>) {

        value.forEach { (key, value) ->
            run {
                selectedDeplacementOutByUser = key
                selectedListLg.addAll(value)
            }
        }

    }


    fun restSelectedDeplacementOutByUser(){
        selectedListLg.clear()
        selectedDeplacementOutByUser = DeplacementOutByUserWithImmobilisation()
    }


    fun onEvent(
        event: ListEvent
    ) {
        when (event) {
            is ListEvent.Order -> {
                if (depOutByUserListstate.listOrder::class == event.listOrder::class &&
                    depOutByUserListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                depOutByUserListstate = depOutByUserListstate.copy(
                    listOrder = event.listOrder
                )
                filterInvPatrimoine(
                    deplacementOutByUserFilterListState = depOutByUserListstate
                )
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()

            is ListEvent.ListSearch -> {
                depOutByUserListstate = depOutByUserListstate.copy(
                    search = event.listSearch
                )

                filterInvPatrimoine(deplacementOutByUserFilterListState = depOutByUserListstate)
            }


            is ListEvent.FirstCustomFilter -> {
                depOutByUserListstate = depOutByUserListstate.copy(
                    onlyWaiting = event.firstFilter
                )

                filterInvPatrimoine(deplacementOutByUserFilterListState = depOutByUserListstate)
            }

            is ListEvent.SecondCustomFilter -> {
                //TODO IF WE ADD FILTERS
            }
            is ListEvent.ThirdCustomFilter -> TODO()
        }

    }
    //var getInvPatrimoineJob: Job = Job()
    fun filterInvPatrimoine(
        deplacementOutByUserFilterListState: DeplacementOutByUserListState
    ) {
        val searchedText = searchTextState.text
        val searchValue = deplacementOutByUserFilterListState.search
        val onlyWaiting = deplacementOutByUserFilterListState.onlyWaiting
    //    getInvPatrimoineJob.cancel()

        if (searchedText.isEmpty()) {
          /*  getInvPatrimoineJob = */when (deplacementOutByUserFilterListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (deplacementOutByUserFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.deplacementOutByUser.getAllFiltred(
                                onlyWaiting = onlyWaiting,
                                isAsc = 1,
                                sortBy = "DEV_Num"
                            ).collect {
                                setInvPatrimoineList(deplacementOutByUser = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.deplacementOutByUser.getAllFiltred(
                                onlyWaiting = onlyWaiting,
                                isAsc = 1,
                                sortBy = "DDmM"

                            ).collect {
                                setInvPatrimoineList(deplacementOutByUser = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.deplacementOutByUser.getAllFiltred(
                                onlyWaiting = onlyWaiting,
                                isAsc = 1,
                                sortBy = "DEV_CodeClient"

                            ).collect {
                                setInvPatrimoineList(deplacementOutByUser = it)
                            }
                        }
                    }
                }

                is OrderType.Descending -> {
                    when (deplacementOutByUserFilterListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proCaisseLocalDb.deplacementOutByUser.getAllFiltred(
                                onlyWaiting = onlyWaiting,
                                isAsc = 2,
                                sortBy = "DEV_Num"
                            ).collect {
                                setInvPatrimoineList(deplacementOutByUser = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proCaisseLocalDb.deplacementOutByUser.getAllFiltred(
                                onlyWaiting = onlyWaiting,
                                isAsc = 2,
                                sortBy = "DDmM"

                            ).collect {
                                setInvPatrimoineList(deplacementOutByUser = it)
                            }
                        }

                        is ListOrder.Third -> viewModelScope.launch {
                            proCaisseLocalDb.deplacementOutByUser.getAllFiltred(
                                onlyWaiting = onlyWaiting,
                                isAsc = 2,
                                sortBy = "DEV_CodeClient"

                            ).collect {
                                setInvPatrimoineList(deplacementOutByUser = it)
                            }
                        }
                    }
                }
            }
        } else {
            if (searchedText.isNotEmpty()) {
                if (searchValue is ListSearch.FirstSearch) {
                   /* getInvPatrimoineJob =*/ when (deplacementOutByUserFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (deplacementOutByUserFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBonCommandeNum(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_Num",
                                        isAsc = 1

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBonCommandeNum(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DDmM",
                                        isAsc = 1

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBonCommandeNum(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_CodeClient",
                                        isAsc = 1
                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (deplacementOutByUserFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBonCommandeNum(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_Num",
                                        isAsc = 2

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBonCommandeNum(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DDmM",
                                        isAsc = 2

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBonCommandeNum(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_CodeClient",
                                        isAsc = 2

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.SecondSearch) {
                   /* getInvPatrimoineJob =*/  when (deplacementOutByUserFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (deplacementOutByUserFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBatiment(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_Num",
                                        isAsc = 1

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBatiment(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DDmM",
                                        isAsc = 1
                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBatiment(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_CodeClient",
                                        isAsc = 1
                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (deplacementOutByUserFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBatiment(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_Num",
                                        isAsc = 2
                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBatiment(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DDmM",
                                        isAsc = 2
                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByBatiment(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_CodeClient",
                                        isAsc = 2

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }
                            }
                        }
                    }
                }

                if (searchValue is ListSearch.ThirdSearch) {
                   /* getInvPatrimoineJob =*/  when (deplacementOutByUserFilterListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (deplacementOutByUserFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByNumSerie(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_Num",
                                        isAsc = 1

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByNumSerie(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DDmM",
                                        isAsc = 1

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByNumSerie(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_CodeClient",
                                        isAsc = 1

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }
                            }
                        }

                        is OrderType.Descending -> {
                            when (deplacementOutByUserFilterListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByNumSerie(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_Num",
                                        isAsc = 2
                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByNumSerie(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DDmM",
                                        isAsc = 2

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }

                                is ListOrder.Third -> viewModelScope.launch {
                                    proCaisseLocalDb.deplacementOutByUser.filterByNumSerie(
                                        onlyWaiting = onlyWaiting,
                                        searchString = searchedText,
                                        sortBy = "DEV_CodeClient",
                                        isAsc = 2

                                    ).collect {
                                        setInvPatrimoineList(deplacementOutByUser = it)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }




    private fun setInvPatrimoineList(deplacementOutByUser:  Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>){

        depOutByUserListstate = depOutByUserListstate.copy(
            lists =  emptyMap() // ,
            // listOrder = articlesListState.listOrder
        )

        depOutByUserListstate = depOutByUserListstate.copy(
            lists = deplacementOutByUser // ,
            // listOrder = articlesListState.listOrder
        )

      //  getInvPatrimoineJob.cancel()
    }







}