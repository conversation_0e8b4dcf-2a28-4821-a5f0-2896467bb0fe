package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import kotlinx.coroutines.flow.Flow


@Dao
interface TypeMouvementDAO {
    @get:Query("SELECT * FROM ${ProCaisseConstants.TYPE_MOUVEMENT}")
    val all: Flow<List<TypeMouvement>>



    @get:Query("SELECT * FROM ${ProCaisseConstants.TYPE_MOUVEMENT} LIMIT 1")
    val one: Flow<TypeMouvement>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: TypeMouvement)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<TypeMouvement>)

    @Query("DELETE FROM ${ProCaisseConstants.TYPE_MOUVEMENT}")
    fun deleteAll()
}