package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.getStationStockArticle
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationState
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.enum_classes.TicketState
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.utils.mobilecode.MobileCodeGeneration
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.filter.BonLivraisonFilterListState
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject


@OptIn(SavedStateHandleSaveableApi::class)
@HiltViewModel
class BonLivraisonViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    var sessionCaisseFilter: String by mutableStateOf("")
        private set

    fun onSessionCaisseFilterChange(value: String) {
        sessionCaisseFilter = value
    }


    var sessionCaisseExpanded by savedStateHandle.saveable { mutableStateOf(false) }
        private set

    fun onSessionCaisseExpandedChange(value: Boolean) {
        sessionCaisseExpanded = value
    }


    var selectedSessionCaisse by mutableStateOf(SessionCaisse())
        private set

    fun onSelectedSessionCaisseChange(value: SessionCaisse) {
        selectedSessionCaisse = value
    }

    var showEnterAdressView: Boolean by mutableStateOf(false)
        private set

    fun onShowEnterAdressViewChange(value: Boolean) {
        showEnterAdressView = value
    }

    var customAdress: String by mutableStateOf("")
        private set

    fun onCustomAdressChange(value: String) {
        customAdress = value
    }

    var showTikPaymentView: Boolean by mutableStateOf(false)
        private set

    fun onShowTikPaymentViewChange(value: Boolean) {
        showTikPaymentView = value
    }


    var update by mutableStateOf(false)
        private set

    fun onUpdateChange(value: Boolean) {
        update = value
    }

    var showBottomPaymentView: Boolean by mutableStateOf(false)
        private set

    fun onShowBottomPaymentViewChange(value: Boolean) {
        showBottomPaymentView = value
    }

    val paymentModeList = listOf("Réglement", "Réglement partiel", "Crédit")

    var selectedPaymentMode by mutableStateOf(paymentModeList.first())
        private set

    fun onSelectedPaymentModeChange(value: String) {
        selectedPaymentMode = value
    }

    var showSearchView: Boolean by mutableStateOf(false)
        private set

    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

    var showCustomFilter: Boolean by mutableStateOf(false)
        private set

    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }

    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set

    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set

    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }


    var selectedLigneBonLivraison: LigneTicket by mutableStateOf(LigneTicket())
        private set

    fun onSelectedLigneBonTransfertChange(value: LigneTicket) {
        selectedLigneBonLivraison = value
    }

    var selectedListLgBonLivraison = mutableStateListOf<LigneTicket>()
        private set
    var selectedListLgBonLivraisonWithArticle = mutableStateListOf<LigneTicketWithArticle>()
        private set
    var selectedBonLivraisonWithFactureAndPayments: TicketWithFactureAndPayments by mutableStateOf(
        TicketWithFactureAndPayments()
    )
        private set


    fun onSelectedBonLivraisonChange(value: Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>) {
        //   restBonLivraison()
        value.forEach { (key, value) ->
            run {
                selectedBonLivraisonWithFactureAndPayments = key
                selectedListLgBonLivraison.addAll(value.map { it.ligneTicket!! })
                selectedListLgBonLivraisonWithArticle.addAll(value)
            }
        }

    }

    fun restBonLivraison() {
        selectedListLgBonLivraison.clear()
        selectedListLgBonLivraisonWithArticle.clear()
        selectedBonLivraisonWithFactureAndPayments = TicketWithFactureAndPayments()
    }


    var bonTransfertListstate: BonLivraisonFilterListState by mutableStateOf(BonLivraisonFilterListState())
        private set


    var codeM: String by mutableStateOf("")
        private set

    fun generateCodeM(utilisateur: Utilisateur, prefix: String) {


        val stationUtilisateur = utilisateur.Station
        val userID = utilisateur.codeUt

        codeM =
            prefix + "_" + stationUtilisateur + "_" + userID + "_" + MobileCodeGeneration.generateCommonCode(
                num = Math.random().toInt().toString(),
                numLign = Math.random().toInt().toString()
            )


        codeM = codeM.replace("__", "_")
    }


    /*  var bonTransfertPrefix: Prefixe by mutableStateOf(Prefixe())
         private set
       fun getBonTransfertPrefix(utilisateur: Utilisateur) {
          viewModelScope.launch {
              proCaisseLocalDb.prefix.getOneById("bon_transfert").collect {
                  if(it!=null){
                      bonTransfertPrefix = it
  
                      if(it.pREPrefixe!=null) generateBonEntreeNum(it.pREPrefixe!!,utilisateur)
                  }
  
              }
          }
      }
  
      var bonTransfertGeneratedNum: String by mutableStateOf("")
          private set
    fun generateBonEntreeNum(prefix : String,utilisateur: Utilisateur) {
          val stationUtilisateur = utilisateur.Station
          val userID = utilisateur.Code_Ut
          viewModelScope.launch {
              proCaisseLocalDb.ticket.getNewCode(prefix).collect {
                  bonTransfertGeneratedNum =prefix + it
              }
          }
      }*/

    fun deleteBLNotSync(
        articleMapByBarCode: Map<String, Article>,
        clientList: List<Client>,
        listStationStockArticl: Map<String, StationStockArticle>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit
    ) {
        viewModelScope.launch(dispatcher) {
            val ticket = selectedBonLivraisonWithFactureAndPayments.ticket ?: return@launch

            val codeM = ticket.tIKNumTicketM
            val exercice = ticket.tIKExerc
            val numTicket = ticket.tIKNumTicket
            val idCaisse = ticket.tIKIdSCaisse

            val listLgBl = selectedListLgBonLivraison

            for (lgBl in listLgBl) {
                val article =
                    articleMapByBarCode[lgBl.lTCodArt]?: articleMapByBarCode[lgBl.codeBarFils] ?: return@launch


                val qty = stringToDouble(lgBl.lTQte)
                ArticleOpeartions.updateArticleQty(
                    quantity = qty,
                    article = article,
                    updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                        updateArtQteStock(
                            newQteAllStations,
                            newQteStation,
                            codeArticle
                        )
                    }
                )
                //  val stationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == ticket.tIKStation && it.sARTCodeArt == lgBl.lTCodArt }
                val stationStockArticle = listStationStockArticl[lgBl.lTCodArt + ticket.tIKStation]?: StationStockArticle()

                getStationStockArticle(
                    codeStation = ticket.tIKStation,
                    codeArticle = lgBl.lTCodArt,
                    stationStockArticle = stationStockArticle,
                    qty = qty,
                    updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                        updateQtePerStation(
                            newQteStation,
                            newSartQteDeclare,
                            codeArticle,
                            codeStation
                        )
                    }
                )


            }

            val client = clientList.firstOrNull { it.cLICode == ticket.tIKCodClt }

            proCaisseLocalDb.bonLivraison.deleteByCodeM(code = codeM, exercice = exercice)
            proCaisseLocalDb.ligneBonLivraison.deleteByNumTicket(
                numTicket = numTicket,
                ltExercice = exercice
            )
            proCaisseLocalDb.chequeCaisse.deleteByCodeM(codeM = codeM, exercice = exercice)
            proCaisseLocalDb.ticketResto.deleteByCodeM(codeM = codeM, exercice = exercice)
            proCaisseLocalDb.reglementCaisse.deleteByCode(
                code = codeM,
                exercice = exercice,
                idCaisse = idCaisse
            )

            if (client == null) return@launch
            updateClientSold(
                client = client,
                sold = (stringToDouble(client.solde) + stringToDouble(ticket.tIKMtTTC)).toString()
            )

            //  delay(500)
        }
    }




    fun saveBonTransfert(ticket: Ticket) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.bonLivraison.upsert(ticket)
        }
    }

    fun saveListLigneBnTransfert(lgticket: List<LigneTicket>) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonLivraison.upsertAll(lgticket)
        }
    }

    fun saveLigneBnTransfert(lgticket: LigneTicket) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.ligneBonLivraison.upsert(lgticket)
        }
    }

    fun onEvent(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (bonTransfertListstate.listOrder::class == event.listOrder::class &&
                    bonTransfertListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                bonTransfertListstate = bonTransfertListstate.copy(
                    listOrder = event.listOrder
                )
                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()

            is ListEvent.ListSearch -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    search = event.listSearch
                )

                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.FirstCustomFilter -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    filterByTicketEtat = event.firstFilter
                )

                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.SecondCustomFilter -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    filterByTicketSource = event.secondFiter
                )

                filterBonTransfert(bonTransfertListstate)
            }

            is ListEvent.ThirdCustomFilter -> {
                bonTransfertListstate = bonTransfertListstate.copy(
                    filterBySessionCaisse = event.thirdFilter
                )

                filterBonTransfert(bonTransfertListstate)
            }

        }

    }

    var getBonTransfertJob: Job = Job()

    fun filterBonTransfert(bonTransfertListState: BonLivraisonFilterListState) {
        val searchedText = searchTextState.text
        val searchValue = bonTransfertListState.search
        val filterByTicketEtat = bonTransfertListState.filterByTicketEtat
        val filterByTicketSource = bonTransfertListState.filterByTicketSource
        val filterBySessionCaisse = bonTransfertListState.filterBySessionCaisse

        getBonTransfertJob.cancel()
        Log.d("ewvwcxwxcwgdg", "filterByTicketEtat "+ filterByTicketEtat)
        Log.d("ewvwcxwxcwgdg", "filterByTicketSource "+ filterByTicketSource)
        Log.d("ewvwcxwxcwgdg", "filterBySessionCaisse "+ filterBySessionCaisse)
        getBonTransfertJob = viewModelScope.launch {
            val isAsc = if (bonTransfertListState.listOrder.orderType is OrderType.Ascending) 1 else 2
            val sortBy = when (bonTransfertListState.listOrder) {
                is ListOrder.Title -> "TIK_NumTicket"
                is ListOrder.Date -> "DDmM"
                is ListOrder.Third -> "TIK_MtTTC"
            }

            val dataFlow = when {
                searchedText.isEmpty() -> proCaisseLocalDb.bonLivraison.getAllFiltred(
                    isAsc = isAsc,
                    sortBy = sortBy,
                    filterByTicketEtat = filterByTicketEtat,
                    filterByTicketSource = filterByTicketSource,
                    //   filterBySessionCaisse = filterBySessionCaisse
                )
                searchValue is ListSearch.FirstSearch -> proCaisseLocalDb.bonLivraison.filterByNumBL(
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    filterByTicketEtat = filterByTicketEtat,
                    filterByTicketSource = filterByTicketSource,
                    filterBySessionCaisse = filterBySessionCaisse
                )
                searchValue is ListSearch.SecondSearch -> proCaisseLocalDb.bonLivraison.filterByClient(
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    filterByTicketEtat = filterByTicketEtat,
                    filterByTicketSource = filterByTicketSource,
                    filterBySessionCaisse = filterBySessionCaisse
                )
                searchValue is ListSearch.ThirdSearch -> proCaisseLocalDb.bonLivraison.filterByTicketNum(
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    filterByTicketEtat = filterByTicketEtat,
                    filterByTicketSource = filterByTicketSource,
                    filterBySessionCaisse = filterBySessionCaisse
                )
                else -> return@launch
            }

            dataFlow.collect {
                setBonTransfertList(it)
            }
        }
    }

    var ticketWithFactureAndPayments = mutableStateListOf<TicketWithFactureAndPayments>()
        private set

    var ligneBonLivraison = mutableStateListOf<LigneTicket>()
        private set

    private fun setBonTransfertList(ticket: Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>) {
        Log.d("ewvwcxwxcwgdg", "ticket "+ ticket.size)
        bonTransfertListstate = bonTransfertListstate.copy(lists = ticket)

        ticketWithFactureAndPayments.clear()
        ligneBonLivraison.clear()

        ticket.forEach { (key, value) ->
            ticketWithFactureAndPayments.add(key)
            ligneBonLivraison.addAll(value.mapNotNull { it.ligneTicket })
        }
        getBonTransfertJob.cancel()

    }

    fun updateClientSold(client: Client, sold: String) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.clients.updateSoldClient(codeClt = client.cLICode, soldClient = sold)
        }
    }


    fun saveNewBonLivraison(
        currentDate: String,
        haveTimber: Boolean,
        tIKMtTTC: String,
        cashValue: String = "",
        montantTotalCheques: String = "",
        montantTotalTicketResto: String = "",
        listStationStockArticl: Map<String, StationStockArticle>,
        sessionCaisse: SessionCaisse,
        customAdress: String,
        totalDiscount: String,
        exerciceCode: String,
        utilisateur: Utilisateur,
        client: Client,
        saveBonTransfert: (ticket: Ticket) -> Unit,
        bonLivraisonCodeM: String,
        selectedArticleList: List<SelectedArticle>,
        listActifTimber: List<Timbre>,
        nbrLinTIK: Int,
        location: LocationState,
        maxNumTicket: String,
        setMaxNumTicket: () -> Unit,
        typePayment: String,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit,
    ) {

        val codeUt = utilisateur.codeUt
        val station = utilisateur.Station

        var latitude = "0"
        var longitude = "0"
        if (location.latitude != null && location.longitude != null) {
            latitude = location.latitude.toString()
            longitude = location.longitude.toString()
        }

        // val mntHT = Calculations.totalPriceHT(listArt = selectedArticleList)
        val mntHT = selectedArticleList.sumOf { stringToDouble(it.lTMtBrutHT) }

        val mntTva = selectedArticleList.sumOf { stringToDouble(it.mntTva) }
        val mntRemise = selectedArticleList.sumOf { stringToDouble(it.mntDiscount) }


        var mntTTC = if ((client.cltMntRevImp ?: 0.0) > 0.0)
            stringToDouble(tIKMtTTC) * (1 + (client.cltMntRevImp ?: 0.0) / 100)
        else stringToDouble(tIKMtTTC)



        val ticket = Ticket(
            tIKNumTicket = maxNumTicket,
            tIKNumTicketM = bonLivraisonCodeM,
            dDmM = currentDate,
            exportM = "",
            tIKAnnuler = "",
            tIKCODECOMMERCIAL = "",
            tIKCodClt = client.cLICode,
            tIKDDm = "empty",//getCurrentDateTime(), //TODO IF update getCurrentDateTime() else empty,
            tIKDateHeureTicket = currentDate,
            tIKDateLivraison = currentDate,
            tIKDateMariage = currentDate,
            tIKEtat = if (typePayment == TicketState.PARTIAL_PAYMENT.getValue()) TicketState.CREDIT.getValue() else typePayment,
            tIKEtatCom = "",
            tIKExerc = exerciceCode,
            tIKIdCarnet = sessionCaisse.sCIdCarnet ?: "N/A",
            tIKIdSCaisse = sessionCaisse.sCIdSCaisse,
            tIKLatitudeEv = latitude, //TODO GET CURRENT LOCATION + HANDLE PERMISSION
            tIKLatitudeSv = latitude,  //TODO GET CURRENT LOCATION + HANDLE PERMISSION
            tIKLongitudeSv = longitude,  //TODO GET CURRENT LOCATION + HANDLE PERMISSION
            tIKLongitudeEv = longitude,  //TODO GET CURRENT LOCATION + HANDLE PERMISSION
            tIKMtCarteBanq = "",
            tIKMtCheque = montantTotalCheques, //TODO PUT MN CHEQUE
            tIKMtEspece = cashValue, // TODO PUT MNT ESPECE
            tIKEnvWebServ = "0",
            tIKMtHT = mntHT.toString(),
            tIKMtRemise = mntRemise.toString(),
            tIKMtTTC = mntTTC.toString(),
            tIKMtTVA = mntTva.toString(),
            tIKMtrecue = montantTotalTicketResto, //Mnt Traite, ticket resto
            tIKMtrendue = "",
            tIKNomClient = client.cLINomPren,
            tIKNumCarte = null, // TODO THEY SEND IT NULL
            tIKNumCheque = "",
            tIKNumContrat = "",
            tIKEmplacementMariage = customAdress,
            tIKNumeroBL = "",
            tIKRegler = "",
            tIKSource = Globals.SOURCE,
            tIKStation = client.cLIStation,
            tIKTauxRemise = totalDiscount,
            tIKTimbre = if (haveTimber) listActifTimber.map { it.tIMBCode }.first().toString() else "0.0", // TODO SET TIMBER ACCORDING TO AUTHS
            tIKUser = codeUt,
            nbrLinTIK = nbrLinTIK.toString(),
            mntRevImp = stringToDouble(tIKMtTTC) * ((client.cltMntRevImp ?: 0.0) / 100)
        )

        ticket.status = ItemStatus.INSERTED.status
        ticket.isSync = false


        setMaxNumTicket()

        saveBonTransfert(ticket)

        saveListLgBonLivraison(
            bonCommandeCodeM = bonLivraisonCodeM,
            sCIdCarnet = sessionCaisse.sCIdCarnet ?: "N/A",
            exerciceCode = exerciceCode,
            cLIStation = client.cLIStation,
            utilisateur = utilisateur,
            listStationStockArticl = listStationStockArticl,
            selectedArticleList = selectedArticleList,
            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                updateQtePerStation(newQteStation, newSartQteDeclare, codeArticle, codeStation)
            },
            updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                updateArtQteStock(newQteAllStations, newQteStation, codeArticle)
            },
            maxNumTicket = maxNumTicket,
            currentDate = currentDate

        )

        updateClientSold(
            client = client,
            sold = newSold(
                typePayment = if (typePayment == TicketState.PARTIAL_PAYMENT.getValue()) TicketState.CREDIT.getValue() else typePayment,
                paymentModeList = paymentModeList,
                client = client,
                ticket = ticket
            ).toString()
        )


    }


    fun newSold(
        typePayment: String,
        paymentModeList: List<String>,
        client: Client,
        ticket: Ticket
    ): Double = if (typePayment != paymentModeList[1]) {
        stringToDouble(client.solde) - stringToDouble(ticket.tIKMtTTC)
    } else {
        stringToDouble(client.solde) - (stringToDouble(ticket.tIKMtTTC) -
                (stringToDouble(ticket.tIKMtCheque) + stringToDouble(ticket.tIKMtEspece))

                // TODO   + stringToDouble(ticket.) rEGCMntEspeceRecue)
                )
    }

    private fun saveListLgBonLivraison(
        bonCommandeCodeM: String,
        sCIdCarnet: String,
        exerciceCode: String,
        cLIStation: String,
        utilisateur: Utilisateur,
        listStationStockArticl: Map<String, StationStockArticle>,
        selectedArticleList: List<SelectedArticle>,
        updateQtePerStation: (newQteStation: String, newSartQteDeclare: String, codeArticle: String, codeStation: String) -> Unit,
        updateArtQteStock: (newQteAllStations: String, newQteStation: String, codeArticle: String) -> Unit,
        maxNumTicket: String,
        currentDate: String
    ) {



        for (i in selectedArticleList.indices) {
            val articl = selectedArticleList[i].article
            val qty = stringToDouble(selectedArticleList[i].quantity)

            //     val lTTVA = if(articl.aRTTVA == 0.0)  else articl.aRTTVA.toString()
            val lTTVA =  if(articl.aRTTVA ==0.0) ((stringToDouble(selectedArticleList[i].lTMtTTC) - stringToDouble(selectedArticleList[i].lTMtNetHT)) / stringToDouble(selectedArticleList[i].lTMtNetHT)) * 100 else articl.aRTTVA


            val ligneBonTicket = LigneTicket(
                lTNumTicket = maxNumTicket,
                lTNumTicketM = bonCommandeCodeM /*+"_${i+1}"*/,
                codeBarFils = articl.aRTCodeBar, //implemented like this in procaisse java version
                dDmM = currentDate,
                exportM = "",
                lTBonEntree = "",
                lTBonEntreeExerc = exerciceCode,
                lTCodArt = articl.aRTCode,
                lTDDm = currentDate,
                lTExerc = exerciceCode,
                lTExercFacture = "",
                lTExport = "",
                lTIdCarnet = sCIdCarnet,
                lTMtHT = selectedArticleList[i].lTMtBrutHT,
                lTMtTTC = selectedArticleList[i].lTMtTTC,
                lTNumOrdre = (i + 1).toString(),
                lTPACHAT = selectedArticleList[i].article.aRTPrixUnitaireHT,
                lTPACHATTC = "",
                lTPrixEncaisse = selectedArticleList[i].prixCaisse,
                lTPrixVente = selectedArticleList[i].prixVente,
                lTQte = selectedArticleList[i].quantity,
                lTRemise = selectedArticleList[i].mntDiscount.toString(),
                lTTauxRemise = selectedArticleList[i].discount.ifEmpty { "0.0" },
                lTStation = cLIStation,
                //   lTTVA = (stringToDouble(selectedArticleList[i].lTMtTTC) -  stringToDouble(selectedArticleList[i].lTMtHT)).toString(),
                lTTVA = lTTVA.toString(),

                lTUnite = articl.uNITEARTICLECodeUnite ?: "",
                lTUser = utilisateur.codeUt,
                numSerie = "",
                observationGarantie = "",
                prestataire = "",
                authorizedDiscount = true, // THIS LINE IS ALWAYS TRUE if false then can't save ticket (see prev app implementation)

                lTAnnuler = "0",
                lTCommande = "false",
                lTPachatPrixFacturee = "0.0",
                lTPachatRes = "0.0",
                lTQteFacturee = "0.0",
                lTQtePiece = articl.uNITEARTICLEQtePiece.toString(),

                lTTarif = articl.aRTPrixUnitaireHT

            )

            // val stationStockArticle = listStationStockArticl.firstOrNull { it.sARTCodeSatation == utilisateur.Station && it.sARTCodeArt == articl.aRTCode } ?: StationStockArticle()
            val stationStockArticle = listStationStockArticl[articl.aRTCode + utilisateur.Station]?: StationStockArticle()


            ligneBonTicket.isSync = false
            ligneBonTicket.status = ItemStatus.INSERTED.status

            saveLigneBnTransfert(lgticket = ligneBonTicket)



            ArticleOpeartions.updateStationArticleQte(
                opeartion = Globals.MINUS,
                quatity = qty,
                aRTCode = articl.aRTCode,
                codeStation = cLIStation,
                stationStockArticle = stationStockArticle,
                updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation ->
                    updateQtePerStation(
                        newQteStation,
                        newSartQteDeclare,
                        codeArticle,
                        codeStation
                    )
                }
            )

            ArticleOpeartions.updateArticleQty(
                operation = Globals.MINUS,
                quantity = qty,
                article = articl,
                updateArtQteStock = { newQteAllStations, newQteStation, codeArticle ->
                    updateArtQteStock(
                        newQteAllStations,
                        newQteStation,
                        codeArticle
                    )
                }
            )
        }


    }
    var selectedArticleList = mutableStateListOf<SelectedArticle>()
        private set

    var bonLivraison = mutableStateListOf<Ticket>()
        private set

    var ligneBonLivraisonWithArticle = mutableStateListOf<LigneTicketWithArticle>()
        private set

    fun addAllTiketMoney(tickets: MutableList<Ticket>, ligneTicket: List<LigneTicketWithArticle>) {
        selectedArticleList.clear()

        tickets.removeIf { it.tIKNumTicket == "0" }

        var tIKMtTTC = 0.0
        var tIKMtRemise = 0.0
        var tIKMtHT = 0.0
        var tIKMtTVA = 0.0
        var tIKMtEspece = 0.0
        var tIKMtCheque = 0.0
        var tIKMtCarteBanq = 0.0
        var tIKMtrecue = 0.0
        var tIKMtrendue = 0.0
        var tIKMtCredit = 0.0

        tickets.forEach { ticket ->
            if (ticket.tIKNumTicket != "0") {
                if (ticket.tIKEtat == TicketState.CREDIT.getValue()) {
                    tIKMtCredit += stringToDouble(ticket.tIKMtTTC)
                }

                tIKMtTTC += stringToDouble(ticket.tIKMtTTC)
                tIKMtRemise += stringToDouble(ticket.tIKMtRemise)
                tIKMtHT += stringToDouble(ticket.tIKMtHT)
                tIKMtTVA += stringToDouble(ticket.tIKMtTVA)
                tIKMtEspece += stringToDouble(ticket.tIKMtEspece)
                tIKMtCheque += stringToDouble(ticket.tIKMtCheque)
                tIKMtCarteBanq += stringToDouble(ticket.tIKMtCarteBanq)
                tIKMtrecue += stringToDouble(ticket.tIKMtrecue)
                tIKMtrendue += stringToDouble(ticket.tIKMtrendue)
            }
        }

        val groupedLigneTicketList = ligneTicket.groupBy { it.ligneTicket?.lTCodArt to it.ligneTicket?.lTPrixVente to it.ligneTicket?.lTTauxRemise }
        // IMPORTANT : if change groupBy the you have to change lazy column key in RecapBonLivraisonScreen FiveColumnTable
        val finalList = groupedLigneTicketList.values.map { list ->
            val listLigneTicket = list.mapNotNull { it.ligneTicket }
            val listArticle = list.mapNotNull { it.article }

            val aggregatedTicket = listLigneTicket.first().copy(
                lTQte = listLigneTicket.sumOf { stringToDouble(it.lTQte) }.toString(),
                lTMtTTC = listLigneTicket.sumOf { stringToDouble(it.lTMtTTC) }.toString()
            )

            LigneTicketWithArticle(
                ligneTicket = aggregatedTicket,
                article = listArticle.firstOrNull() ?: Article(
                    aRTCode = aggregatedTicket.lTCodArt,
                    aRTCodeBar = aggregatedTicket.codeBarFils
                )
            )
        }.sortedBy { it.ligneTicket?.lTCodArt }

        val ticket = tickets.first().copy(
            tIKMtTTC = tIKMtTTC.toString(),
            tIKMtRemise = tIKMtRemise.toString(),
            tIKMtHT = tIKMtHT.toString(),
            tIKMtTVA = tIKMtTVA.toString(),
            tIKMtEspece = tIKMtEspece.toString(),
            tIKMtCheque = tIKMtCheque.toString(),
            tIKMtCarteBanq = tIKMtCarteBanq.toString(),
            tIKMtrecue = tIKMtrecue.toString(),
            tIKMtrendue = tIKMtrendue.toString(),
            tIKMtCredit = tIKMtCredit,
            tIKNomClient = finalList.size.toString(),
            //   nbreTicketsRecap = tickets.size.toString()
        )
        ticket.nbreTicketsRecap = tickets.size.toString()
        val recapBLMap = mapOf(ticket to finalList)

        recapBLMap.forEach { (key, value) ->
            bonLivraison.add(key)
            ligneBonLivraisonWithArticle.addAll(value)
        }

        finalList.forEach { item ->
            val lgBonLiv = item.ligneTicket
            val article = item.article ?: Article(
                aRTCodeBar = lgBonLiv?.codeBarFils ?: "N/A",
                aRTCode = lgBonLiv?.lTCodArt ?: "N/A",
                aRTDesignation = lgBonLiv?.codeBarFils ?: "N/A",
                aRTTVA = stringToDouble(lgBonLiv?.lTTVA ?: "0.0"),
                pvttc = stringToDouble(lgBonLiv?.lTPrixVente ?: "0")
            )

            val selectedArticle = SelectedArticle(
                article = article,
                quantity = lgBonLiv?.lTQte ?: "0",
                prixCaisse = lgBonLiv?.lTPrixVente ?: "0",
                discount = lgBonLiv?.lTTauxRemise ?: "0",
                mntDiscount = lgBonLiv?.lTRemise ?: "0",
                lTMtTTC = lgBonLiv?.lTMtTTC ?: "0",
                lTMtBrutHT = lgBonLiv?.lTMtHT ?: "0",
                prixVente = lgBonLiv?.lTPrixVente ?: "0",
                tva = Tva(),
                mntTva = ""
            )
            selectedArticleList.add(selectedArticle)


        }
        //  veryLongLog("selectedArticleListdd:", selectedArticleList.toString())

    }

//    var selectedArticleList = mutableStateListOf<SelectedArticle>()
//        private set
//
//
//    var bonLivraison = mutableStateListOf<Ticket>()
//        private set
//
//    var ligneBonLivraisonWithArticle = mutableStateListOf<LigneTicketWithArticle>()
//        private set
//
//
//
//    fun addAllTiketMoney(tickets: MutableList<Ticket>, ligneTicket: List<LigneTicketWithArticle>) {
//        selectedArticleList.clear()
//        val recapBLMap: Map<Ticket, List<LigneTicketWithArticle>>
//
//
//        //  val amount = accounts.map { account -> account.balance.toFloat() }.sum()
//        val iterator = tickets.iterator()
//        while (iterator.hasNext()) {
//            val ticket = iterator.next()
//            if (ticket.tIKNumTicket == "0") {
//                iterator.remove()
//            }
//        }
//
//        var tIKMtTTC = 0.0
//        var tIKMtRemise = 0.0
//        var tIKMtHT = 0.0
//        var tIKMtTVA = 0.0
//        var tIKMtEspece = 0.0
//        var tIKMtCheque = 0.0
//        var tIKMtCarteBanq = 0.0
//        var tIKMtrecue = 0.0
//        var tIKMtrendue = 0.0
//        var tIKMtCredit = 0.0
//
//        var ticket = tickets.first()
//
//        for (i in tickets.indices) {
//            if (tickets[i].tIKNumTicket != "0") {
//                if (tickets[i].tIKEtat == TicketState.CREDIT.getValue()) {
//                    tIKMtCredit += stringToDouble(tickets[i].tIKMtTTC)
//                }
//
//                tIKMtTTC += stringToDouble(tickets[i].tIKMtTTC)
//                tIKMtRemise += stringToDouble(tickets[i].tIKMtRemise)
//                tIKMtHT += stringToDouble(tickets[i].tIKMtHT)
//                tIKMtTVA += stringToDouble(tickets[i].tIKMtTVA)
//                tIKMtEspece += stringToDouble(tickets[i].tIKMtEspece)
//                tIKMtCheque += stringToDouble(tickets[i].tIKMtCheque)
//                tIKMtCarteBanq += stringToDouble(tickets[i].tIKMtCarteBanq)
//                tIKMtrecue += stringToDouble(tickets[i].tIKMtrecue)
//                tIKMtrendue += stringToDouble(tickets[i].tIKMtrendue)
//
//            } else Log.d("kkklld", "bhhhh")
//        }
//
//
//        val sameCodeBareList: MutableList<LigneTicket> = ArrayList()
//
//        val finalList: MutableList<LigneTicketWithArticle> = ArrayList()
//
//        val groupedLigneTicketList = ligneTicket.groupBy { it.ligneTicket?.codeBarFils to it.ligneTicket?.lTPrixVente to it.ligneTicket?.lTTauxRemise }
//
//        for (j in groupedLigneTicketList.keys.indices) {
//            val listLigneTicket: List<LigneTicket?> = groupedLigneTicketList.values.stream().skip(j.toLong()).findFirst().get().map { it.ligneTicket }
//
//            val listarticle =
//                groupedLigneTicketList.values.stream().skip(j.toLong()).findFirst().get().map {
//                    it.article ?: Article(
//                        aRTCode = it.ligneTicket?.lTCodArt?: j.toString(),
//                        aRTCodeBar = it.ligneTicket?.codeBarFils?: j.toString()
//                    )
//                }
//
//            var mtttc = 0.0
//            var qt = 0.0
//
//            if (listLigneTicket.size > 1) {
//                var lgTicket = listLigneTicket[0]
//
//
//                val article = listarticle.firstOrNull { it.aRTCodeBar == lgTicket?.codeBarFils || it.aRTCode == lgTicket?.lTCodArt }
//
//
//                for (index in listLigneTicket) {
//                    mtttc += stringToDouble(index?.lTMtTTC)
//                    qt += stringToDouble(index?.lTQte)
//                }
//                lgTicket = lgTicket?.copy(lTQte = qt.toString())
//                lgTicket = lgTicket?.copy(lTMtTTC = mtttc.toString())
//                finalList.add(
//                    LigneTicketWithArticle(
//                        ligneTicket = lgTicket,
//                        article = article
//                    )
//                )
//
//            } else finalList.add(
//                LigneTicketWithArticle(
//                    ligneTicket = listLigneTicket[0],
//                    article = listarticle[0]
//                )
//            )
//        }
//
//
//        ticket = ticket.copy(
//            tIKMtTTC = tIKMtTTC.toString(),
//            tIKMtRemise = tIKMtRemise.toString(),
//            tIKMtHT = tIKMtHT.toString(),
//            tIKMtTVA = tIKMtTVA.toString(),
//            tIKMtEspece = tIKMtEspece.toString(),
//            tIKMtCheque = tIKMtCheque.toString(),
//            tIKMtCarteBanq = tIKMtCarteBanq.toString(),
//            tIKMtrecue = tIKMtrecue.toString(),
//            tIKMtrendue = tIKMtrendue.toString(),
//            tIKMtCredit = tIKMtCredit,
//            // nbreTicketsRecap = tickets.size.toString(),
//            tIKNomClient = sameCodeBareList.size.toString(),
//
//            )
//
//        ticket.nbreTicketsRecap = tickets.size.toString()
//
//        recapBLMap = mapOf(ticket to finalList)
//
//
//
//
//
//        for (i in finalList.indices) {
//            //  val tva = mainViewModel.tvaList.firstOrNull { it.tVACode == ligneBonLivraison[i].lTTVA}
//
//
//            val lgBonLiv = finalList[i].ligneTicket
//
//            val article = finalList[i].article?:
//            Article(
//                aRTCodeBar = lgBonLiv?.codeBarFils?:"N/A $i",
//                aRTCode = lgBonLiv?.lTCodArt?:"N/A $i",
//                aRTDesignation = lgBonLiv?.codeBarFils?:"N/A $i",
//                aRTTVA = stringToDouble(lgBonLiv?.lTTVA?:"0.0"),
//                pvttc = stringToDouble(lgBonLiv?.lTPrixVente?:"0")
//            )
//            val selectedArticle = SelectedArticle(
//                article = article,
//                //  tva = tva,
//                quantity=  lgBonLiv!!.lTQte,
//                prixCaisse = lgBonLiv.lTPrixVente,
//                discount = lgBonLiv.lTTauxRemise,
//                mntDiscount = lgBonLiv.lTRemise,
//                lTMtTTC = lgBonLiv.lTMtTTC,
//                lTMtBrutHT = lgBonLiv.lTMtHT,
//                prixVente = lgBonLiv.lTPrixVente,
//                tva = Tva(),
//                mntTva = ""
//            )
//            selectedArticleList.add(selectedArticle)
//         //   selectArtMobilityVM.setConsultationSelectedArticleMobilityList(selectedArticle = selectedArticle)
//        }
//
//
//
//
//
//         recapBLMap.forEach { (key, value) ->
//            run {
//                bonLivraison.add(key)
//                ligneBonLivraisonWithArticle.addAll(value)
//            }
//        }
//    }


    fun haveTimbre(client: Client, autoBLTimbreEnabled: Boolean): Boolean {
        return /* ticket.setTimbre(timbre.tIMBCode.toDouble())
            ticket.settIKMtTTC(
                Utils.round(
                    articleListDialog.getList()
                        .getAmountWithDiscount() + timbre.tIMBValue!!.toDouble(), 3
                )
            )*/autoBLTimbreEnabled && client.cLITimbre == "1"//ticket.setTimbre(0.0)
    }

    fun isPassagerClient(client: Client): Boolean = client.cLIType == Globals.PASSAGER



}