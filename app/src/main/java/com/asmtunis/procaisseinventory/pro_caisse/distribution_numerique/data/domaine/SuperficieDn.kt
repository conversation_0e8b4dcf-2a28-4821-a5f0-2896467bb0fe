package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.SUPERFICIE_TABLE)
@Serializable
data class SuperficieDn(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "CodeSuperf")
    @SerialName("CodeSuperf")
    var codeSuperf: String = "",

    @ColumnInfo(name = "TypeSuperf")
    @SerialName("TypeSuperf")
    var typeSuperf: String = "",

    @ColumnInfo(name = "NoteSuperf") //
    @SerialName("NoteSuperf")
    var noteSuperf: String? = "",

    @ColumnInfo(name = "EtatSuperf")
    @SerialName("EtatSuperf")
    var etatSuperf : Int? = 0
)
