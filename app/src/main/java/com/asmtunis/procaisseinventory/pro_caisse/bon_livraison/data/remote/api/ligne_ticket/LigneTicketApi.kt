package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.remote.api.ligne_ticket

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow


interface LigneTicketApi {
        suspend fun getLigneTicketByTickets(baseConfig: String): Flow<DataResult<List<List<LigneTicket>>>> = flow {}
        /**
         * Not used
         */
        /*
          @Headers("User-Agent: android-api-client")
    @POST("getLigneTicketByCarnetId")
    Call<List<LigneTicket>> getLignesTicket(@Body GenericObject genericObject);


    @Headers("User-Agent: android-api-client")
    @POST("getLigneTicketByTicket")
    Call<List<LigneTicket>> getLigneTicketByTicket(@Body GenericObject genericObject);

         */
}