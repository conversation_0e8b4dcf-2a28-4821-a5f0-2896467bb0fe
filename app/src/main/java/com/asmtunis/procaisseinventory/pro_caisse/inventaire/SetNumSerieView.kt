package com.asmtunis.procaisseinventory.pro_caisse.inventaire

import android.Manifest
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DriveFileRenameOutline
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.selectedArticle
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.NOT_FOUND_LOCALLY
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.CODE_VERIFICATION_INVENTAIRE_EXIST
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControlInventaireResponse
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.edit_text.EditTextField

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun SetNumSerieView(
    articleMapByBarCode: Map<String, Article>,
    marqueList: List<Marque>,
    haveCamera: Boolean,
    selectedPatrimoine: SelectedPatrimoine,
    backUpSelectedPatrimoine: SelectedPatrimoine = SelectedPatrimoine(),
    selectedPatrimoineList: List<SelectedPatrimoine>,
    onNumSerieChange: (String) -> Unit,
    onNoteChange: (String) -> Unit,
    dropDownMenuComposable: @Composable () -> Unit = { },
    showDropDownMenuComposable: Boolean,
    onAffect: () -> Unit = {},
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    onAddInvPat: () -> Unit,
    onBareCodeScan: () -> Unit,
    barCodeInfo: BareCode,
    patrimoineVerificationState: RemoteResponseState<ControlInventaireResponse>,
) {
    val note = selectedPatrimoine.note
    val article = articleMapByBarCode[selectedPatrimoine.articleCode]
    val marque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque()
    val numSerie = selectedPatrimoine.numSerie
    val backUpNumSerie = backUpSelectedPatrimoine.numSerie
    val canVerifyPat = numSerie.isNotEmpty() && if (showDropDownMenuComposable) { marque != Marque() } else { true }

    val density = LocalDensity.current
    val controlInventaireResponse = patrimoineVerificationState.data

    val duplicatedPatrimoine = selectedPatrimoineList.firstOrNull { it.numSerie == numSerie }
    val duplicatedPatrimoineArticle = articleMapByBarCode[duplicatedPatrimoine?.articleCode]

    LaunchedEffect(key1 = barCodeInfo) {
        if (barCodeInfo.value == "") return@LaunchedEffect
        onNumSerieChange(barCodeInfo.value)
        // onConfirm()
    }


    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.


            onDismiss()
        },
        properties =
            DialogProperties(
                usePlatformDefaultWidth = true,
            ),
        content = {
            Card(
                // modifier = Modifier.heightIn(min = 250.dp, max = 350.dp),
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp),
            ) {
                if (patrimoineVerificationState.loading) {
                    LottieAnim(lotti = R.raw.loading, size = 100.dp)
                } else {
                    Column(
                        modifier = Modifier.verticalScroll(rememberScrollState()).padding(start = 12.dp, end = 12.dp),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,

                        ) {
                        Spacer(modifier = Modifier.height(24.dp))
                        // Handle automatic actions when response code is 10200
                        LaunchedEffect(key1 = controlInventaireResponse?.code) {
                            if (controlInventaireResponse?.code == "10200") {
                                Log.d("eeedddddcxxcxc", "onAddInvPat 10200")
                                onAddInvPat()
                                onDismiss()
                            }
                        }


                        Text(
                            modifier = Modifier.fillMaxWidth(),
                            text = article?.aRTDesignation?: selectedPatrimoine.articleCode,
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))

                        dropDownMenuComposable()
                        Spacer(modifier = Modifier.height(9.dp))
                        EditTextField(
                            modifier = Modifier.fillMaxWidth(),
                            requestFocus = true,
                            showTrailingIcon = true,
                            text = numSerie,
                            errorValue = patrimoineVerificationState.error,
                            label = stringResource(id = R.string.serial_number),
                            onValueChange = {
                                onNumSerieChange(it.removePrefix(" "))
                            },
                            leadingIcon = Icons.Default.DriveFileRenameOutline,
                            keyboardType = KeyboardType.Password,
                            imeAction = ImeAction.Next,
                        )

                        AnimatedVisibility(
                            visible = duplicatedPatrimoine != null && backUpSelectedPatrimoine == SelectedPatrimoine(),
                            enter = slideInVertically {
                                with(density) { 40.dp.roundToPx() }
                            } + fadeIn(),
                            exit = fadeOut(
                                animationSpec = keyframes {
                                    this.durationMillis = 120
                                }
                            )
                        ) {
                            Text(
                                text = stringResource(id = R.string.num_serie_deja_affecter,  duplicatedPatrimoineArticle?.aRTDesignation?: "NULL VALUE !! "),
                                style = MaterialTheme.typography.labelLarge,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }

                        AnimatedVisibility(
                            visible = backUpSelectedPatrimoine != SelectedPatrimoine() && backUpNumSerie != numSerie,
                            enter = slideInVertically {
                                with(density) { 40.dp.roundToPx() }
                            } + fadeIn(),
                            exit = fadeOut(
                                animationSpec = keyframes {
                                    this.durationMillis = 120
                                }
                            )
                        ) {
                            Text(
                                text = stringResource(id = R.string.remplacer_num_serie, backUpNumSerie),
                                style = MaterialTheme.typography.labelLarge,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }

                        Spacer(modifier = Modifier.height(9.dp))

                        EditTextField(
                            modifier = Modifier.fillMaxWidth(),
                            text = note,
                            showTrailingIcon = true,
                            errorValue = null,
                            label = stringResource(id = R.string.note_field),
                            onValueChange = {
                                onNoteChange(it)
                            },
                            leadingIcon = Icons.Default.DriveFileRenameOutline,
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Done,
                        )

                        if (patrimoineVerificationState.error == NOT_FOUND_LOCALLY &&
                            patrimoineVerificationState.message == TypePatrimoine.INVENTAIRE.typePat
                        ) {
                            Spacer(modifier = Modifier.height(12.dp))
                            Text(
                                text = stringResource(id = R.string.affecter_article_question),
                                fontWeight = MaterialTheme.typography.titleMedium.fontWeight
                            )
                            Text(
                                text = stringResource(id = R.string.nb_inv_will_be_saved),
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.error
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            FilledTonalButton(
                                onClick = {
                                    onAffect()
                                    // onDismiss()
                                },
                                //   shape = MaterialTheme.shapes.medium,
                            ) {
                                Text(text = stringResource(id = R.string.affecter_article))
                            }
                        }

                        Spacer(modifier = Modifier.padding(top = 20.dp))
                        HorizontalDivider()
                        Spacer(modifier = Modifier.padding(top = 20.dp))

                        if (patrimoineVerificationState.message == CODE_VERIFICATION_INVENTAIRE_EXIST) {
                            Text(
                                text = stringResource(id = R.string.voulez_vous_confirmer),
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.error
                            )
                            Spacer(modifier = Modifier.padding(top = 20.dp))
                        }
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceAround,
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            OutlinedButton(
                                enabled = true,
                                onClick = {
                                    onDismiss()
                                },
                            ) {
                                Text(
                                    text = stringResource(id = R.string.cancel),
                                    textAlign = TextAlign.Center,
                                )
                            }
                            Button(
                                enabled = canVerifyPat,
                                onClick = {

                                    if (patrimoineVerificationState.message == CODE_VERIFICATION_INVENTAIRE_EXIST) {
                                        onAddInvPat()
                                        onDismiss()
                                    } else
                                        onConfirm()
                                },
                            ) {
                                Text(
                                    text = stringResource(id = R.string.confirm),
                                    textAlign = TextAlign.Center,
                                )
                            }
                            if(haveCamera) {
                                AskPermission(
                                    permission = listOf(Manifest.permission.CAMERA,),
                                    permissionNotAvailableContent = { permissionState ->
                                        /*  val textToShow = if (permissionState.shouldShowRationale) {
                                              "To accessee cameara Please grant the permission."
                                          } else {
                                              "Camera not available"
                                          }
                                          Text(textToShow)*/
                                        IconButton(
                                            enabled = true,
                                            onClick = {
                                                permissionState.launchMultiplePermissionRequest()
                                            },
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.QrCodeScanner,
                                                contentDescription =
                                                    stringResource(
                                                        id = R.string.icn_search_back_content_description,
                                                    ),
                                            )
                                        }
                                    },
                                    content = {
                                        IconButton(
                                            enabled = true,
                                            onClick = onBareCodeScan,
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.QrCodeScanner,
                                                contentDescription =
                                                    stringResource(
                                                        id = R.string.icn_search_back_content_description,
                                                    ),
                                            )
                                        }
                                    },
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(18.dp))
                    }
                }
            }
        },
    )
}

fun updateInvPatQty(
    invPatByNumSerie: Map<BonCommande, List<LigneBonCommande>> = emptyMap(),
    articleCode: String,
    imageList: List<ImagePieceJoint>,
    numeSerie: String,
    selectedPatrimoineList: List<SelectedPatrimoine>,
    patrimoineVerificationState: RemoteResponseState<ControlInventaireResponse>,
    addItemToSelectedPatrimoineList: (selectedPatrimoine: SelectedPatrimoine) -> Unit,
    operation: String = Globals.ADD,
    marque: Marque = Marque(),
    note: String,
) {
    val currentSelectedArticle =
        selectedArticle(
            listSelectedPatrimoine = selectedPatrimoineList,
            articleCode = articleCode,
        )

    val quantity =
        if (operation == Globals.ADD) {
            currentSelectedArticle.quantity + 1.0
        } else {
            currentSelectedArticle.quantity - 1.0
        }

    val selectedArticleMobility =
        SelectedPatrimoine(
            articleCode = articleCode,
            quantity = quantity,
            numSerie = numeSerie,
            extraInfo = if (patrimoineVerificationState.data != null) patrimoineVerificationState.data.message else "",
            marqueCode = marque.mARCode,
            note =
                note.ifEmpty {
                    if (invPatByNumSerie.entries.isEmpty()) {
                        ""
                    } else if (invPatByNumSerie.entries.first().value.isEmpty()) {
                        ""
                    } else {
                        invPatByNumSerie.entries.first().value.first().lgDEVNote ?: ""
                    }
                },
            imageList =
                imageList.filter { it.vcNumSerie == numeSerie }
//                invPatByNumSerie.entries.flatMap {
//                        value ->
//                    value.value.flatMap { it.imageList ?: emptyList() }
//                },
        )

    addItemToSelectedPatrimoineList(selectedArticleMobility)

    // selectPatrimoineVM.resetPatrimoineVerificationState()
}
