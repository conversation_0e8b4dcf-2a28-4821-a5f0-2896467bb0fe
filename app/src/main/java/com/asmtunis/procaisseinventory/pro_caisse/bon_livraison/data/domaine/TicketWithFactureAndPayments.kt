package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Serializable
data class TicketWithFactureAndPayments(
    @Embedded
    @SerialName("ticket")
    var ticket: Ticket? = null,

      @Relation(
          parentColumn = "TIK_NumTicket",
          entityColumn = "LT_NumTicket"
      )
      @SerialName("lignesTicket")
      var ligneTicket: List<LigneTicket>? = null,

    @Relation(
        parentColumn = "TIK_NumTicket",
        entityColumn = "FACT_NumBC"
    )
    @SerialName("facture")
    var facture: Facture? = null,

    @Relation(
        parentColumn = "TIK_NumTicket_M",
        entityColumn = "Reglement_M"
    )
    @SerialName("cheques")
    var cheques: List<ChequeCaisse>? = null,

    @Relation(
        parentColumn = "TIK_NumTicket_M",
        entityColumn = "TRAIT_Num_M"
    )
    @SerialName("traites")
    var traites: List<TraiteCaisse>? = null,


//      @Relation(
//          parentColumn = "TIK_NumTicket_M",
//          entityColumn = "REGC_Code_M"
//      )
    @Relation(
        parentColumn = "TIK_NumTicket",
        entityColumn = "REGC_NumTicket"
    )
      //todo see why TIK_NumTicket != REGC_NumTicket


     @SerialName("reglement")
    var reglement: ReglementCaisse? = ReglementCaisse(),


    @Relation(
        parentColumn = "TIK_NumTicket",
        entityColumn = "REGC_NumTicketPart"
    )
    @SerialName("regTicketPart")
    var regTicketPart: ReglementCaisse? = ReglementCaisse(),


    @Relation(
        parentColumn = "TIK_Timbre",
        entityColumn = "TIMB_Code"
    )
    @Transient
    @SerialName("timbre")
    var timbre: Timbre? = Timbre(),


    @Transient
    @Relation(
        parentColumn = "TIK_CodClt",
        entityColumn = "CLI_Code"
    )
    @SerialName("client")
    var client: Client? = null

    /*@Relation(
        parentColumn = "TIK_NumTicket",
        entityColumn = "REGC_NumTicket"
    )
    @Transient
    @SerialName("reglementPrint")
    var reglementPrint: ReglementCaisse? = ReglementCaisse(),
*/
    )


