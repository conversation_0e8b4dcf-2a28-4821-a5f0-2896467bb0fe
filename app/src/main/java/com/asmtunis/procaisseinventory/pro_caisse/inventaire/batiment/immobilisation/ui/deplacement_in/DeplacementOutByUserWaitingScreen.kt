package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_in

import android.annotation.SuppressLint
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.twotone.ArrowBack
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AjoutDepInBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserWaitingRoute
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui.DeplacementOutByUserScreenContent
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.view_model.DeplacementOutByUserViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel


@SuppressLint("SuspiciousIndentation")
@Composable
fun DeplacementOutByUserWaitingScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    args: DeplacementOutByUserWaitingRoute,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    deplacementOutByUserViewModel: DeplacementOutByUserViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,

    ) {

    val title = args.title
    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)


    val isConnected = networkViewModel.isConnected
    val allBatimentListstate = deplacementOutByUserViewModel.depOutByUserListstate
// navController.navigate(Screen.AjoutDepInBatiment.Route)

        DeplacementOutByUserScreenContent (
            popBackStack = { popBackStack() },
            isConnected = isConnected,
            //showNavIcon = false,
            showOnlyWaiting = true,
            navIcon = Icons.AutoMirrored.TwoTone.ArrowBack,
            title = title,//stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
            immobilisationList = mainViewModel.immobilisationList,
            deplacementOutByUserViewModel = deplacementOutByUserViewModel,
            dataViewModel = dataViewModel,
            drawer = drawer,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            allBatimentListstate = allBatimentListstate,
            onItemClick = { item->
                deplacementOutByUserViewModel.restSelectedDeplacementOutByUser()

                deplacementOutByUserViewModel.onSelectedDeplacementOutByUserChange(
                    allBatimentListstate.lists.filter { it.key == item },
                )

                navigate(AjoutDepInBatimentRoute)
            }
        )

}