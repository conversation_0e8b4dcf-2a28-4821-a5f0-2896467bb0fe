package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.ORDRE_MISSION_TABLE)
@Serializable
data class OrdreMission(
    @PrimaryKey
    @SerialName("ORD_Code")
    @ColumnInfo(name = "ORD_Code")
    
    var oRDCode: String = "",

    @SerialName("ORD_exercice")
    @ColumnInfo(name = "ORD_exercice")
    
    var oRDExercice: String? = null,

    @SerialName("ORD_date")
    @ColumnInfo(name = "ORD_date")
    
    var oRDDate: String = "",

    @SerialName("ORD_vendeur")
    @ColumnInfo(name = "ORD_vendeur")
    
    var oRDVendeur: String? = null,

    @SerialName("ORD_note")
    @ColumnInfo(name = "ORD_note")
    
    var oRDNote: String? = null,

    @SerialName("ORD_etat")
    @ColumnInfo(name = "ORD_etat")
    
    var oRDEtat: String? = null,

    @SerialName("ORD_user")
    @ColumnInfo(name = "ORD_user")
    
    var oRDUser: String? = null,

    @SerialName("ORD_DDM")
    @ColumnInfo(name = "ORD_DDM")
    
    var ordDdm: String? = null,

    @SerialName("ORD_circuit")
    @ColumnInfo(name = "ORD_circuit")
    
    var oRDCircuit: String? = null,

    @SerialName("ORD_station")
    @ColumnInfo(name = "ORD_station")
    
    var oRDStation: String? = null,

    @SerialName("export")
    @ColumnInfo(name = "export")
    
    var export: Int? = null,

    @SerialName("DDm")
    @ColumnInfo(name = "DDm")
    
    var dDm: String? = null,

    @SerialName("exportM")
    @ColumnInfo(name = "exportM")
    
    var exportM: Int? = null,

    @SerialName("DDmM")
    @ColumnInfo(name = "DDmM")
    
    var dDmM: String? = null,

    @SerialName("ORD_stationDepart")
    @ColumnInfo(name = "ORD_stationDepart")
    
    var oRDStationDepart: String? = null,

    @SerialName("ORD_dateDebut")
    @ColumnInfo(name = "ORD_dateDebut")
    
    var oRD_dateDebut: String? = null,

    @SerialName("ORD_dateFin")
    @ColumnInfo(name = "ORD_dateFin")
    
    var oRD_dateFin: String? = null,

    @SerialName("ORD_Session")
    @ColumnInfo(name = "ORD_Session")
    
    var oRD_Session: String? = null,


    @SerialName("ORD_Type")
    @ColumnInfo(name = "ORD_Type")

    var oRD_Type: String? = null,

    @SerialName("ORD_BC_Code")
    @ColumnInfo(name = "ORD_BC_Code")

    var oRD_BC_Code: String? = null,

    @SerialName("ORD_BC_exercice")
    @ColumnInfo(name = "ORD_BC_exercice")

    var oRD_BC_exercice: String? = null,


)

