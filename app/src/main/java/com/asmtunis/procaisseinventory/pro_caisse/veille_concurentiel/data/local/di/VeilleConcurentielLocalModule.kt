package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.dao.AutreVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.repository.AutreVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.repository.AutreVCLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.dao.ImageVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.repository.ImageVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.repository.ImageVCLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.dao.ListeConcurrentVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.repository.ListConcurrentVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.repository.ListConcurrentVCLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.dao.NewProductVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.repository.NewProductVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.repository.NewProductVCLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.dao.PrixVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.repository.PrixVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.repository.PrixVCLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.dao.PromoVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.repository.PromoVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.repository.PromoVCLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.dao.TypeCommunicationVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.repository.TypeCommunicationVCLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.repository.TypeCommunicationVCLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton




    @Module
    @InstallIn(SingletonComponent::class)
    class VeilleConcurentielLocalModule {

        @Provides
        @Singleton
        fun provideListeConcurrentDao(
            listeConcurrentProCaisseDataBase: ProCaisseDataBase
        ) = listeConcurrentProCaisseDataBase.ListeConcurrentVCDAO()

        @Provides
        @Singleton
        @Named("ListeConcurrent")
        fun provideListeConcurrentRepository(
            listeConcurrentVCDAO: ListeConcurrentVCDAO
        ): ListConcurrentVCLocalRepository = ListConcurrentVCLocalRepositoryImpl(
            listeConcurrentVCDAO = listeConcurrentVCDAO)




        @Provides
        @Singleton
        fun provideNewProductDao(
            newProductProCaisseDataBase: ProCaisseDataBase
        ) = newProductProCaisseDataBase.newProductVCDAO()

        @Provides
        @Singleton
        @Named("NewProduct")
        fun provideNewProductRepository(
            newProductVCDAO: NewProductVCDAO
        ): NewProductVCLocalRepository = NewProductVCLocalRepositoryImpl(
            newProductVCDAO = newProductVCDAO)



        @Provides
        @Singleton
        fun providePromoDao(
            promoProCaisseDataBase: ProCaisseDataBase
        ) = promoProCaisseDataBase.PromoVCDAO()

        @Provides
        @Singleton
        @Named("Promo")
        fun providePromoRepository(
            promoVCDAO: PromoVCDAO
        ): PromoVCLocalRepository = PromoVCLocalRepositoryImpl(
            promoVCDAO = promoVCDAO)




        @Provides
        @Singleton
        fun providePrixDao(
            prixProCaisseDataBase: ProCaisseDataBase
        ) = prixProCaisseDataBase.PrixVCDAO()

        @Provides
        @Singleton
        @Named("Prix")
        fun providePrixRepository(
            prixVCDAO: PrixVCDAO
        ): PrixVCLocalRepository = PrixVCLocalRepositoryImpl(
            prixVCDAO = prixVCDAO)




        @Provides
        @Singleton
        fun provideAutreDao(
            autreProCaisseDataBase: ProCaisseDataBase
        ) = autreProCaisseDataBase.AutreVCDAO()

        @Provides
        @Singleton
        @Named("Autre")
        fun provideAutreRepository(
            autreVCDAO: AutreVCDAO
        ): AutreVCLocalRepository = AutreVCLocalRepositoryImpl(
            autreVCDAO = autreVCDAO)



        @Provides
        @Singleton
        fun provideTypeCommunicationDao(
            typeCommunicationProCaisseDataBase: ProCaisseDataBase
        ) = typeCommunicationProCaisseDataBase.TypeCommunicationVCDAO()

        @Provides
        @Singleton
        @Named("TypeCommunication")
        fun provideTypeCommunicationRepository(
            typeCommunicationVCDAO: TypeCommunicationVCDAO
        ): TypeCommunicationVCLocalRepository = TypeCommunicationVCLocalRepositoryImpl(
            typeCommmunicationVCDAO = typeCommunicationVCDAO)






        @Provides
        @Singleton
        fun provideImageDao(
            imageProCaisseDataBase: ProCaisseDataBase
        ) = imageProCaisseDataBase.ImageVCDAO()

        @Provides
        @Singleton
        @Named("Image")
        fun provideImageRepository(
            imageVCDAO: ImageVCDAO
        ): ImageVCLocalRepository = ImageVCLocalRepositoryImpl(
            imageVCDAO = imageVCDAO)

    }