package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import kotlinx.coroutines.flow.Flow

interface ImmobilisationLocalRepository {
    fun getAll(): Flow<List<Immobilisation>?>

    fun getNotSync(): Flow<List<Immobilisation>>
    fun setIsBatimentUser(cliCode: String)
    fun getAllZoneConsomationByImoCB(imoCB: String): Flow<List<Immobilisation>?>
    fun getParent(codeParent: String): Flow<Immobilisation?>
    fun insert(item: Immobilisation)

    fun insertAll(items: List<Immobilisation>)

    fun deleteAll()

    fun setZoneConsomationImmoCB(immoCB: String, cliCode: String, isSynced: Boolean, status: String)

    fun getAllFiltred(isAsc: Int,
                      sortBy: String,
                      byUser: Boolean,
                      tyEmpImNom: String): Flow<List<Immobilisation>>
    fun filterByCltImoCB(filterString: String,
                         sortBy: String,
                         isAsc: Int,
                         byUser: Boolean,
                         tyEmpImNom: String): Flow<List<Immobilisation>>
    fun filterByCLICode(filterString: String,
                        sortBy: String,
                        isAsc: Int,
                        byUser: Boolean,
                        tyEmpImNom: String): Flow<List<Immobilisation>>
    fun filterByName(filterString: String,
                     sortBy: String,
                     isAsc: Int,
                     byUser: Boolean,
                     tyEmpImNom: String): Flow<List<Immobilisation>>

}