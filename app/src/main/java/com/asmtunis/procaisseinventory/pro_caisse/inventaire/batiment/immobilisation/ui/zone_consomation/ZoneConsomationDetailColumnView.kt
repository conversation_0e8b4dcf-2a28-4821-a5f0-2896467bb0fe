package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.zone_consomation

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.CallMade
import androidx.compose.material.icons.automirrored.filled.CallReceived
import androidx.compose.material.icons.automirrored.filled.DriveFileMove
import androidx.compose.material.icons.automirrored.filled.FactCheck
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.twotone.Code
import androidx.compose.material.icons.twotone.SyncDisabled
import androidx.compose.material3.Card
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.navigation.AjoutAffectationBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutInventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.ChooseImmoRoute
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserWaitingRoute
import com.asmtunis.procaisseinventory.core.navigation.InventaireBatimentRoute
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheck
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheckResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.SyncInvBatimentViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.BareCodeScannerIcon
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.CustomCard
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.edit_text.EditTextField

@Composable
fun ZoneConsomationDetailColumnView(
    navigate: (route: Any) -> Unit,
    generateCodeM: (prefix: String, route: Any) -> Unit,
    padding: PaddingValues,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    syncInvBatimentViewModel: SyncInvBatimentViewModel,
    batimentViewModel: InventaireBatimentViewModel,
    barCodeViewModel: BarCodeViewModel,
    showAddCBBatiment: Boolean,
    isDarkTheme: Boolean,
    haveCameraDevice: Boolean,
    affectCodeBareBatimentState: RemoteResponseState<BatimentCheckResponse>,
    zoneConsomationCB: String,
    societe: Immobilisation?,
    siteFinacier: Immobilisation?,
    siteReception: Immobilisation?,
    selectedZoneConsomation: Immobilisation
) {
    val context = LocalContext.current
    val density = LocalDensity.current


    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = isDarkTheme)

    Column(
        modifier = Modifier
            //.fillMaxWidth()
            .fillMaxSize()
            .verticalScroll(state = rememberScrollState(), enabled = true)
            .padding(padding),

        horizontalAlignment = Alignment.Start,
        verticalArrangement = Arrangement.Top
    ) {

        Spacer(modifier = Modifier.height(12.dp))
        Card (
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 12.dp, end = 12.dp)
        ) {
            ItemDetail(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.societe),
                dataText = societe?.cLINomPren ?: "Null",
                icon = Icons.Default.Info
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
        Card (
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 12.dp, end = 12.dp)
        ) {
            ItemDetail(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.site_Financier),
                dataText = siteFinacier?.cLINomPren ?: "Null",
                icon = Icons.Default.Info
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
        Card (
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 12.dp, end = 12.dp)
        ) {
            ItemDetail(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.site_Reception),
                dataText = siteReception?.cLINomPren ?: "Null",
                icon = Icons.Default.Info
            )
        }

        Spacer(modifier = Modifier.height(12.dp))
        Card (
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 12.dp, end = 12.dp)
        ){
            Column(horizontalAlignment = Alignment.CenterHorizontally){
                ItemDetail(
                    modifier = Modifier
                        .fillMaxWidth(0.95f)
                        .padding(top = 12.dp, bottom = 12.dp),
                    title = stringResource(id = R.string.zone_Consomation),
                    dataText = selectedZoneConsomation.cLINomPren,
                    icon = Icons.Default.Info,
                    onClick = {
                        NavigationDrawerViewModel.proCaisseDrawerItems.find { it.id == ID.INVENTAIRE_BATIMENT_ID }
                            ?.let {
                                navigationDrawerViewModel.onSelectedMenuChange(it)
                            }
                        navigate(InventaireBatimentRoute)
                    }
                )


                HorizontalDivider(
                    modifier = Modifier .fillMaxWidth(0.8f),
                    color = MaterialTheme.colorScheme.scrim)


                ItemDetail(
                    modifier = Modifier.fillMaxWidth(0.95f).padding(top = 12.dp, bottom = 12.dp),
                    title = stringResource(id = R.string.bar_code),
                    dataText = /*if(affectCodeBareBatimentState.error != null)*/  selectedZoneConsomation.cliImoCB?: stringResource(id = R.string.ajout_bar_code) /*else stringResource(id = R.string.ajout_bar_code)*/,
                    icon = if(!selectedZoneConsomation.isSync){ Icons.TwoTone.SyncDisabled } else { Icons.TwoTone.Code },
                    tint = if(selectedZoneConsomation.cliImoCB.isNullOrEmpty()) MaterialTheme.colorScheme.error else  LocalContentColor.current,
                    onClick = { batimentViewModel.onshowAddCBBatimentChange(!batimentViewModel.showAddCBBatiment) }
                )



                Spacer(modifier = Modifier.height(12.dp))
            }
        }
        Spacer(modifier = Modifier.height(16.dp))
        AnimatedVisibility(
            visible = showAddCBBatiment,
            enter = slideInVertically {
                with(density) { 40.dp.roundToPx() }
            } + fadeIn(),
            exit = fadeOut(
                animationSpec = keyframes {
                    this.durationMillis = 240
                }
            )
        ) {
            Column {


                Spacer(modifier = Modifier.height(16.dp))
                HorizontalDivider()
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = stringResource(id = R.string.affectation_bar_code),
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.outline,
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.60f),
                        text = zoneConsomationCB,
                        errorValue = batimentViewModel.zoneConsomationErrorCB?.asString(),
                        label = stringResource(R.string.bar_code),
                        onValueChange = {
                            batimentViewModel.onZoneConsomationCBChange(it, "3")
                        },
                        readOnly = false,
                        leadingIcon = Icons.Default.Home,
                        showLeadingIcon = false,
                        showTrailingIcon = true,
                        keyboardType = KeyboardType.Password,
                        imeAction = ImeAction.Done,
                        onKeyboardActions = {
                        },
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    BareCodeScannerIcon(
                        haveCameraDevice = haveCameraDevice,
                        toaster = toaster,
                        onClick = {
                            openBareCodeScanner(
                                navigate = { navigate(it) },
                                onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                            )
                        }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))
                OutlinedButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 35.dp, end = 35.dp),
                    enabled = zoneConsomationCB.isNotEmpty() && !affectCodeBareBatimentState.loading,
                    onClick = {
                        syncInvBatimentViewModel.syncAffectCodeBareBatiment(
                            batimentCheck = BatimentCheck(
                                cLICode = selectedZoneConsomation.cLICode,
                                cltImoCB = zoneConsomationCB
                            ),
                            showErrorResult = true
                        )
                    },
                    shape = MaterialTheme.shapes.medium
                ) {
                    if (affectCodeBareBatimentState.loading)
                        LottieAnim(lotti = R.raw.loading, size = 30.dp)
                    else
                        Text(text = stringResource(R.string.verifier_bar_code))
                }

                Spacer(modifier = Modifier.height(16.dp))
                HorizontalDivider()
            }


        }



        if(selectedZoneConsomation.cliImoCB.isNullOrEmpty()) {
            Spacer(modifier =  Modifier.height(18.dp))
            Text(
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                text = stringResource(id = R.string.barecode_required_to_perform_operation_for_this_batiment),
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.error
            )
        }
        else {
            Spacer(modifier =  Modifier.height(18.dp))
            CustomCard(
                text = stringResource(id = R.string.inventaire) + " Articles",
                icon = Icons.AutoMirrored.Filled.FactCheck,
                onClick = {


                    generateCodeM("Inv", AjoutInventaireBatimentRoute())

                }
            )
            //  Spacer(modifier =  Modifier.height(6.dp))
            CustomCard(
                text = stringResource(id = R.string.affectation_title) + " Articles",
                icon = Icons.AutoMirrored.Filled.DriveFileMove,
                onClick = {

                    generateCodeM("Aff", AjoutAffectationBatimentRoute())
                }
            )
            //  Spacer(modifier =  Modifier.height(6.dp))
            CustomCard(
                text = stringResource(id = R.string.patrimoine_deplacement_out_title) + " Articles",
                icon = Icons.AutoMirrored.Filled.CallMade,
                onClick = {
                    generateCodeM("Dep_Out", ChooseImmoRoute)
                }
            )
            //  Spacer(modifier =  Modifier.height(6.dp))

            CustomCard(
                text = stringResource(id = R.string.patrimoine_deplacement_in_title) + " Articles",
                icon = Icons.AutoMirrored.Filled.CallReceived,
                onClick = {
                    generateCodeM("Dep_In", DeplacementOutByUserWaitingRoute(title = context.resources.getString(
                        R.string.etat_depout_title))
                    )
                }
            )
        }


        Spacer(modifier =  Modifier.height(24.dp))

    }
}