package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.remote.api.InventairePatrimoineApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.remote.api.InventairePatrimoineApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object InventairePatrimoineRemoteModule {

        @Provides
        @Singleton
        fun provideInventairePatrimoineApi(client: HttpClient): InventairePatrimoineApi = InventairePatrimoineApiImpl(client)


    }