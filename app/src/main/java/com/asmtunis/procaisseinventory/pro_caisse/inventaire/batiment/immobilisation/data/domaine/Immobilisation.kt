package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProCaisseConstants.IMMOBILISATION_TABLE)
@Serializable
class Immobilisation  (
    @PrimaryKey
    @ColumnInfo(name = "CLI_Code")
    @SerialName("CLI_Code")
    var cLICode: String = "",

    @ColumnInfo(name = "Clt_Immo")
    @SerialName("Clt_Immo")
    var cliImmo: Int? = 0,

//not present in response
    @SerialName("exercice")
    @ColumnInfo(name = "exercice")
    var exercice: String = "",


    @ColumnInfo(name = "Clt_ImoCodeP")
    @SerialName("Clt_ImoCodeP")
    var cliImoCodeP: String? = null,

    @ColumnInfo(name = "Clt_ImoTypEmp")
    @SerialName("Clt_ImoTypEmp")
    var cliImoTypEmp: String? = null,

    @ColumnInfo(name = "Clt_ImoCB")
    @SerialName("Clt_ImoCB")
    var cliImoCB: String? = null,

    @ColumnInfo(name = "TyEmpImNom")
    @SerialName("TyEmpImNom")
    var tyEmpImNom: String? = null,


    @ColumnInfo(name = "isBatimentByUser")
    @Transient
    var isBatimentByUser: Boolean = false,

    @SerialName("avec_qui_convention")
    @ColumnInfo(name = "avec_qui_convention")
    val avecQuiConvention: String? = "",
    @SerialName("CLI_Adresse")
    @ColumnInfo(name = "CLI_Adresse")
    val cLIAdresse: String? = "",
    @SerialName("CLI_Code_M")
    @ColumnInfo(name = "CLI_Code_M")
    val cLICodeM: String? = "",
    @SerialName("CLI_CodePostale")
    @ColumnInfo(name = "CLI_CodePostale")
    val cLICodePostale: String? = "",
    @SerialName("CLI_CodeRep")
    @ColumnInfo(name = "CLI_CodeRep")
    val cLICodeRep: String? = "",
    @SerialName("CLI_DC")
    @ColumnInfo(name = "CLI_DC")
    val cLIDC: String = "",
    @SerialName("CLI_DDm")
    @ColumnInfo(name = "CLI_DDm")
    val cLIDDm: String = "",
    @SerialName("CLI_DDmM")
    @ColumnInfo(name = "CLI_DDmM")
    val cLIDDmM: String? = "",
    @SerialName("CLI_Date_Cre")
    @ColumnInfo(name = "CLI_Date_Cre")
    val cLIDateCre: String = "",
    @SerialName("CLI_Etat")
    @ColumnInfo(name = "CLI_Etat")
    val cLIEtat: String = "",
    @SerialName("CLI_Exeno")
    @ColumnInfo(name = "CLI_Exeno")
    val cLIExeno: String = "",
    @SerialName("CLI_Exo_Num")
    @ColumnInfo(name = "CLI_Exo_Num")
    val cLIExoNum: String? = "",
    @SerialName("CLI_Exo_Valable")
    @ColumnInfo(name = "CLI_Exo_Valable")
    val cLIExoValable: String? = "",
    @SerialName("CLI_Exonoration")
    @ColumnInfo(name = "CLI_Exonoration")
    val cLIExonoration: String? = "",
    @SerialName("CLI_export")
    @ColumnInfo(name = "CLI_export")
    val cLIExport: String = "",
    @SerialName("CLI_exportM")
    @ColumnInfo(name = "CLI_exportM")
    val cLIExportM: String = "",
    @SerialName("CLI_Fax")
    @ColumnInfo(name = "CLI_Fax")
    val cLIFax: String? = "",
    @SerialName("CLI_Fodec")
    @ColumnInfo(name = "CLI_Fodec")
    val cLIFodec: String = "",
    @SerialName("CLI_Forfetaire")
    @ColumnInfo(name = "CLI_Forfetaire")
    val cLIForfetaire: String = "",
    @SerialName("CLI_Industrielle")
    @ColumnInfo(name = "CLI_Industrielle")
    val cLIIndustrielle: String = "",
    @SerialName("CLI_isCredit")
    @ColumnInfo(name = "CLI_isCredit")
    val cLIIsCredit: String = "",
    @SerialName("CLI_isPGros")
    @ColumnInfo(name = "CLI_isPGros")
    val cLIIsPGros: String = "",
    @SerialName("CLI_isRemise")
    @ColumnInfo(name = "CLI_isRemise")
    val cLIIsRemise: String = "",
    @SerialName("CLI_Localite")
    @ColumnInfo(name = "CLI_Localite")
    val cLILocalite: String? = "",
    @SerialName("CLI_mail")
    @ColumnInfo(name = "CLI_mail")
    val cLIMail: String? = "",
    @SerialName("CLI_MatFisc")
    @ColumnInfo(name = "CLI_MatFisc")
    val cLIMatFisc: String? = "",
    @SerialName("CLI_MaxCredit")
    @ColumnInfo(name = "CLI_MaxCredit")
    val cLIMaxCredit: String? = "",
    @SerialName("CLI_NomPren")
    @ColumnInfo(name = "CLI_NomPren")
    val cLINomPren: String = "",
    @SerialName("CLI_Obs")
    @ColumnInfo(name = "CLI_Obs")
    val cLIObs: String? = "",
    @SerialName("CLI_PC_Email")
    @ColumnInfo(name = "CLI_PC_Email")
    val cLIPCEmail: String? = "",
    @SerialName("CLI_PC_Tel1")
    @ColumnInfo(name = "CLI_PC_Tel1")
    val cLIPCTel1: String? = "",
    @SerialName("CLI_PC_Tel2")
    @ColumnInfo(name = "CLI_PC_Tel2")
    val cLIPCTel2: String? = "",
    @SerialName("CLI_PGros")
    @ColumnInfo(name = "CLI_PGros")
    val cLIPGros: String = "",
    @SerialName("CLI_Pers_Contact")
    @ColumnInfo(name = "CLI_Pers_Contact")
    val cLIPersContact: String? = "",
    @SerialName("CLI_Remise")
    @ColumnInfo(name = "CLI_Remise")
    val cLIRemise: String = "",
    @SerialName("CLI_SiteWeb")
    @ColumnInfo(name = "CLI_SiteWeb")
    val cLISiteWeb: String? = "",
    @SerialName("CLI_Solde")
    @ColumnInfo(name = "CLI_Solde")
    val cLISolde: String? = "",
    @SerialName("CLI_Station")
    @ColumnInfo(name = "CLI_Station")
    val cLIStation: String = "",
    @SerialName("CLI_Tarif")
    @ColumnInfo(name = "CLI_Tarif")
    val cLITarif: String? = "",
    @SerialName("CLI_TauxRemGlob")
    @ColumnInfo(name = "CLI_TauxRemGlob")
    val cLITauxRemGlob: String? = "",
    @SerialName("CLI_Tel1")
    @ColumnInfo(name = "CLI_Tel1")
    val cLITel1: String? = null,
    @SerialName("CLI_Tel2")
    @ColumnInfo(name = "CLI_Tel2")
    val cLITel2: String? = null,
    @SerialName("CLI_Timbre")
    @ColumnInfo(name = "CLI_Timbre")
    val cLITimbre: String = "",
    @SerialName("CLI_Type")
    @ColumnInfo(name = "CLI_Type")
    val cLIType: String = "",
    @SerialName("CLI_Type_Reg")
    @ColumnInfo(name = "CLI_Type_Reg")
    val cLITypeReg: String? = "",
    @SerialName("CLI_User")
    @ColumnInfo(name = "CLI_User")
    val cLIUser: String? = "",
    @SerialName("CLI_ValiditeTraite")
    @ColumnInfo(name = "CLI_ValiditeTraite")
    val cLIValiditeTraite: String? = "",
    @SerialName("CLI_Ville")
    @ColumnInfo(name = "CLI_Ville")
    val cLIVille: String? = "",
    @SerialName("Cli_Credit")
    @ColumnInfo(name = "Cli_Credit")
    val cliCredit: String = "",
    @SerialName("Cli_Debit")
    @ColumnInfo(name = "Cli_Debit")
    val cliDebit: String = "",
    @SerialName("Cli_EX")
    @ColumnInfo(name = "Cli_EX")
    val cliEX: String? = "",
    @SerialName("Clt_CIN")
    @ColumnInfo(name = "Clt_CIN")
    val cltCIN: String? = "",
    @SerialName("Clt_CIN_Date")
    @ColumnInfo(name = "Clt_CIN_Date")
    val cltCINDate: String? = "",
    @SerialName("Clt_CIN_ville")
    @ColumnInfo(name = "Clt_CIN_ville")
    val cltCINVille: String? = "",
    @SerialName("Clt_Circuit")
    @ColumnInfo(name = "Clt_Circuit")
    val cltCircuit: String? = "",
    @SerialName("Clt_CodCompta")
    @ColumnInfo(name = "Clt_CodCompta")
    val cltCodCompta: String? = "",
    @SerialName("Clt_Couleur")
    @ColumnInfo(name = "Clt_Couleur")
    val cltCouleur: String = "",
    @SerialName("Clt_Epouse")
    @ColumnInfo(name = "Clt_Epouse")
    val cLISociete: String? = "",
    @SerialName("Clt_Epouse_Tel")
    @ColumnInfo(name = "Clt_Epouse_Tel")
    val cltEpouseTel: String? = "",
    @SerialName("Clt_Epoux")
    @ColumnInfo(name = "Clt_Epoux")
    val cltEpoux: String? = "",
    @SerialName("Clt_Epoux_Tel")
    @ColumnInfo(name = "Clt_Epoux_Tel")
    val cltEpouxTel: String? = "",
    @SerialName("Clt_Fidelite")
    @ColumnInfo(name = "Clt_Fidelite")
    val cltFidelite: String? = "",
    @SerialName("Clt_Info1")
    @ColumnInfo(name = "Clt_Info1")
    val cltInfo1: String? = null,
    @SerialName("Clt_Info2")
    @ColumnInfo(name = "Clt_Info2")
    val cltInfo2: String? = null,
    @SerialName("Clt_Info3")
    @ColumnInfo(name = "Clt_Info3")
    val cltInfo3: String? = null,
    @SerialName("Clt_Info4")
    @ColumnInfo(name = "Clt_Info4")
    val cltInfo4: String? = null,
    @SerialName("Clt_Info5")
    @ColumnInfo(name = "Clt_Info5")
    val cltInfo5: String? = null,
    @SerialName("Clt_Info6")
    @ColumnInfo(name = "Clt_Info6")
    val cltnomGearant: String? = null,
    @SerialName("Clt_Info7")
    @ColumnInfo(name = "Clt_Info7")
    val cltVille: String? = null,
    @SerialName("Clt_Info8")
    @ColumnInfo(name = "Clt_Info8")
    val cltGouvernorat: String? = null,
    @SerialName("Clt_Info9")
    @ColumnInfo(name = "Clt_Info9")
    val cLtNomMagasin: String? = null,
    @SerialName("Clt_Info10")
    @ColumnInfo(name = "Clt_Info10")
    val cLtProfession: String? = null,
    @SerialName("Clt_Latitude")
    @ColumnInfo(name = "Clt_Latitude")
    val cltLatitude: Double? = null,
    @SerialName("Clt_Longitude")
    @ColumnInfo(name = "Clt_Longitude")
    val cltLongitude: Double? = null,
    @SerialName("Clt_PersMorale")
    @ColumnInfo(name = "Clt_PersMorale")
    val cltPersMorale: String? = "",
    @SerialName("Clt_PersPysiq")
    @ColumnInfo(name = "Clt_PersPysiq")
    val cltPersPysiq: String? = "",
    @SerialName("Credit")
    @ColumnInfo(name = "Credit")
    val credit: String = "",
    @SerialName("dateNaissance")
    @ColumnInfo(name = "dateNaissance")
    val dateNaissance: String? = "",
    @SerialName("datePA")
    @ColumnInfo(name = "datePA")
    val datePA: String? = "",
    @SerialName("Debit")
    @ColumnInfo(name = "Debit")
    val debit: String = "",
    @SerialName("Num_Fidelite")
    @ColumnInfo(name = "Num_Fidelite")
    val numFidelite: String? = "",
    @SerialName("PrefixPhone")
    @ColumnInfo(name = "PrefixPhone")
    val prefixPhone: String? = "",
    @SerialName("Solde")
    @ColumnInfo(name = "Solde")
    var solde: String = "",



): BaseModel()




