package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.TYPE_POINT_VENTE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import kotlinx.coroutines.flow.Flow


@Dao
interface TypePointVenteDAO {
    @get:Query("SELECT * FROM $TYPE_POINT_VENTE_TABLE")
    val all: Flow<List<TypePointVenteDn>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(DNtypeServices: List<TypePointVenteDn>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(dnTypePVente: TypePointVenteDn)

    @Query("SELECT * FROM $TYPE_POINT_VENTE_TABLE WHERE CodeTypePV = :codeTypePV")
    fun getbyCode(codeTypePV: String?): TypePointVenteDn

    @Query("delete from $TYPE_POINT_VENTE_TABLE")
    fun deleteAll()

}

