package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.repository

import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMission
import kotlinx.coroutines.flow.Flow


interface LigneOrdreMissionLocalRepository {
    fun upsertAll(value: List<LigneOrdreMission>)
    fun upsert(value: LigneOrdreMission)
    fun notSync(): Flow<List<LigneOrdreMission>?>

    fun updateLgOrdreMissionNotSync(oRDCode: String)
    fun deleteAll()
}