package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.remote.api.DeplacementOutByUserApi
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.remote.api.DeplacementOutByUserApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object DeplacementOutByUserRemoteModule {

    @Provides
    @Singleton
    fun provideDeplacementOutByUserApi(client: HttpClient): DeplacementOutByUserApi = DeplacementOutByUserApiImpl(client)

}