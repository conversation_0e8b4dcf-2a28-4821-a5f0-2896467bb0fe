package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.coroutines.flow.Flow


interface LigneBonCommandeLocalRepository {
        fun upsertAll(value: List<LigneBonCommande>)
        fun upsert(value: LigneBonCommande)

        fun setSynced(newNum: String, oldNum: String)
        fun setToInserted(codeM: String)
        fun deletebyCodeMAndArtCode(codeM: String, artCode: String)
        fun deleteByCodeM(codeM: String)
        fun deleteByLgDevNumBon(code: String)
        fun deleteAll()

        fun getAll(): Flow<List<LigneBonCommande>>


    }