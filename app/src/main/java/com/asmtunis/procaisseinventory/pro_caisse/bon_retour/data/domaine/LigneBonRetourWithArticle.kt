package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable



@Serializable
data class LigneBonRetourWithArticle (
    @Embedded
    @SerialName("ligneBonRetour")
    var ligneBonRetour: LigneBonRetour? = null,

    @Relation(
        parentColumn = "LIG_BonEntree_CodeArt",
        entityColumn = "ART_Code"
    )
    @SerialName("article")
    var article: Article? = null,


    )