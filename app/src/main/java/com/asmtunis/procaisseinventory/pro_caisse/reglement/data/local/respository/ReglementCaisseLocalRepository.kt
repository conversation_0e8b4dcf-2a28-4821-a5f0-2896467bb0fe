package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.respository

import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.domaine.ReglementWithChequeAndTicketResto
import kotlinx.coroutines.flow.Flow

interface ReglementCaisseLocalRepository {
    fun getBySession(
        station: String,
        caisse: String,
    ): Flow<List<ReglementCaisse>?>

    fun upsert(value: ReglementCaisse)

    fun upsertAll(value: List<ReglementCaisse>)

    fun updateRegCodeAndState(
        regCode: String,
        regCodeM: String,
    )

    fun deleteAll()

    fun deleteByCode(
        code: String,
        exercice: String,
        idCaisse: String,
    )

    fun getAll(): Flow<List<ReglementCaisse>>

    fun getbyRegRemarque(
        session: String,
        station: String,
        regRemarque: String,
    ): Flow<String?>

    fun getMntEspece(
        station: String,
        caisse: String,
    ): Flow<String?>

    fun getMntCheque(
        station: String,
        caisse: String,
    ): Flow<String?>

    fun getMntTraite(
        station: String,
        caisse: String,
    ): Flow<String?>

    fun getByTicketM(rEGCNumTicket: String): Flow<ReglementCaisse>

    fun getReglementByClient(codeClt: String): Flow<List<ReglementCaisse>>

    fun getAllNotSynced(): Flow<List<ReglementWithChequeAndTicketResto>?>

    fun getAllFiltred(
        isAsc: Int,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        sortBy: String,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>

    fun filterByClient(
        searchString: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        sortBy: String?,
        isAsc: Int?,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>

    fun filterByRegNumTicket(
        searchString: String,
        sortBy: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        isAsc: Int,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>

    fun filterByRegCode(
        searchString: String,
        sortBy: String,
        filterByTypePayment: String,
        filterByTypeReglement: String,
        isAsc: Int,
    ): Flow<List<ReglementCaisseWithTicketAndClient>>
}
