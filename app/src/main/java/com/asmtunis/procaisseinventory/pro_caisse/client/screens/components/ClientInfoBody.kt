package com.asmtunis.procaisseinventory.pro_caisse.client.screens.components

import android.widget.Toast
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CreditScore
import androidx.compose.material.icons.filled.Percent
import androidx.compose.material.icons.twotone.AccountBalance
import androidx.compose.material.icons.twotone.Code
import androidx.compose.material.icons.twotone.FormatQuote
import androidx.compose.material.icons.twotone.LocationSearching
import androidx.compose.material.icons.twotone.Mail
import androidx.compose.material.icons.twotone.Phone
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.AFF
import com.asmtunis.procaisseinventory.core.Globals.DEP_IN
import com.asmtunis.procaisseinventory.core.Globals.DEP_OUT
import com.asmtunis.procaisseinventory.core.Globals.INV
import com.asmtunis.procaisseinventory.core.Globals.PROSPECT
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction.haveAuth
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BC
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BL
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.BR
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.CLOT_SESSION_AUTO
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.PATRIMOINE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.VEILLE_CONCURENTIELLE
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.navigation.AddBonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.AddBonRetourRoute
import com.asmtunis.procaisseinventory.core.navigation.AddModifyDigitalDistributionRoute
import com.asmtunis.procaisseinventory.core.navigation.AddReglementRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutAffectationPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutDepInPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutDepOutPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutInventairePatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.BonCommandeRoute
import com.asmtunis.procaisseinventory.core.navigation.BonLivraisonRoute
import com.asmtunis.procaisseinventory.core.navigation.BonRetourRoute
import com.asmtunis.procaisseinventory.core.navigation.DigitalDistributionRoute
import com.asmtunis.procaisseinventory.core.navigation.InventairePatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.ReglementRoute
import com.asmtunis.procaisseinventory.core.utils.IntentUtils.openEmailApp
import com.asmtunis.procaisseinventory.core.utils.IntentUtils.openPhoneDialer
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.returnNAifEmpty
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.AddSessionCaisseResponse
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.ville.domaine.Ville
import com.asmtunis.procaisseinventory.nav_components.ID.BON_COMMANDE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.BON_LIVRAISON_ID
import com.asmtunis.procaisseinventory.nav_components.ID.BON_RETOUR_ID
import com.asmtunis.procaisseinventory.nav_components.ID.DISTRIBUTION_NUMERIQUE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.INVENTAIRE_PATRIMOINE_ID
import com.asmtunis.procaisseinventory.nav_components.ID.REGLEMENT_ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel.Companion.proCaisseDrawerItems
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.client.DetailComponent
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.client.screens.maxHeight
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.utils.durationSincePast
import java.util.Locale

@Composable
fun ClientInfoBody (
    navigate: (route: Any) -> Unit,
    proCaisseViewModels: ProCaisseViewModels,
    locationViewModule: LocationViewModule,
    navDrawerViewModel: NavigationDrawerViewModel,
    isConnected: Boolean,
    utilisateur: Utilisateur,
    client: Client,
    clientId: String,
    sessionCaisse: SessionCaisse,
    addSessionCaisseState: RemoteResponseState<AddSessionCaisseResponse>,
    listVilleDn: List<Ville?>,
    listeReglementLibreByClient: MutableList<ReglementCaisse>,
    proCaisseAuthorization: List<Authorization>,
    visitesList: Map<VisitesDn, List<LigneVisitesDn>>,
    invPatList: Map<BonCommande, List<LigneBonCommande>>,
    bcList: Map<BonCommande, List<LigneBonCommande>>,
    blList: Map<Ticket, List<LigneTicket>>,
    prefixList: List<Prefixe>,
    bonRetourList: Map<BonRetour, List<LigneBonRetourWithArticle>>,
    setFondCaisseDialogueVisibility : (Boolean) -> Unit,
    generateCodeMPayment : (utilisateur: Utilisateur, prefix: String) -> Unit,
    generateCodeM : (utilisateur: Utilisateur, prefix: String) -> Unit
) {

    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val distNumViewModel = proCaisseViewModels.distNumViewModel
    val paymentViewModel = proCaisseViewModels.paymentViewModel
    val bonCommandeVM = proCaisseViewModels.bonCommandeViewModel
    val bonLivraisonVM = proCaisseViewModels.bonLivraisonViewModel

    val haveClotureSessionAutoAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == CLOT_SESSION_AUTO}
    val haveBCAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == BC}
    val haveBLAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == BL}
    val haveBRAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == BR}
    val haveInvPatrimoineAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == PATRIMOINE}
    val haveVcAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == VEILLE_CONCURENTIELLE}

    val isProspect = client.cLIType.lowercase(Locale.getDefault()) == PROSPECT.lowercase(Locale.getDefault())


    val context = LocalContext.current
    val closeSessionCaisse = (haveClotureSessionAutoAuthorisation  &&
            durationSincePast(dateInPast = sessionCaisse.sCDateHeureCrea?:"2023-05-15 01:00:00") >0)  ||
            (listVilleDn.firstOrNull()?.factClotSessPDA?: "null") =="1"

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(top = maxHeight.dp)
    ) {
        item {
            Spacer(modifier = Modifier.height(12.dp))

            ItemDetail(
                modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.code_client),
                dataText = client.cLICode,
                icon = Icons.TwoTone.Code
            )

            ItemDetail(
                modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.tax_registration_number_field_title),
                dataText = returnNAifEmpty(client.cLIMatFisc),
                icon = Icons.TwoTone.FormatQuote
            )



            ItemDetail(
                modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.adresse_field_title),
                dataText = returnNAifEmpty(client.cLIAdresse),
                icon = Icons.TwoTone.LocationSearching
            )


            ItemDetail(
                modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.email_field_title),
                dataText = returnNAifEmpty(client.cLIMail),
                icon = Icons.TwoTone.Mail,
                onClick = {
                    if(returnNAifEmpty(client.cLIMail) ==  "N/A") return@ItemDetail

                    openEmailApp(
                        context = context,
                        email = returnNAifEmpty(client.cLIMail),
                        onComplete = { error ->
                            if(error != null)
                                Toast.makeText(context, error, Toast.LENGTH_LONG).show()

                        }
                    )
                }
            )

            ItemDetail(
                modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.sold),
                dataText = if (client.solde.isEmpty()) "N/A" else convertStringToPriceFormat(client.solde),
                icon = Icons.TwoTone.AccountBalance
            )


            ItemDetail(
                modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                title = stringResource(id = R.string.revenuImposable),
                dataText = if ((client.cltMntRevImp?:0.0)>0.0) client.cltMntRevImp?.toString()?:"N/A" else "N/A",
                icon = Icons.TwoTone.AccountBalance
            )

            Row(
                modifier = Modifier.fillMaxWidth().padding(top = 12.dp, bottom = 12.dp),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment =  Alignment.CenterVertically,
            ) {
                ItemDetail(
                    modifier = Modifier.fillMaxWidth(0.45f),
                    title = stringResource(id = R.string.credit_title),
                    dataText = if(haveAuth(value = client.cLIIsCredit)) stringResource(id =R.string.oui) else stringResource(id =R.string.non),
                    icon = Icons.Default.CreditScore
                )

                ItemDetail(
                    modifier = Modifier.fillMaxWidth(),
                    title = stringResource(id = R.string.stamp_field_title),
                    dataText = if(haveAuth(value = client.cLITimbre)) stringResource(id =R.string.oui) else stringResource(id =R.string.non),
                    icon = Icons.Default.Percent
                )
            }


            Row(
                modifier = Modifier.fillMaxWidth().padding(top = 12.dp, bottom = 12.dp),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment =  Alignment.CenterVertically,
            ) {
                ItemDetail(
                    modifier = Modifier.fillMaxWidth(0.45f),
                    title = stringResource(id = R.string.phone1_field_title),
                    dataText = returnNAifEmpty(client.cLITel1),
                    icon = Icons.TwoTone.Phone,
                    onClick = {
                        if(returnNAifEmpty(client.cLITel1) ==  "N/A")
                            return@ItemDetail
                        openPhoneDialer(
                            context = context,
                            phoneNbr = returnNAifEmpty(client.cLITel1)
                        )
                    }
                )


                ItemDetail(
                    modifier = Modifier.fillMaxWidth(),
                    title = stringResource(id = R.string.phone2_field_title),
                    dataText = returnNAifEmpty(client.cLITel2),
                    icon = Icons.TwoTone.Phone,
                    onClick = {
                        if(returnNAifEmpty(client.cLITel2) ==  "N/A")
                            return@ItemDetail
                        openPhoneDialer(
                            context = context,
                            phoneNbr = returnNAifEmpty(client.cLITel2)
                        )
                    }
                )
            }



            Spacer(modifier = Modifier.height(12.dp))
            if (closeSessionCaisse) {
                OutlinedButton(
                    enabled = !addSessionCaisseState.loading && isConnected,
                    onClick = { setFondCaisseDialogueVisibility(true) },
                    border = BorderStroke(width = 1.dp, color = MaterialTheme.colorScheme.error),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(8.dp)
                ) {
                    if (addSessionCaisseState.loading)
                        LottieAnim(lotti = R.raw.loading, size = 50.dp)
                    else if(addSessionCaisseState.error!=null)
                        Text(text = addSessionCaisseState.error, color = MaterialTheme.colorScheme.error)
                    else
                        Text(text = stringResource(id = R.string.clot_session))
                }
            }






            if (haveBLAuthorisation) {
                DetailComponent(
                    isAddVisible = !closeSessionCaisse,
                    isDescriptionVisisble = listeReglementLibreByClient.isNotEmpty(),
                    describtionText = stringResource(id = R.string.reglement_libre, listeReglementLibreByClient.size.toString()),
                    onAddClick = {
                        paymentViewModel.resetPaymentsValue()
                        val prefixe = prefixList.firstOrNull { it.pREIdTable == "ReglementCaisse" }?.pREPrefixe?: "REGC_"

                        generateCodeMPayment(
                            utilisateur,
                            prefixe
                        )
                        navigate(AddReglementRoute(clientId = clientId))
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == REGLEMENT_ID }?.let { navDrawerViewModel.onSelectedMenuChange(it) }
                        navigate(ReglementRoute(clientId = clientId))
                    }
                )
            }

            if (haveVcAuthorisation && isProspect) {

                DetailComponent(
                    isDescriptionVisisble = visitesList.isNotEmpty(),
                    describtionText =  stringResource(id = R.string.visites_nbr, visitesList.size.toString()),
                    onAddClick = {

                        val prefixe = prefixList.firstOrNull { it.pREIdTable == "DN_Visite" }?.pREPrefixe?: "VS_"
                        generateCodeM(
                            utilisateur,
                            prefixe
                        )
                        distNumViewModel.onModifyVisiteChange(true)
                        distNumViewModel.onSelectedVisiteChange(visitesList)
                        navigate(AddModifyDigitalDistributionRoute(clientId = clientId))
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == DISTRIBUTION_NUMERIQUE_ID }?.let { navDrawerViewModel.onSelectedMenuChange(it) }
                        navigate(DigitalDistributionRoute(clientId = clientId))
                    }
                )
            }

            if (haveBCAuthorisation) {

                DetailComponent(
                    isAddVisible = !closeSessionCaisse,
                    isDescriptionVisisble = bcList.isNotEmpty(),
                    describtionText = stringResource(id = R.string.commande_nbr, bcList.size.toString()),
                    onAddClick = {
                        // val prefixe = prefixList.firstOrNull { it.pREIdTable == "ReglementCaisse" }?.pREPrefixe?: "REGC_"

                        generateCodeM(
                            utilisateur,
                            "BC_M"
                        )
                        paymentViewModel.resetPaymentsValue()// todo may be call it when open client info screen
                        bonCommandeVM.restBonCommande()
                        navigate(AddBonCommandeRoute(clientId = clientId))
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == BON_COMMANDE_ID }?.let { navDrawerViewModel.onSelectedMenuChange(it) }
                        navigate(BonCommandeRoute(clientId = clientId))
                    }
                )


            }

            if (haveBLAuthorisation) {

                DetailComponent(
                    isAddVisible = !closeSessionCaisse,
                    isDescriptionVisisble = blList.isNotEmpty(),
                    describtionText = stringResource(id = R.string.nbr_bon_livraison, blList.size.toString()),
                    onAddClick = {
                        // restBonCommande in case if bl is created from bc
                        bonCommandeVM.restBonCommande()

                        paymentViewModel.resetPaymentsValue()
                        bonLivraisonVM.onUpdateChange(false)

                        val prefixeBl = prefixList.firstOrNull { it.pREIdTable == "Bon_livraison" }?.pREPrefixe?: "BL_M_"
                        bonLivraisonVM.generateCodeM(utilisateur = utilisateur, prefix = prefixeBl)

                        val prefixe = prefixList.firstOrNull { it.pREIdTable == "ReglementCaisse" }?.pREPrefixe?: "REGC_"

                        generateCodeMPayment(
                            utilisateur,
                            prefixe
                        )
                        locationViewModule.getCurrentLocation()
                        navigate(AddBonLivraisonRoute(clientId = clientId))
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == BON_LIVRAISON_ID }?.let { navDrawerViewModel.onSelectedMenuChange(it) }
                        navigate(BonLivraisonRoute(clientId = clientId))
                    }
                )


            }

            if (haveBRAuthorisation) {

                DetailComponent(
                    isAddVisible = !closeSessionCaisse,
                    isDescriptionVisisble = bonRetourList.isNotEmpty(),
                    describtionText = stringResource(id = R.string.nbr_retour, bonRetourList.size.toString()),
                    onAddClick = {

                        val prefixe = prefixList.firstOrNull { it.pREIdTable == "Bon_Retour" }?.pREPrefixe?: "BR_M_"
                        generateCodeM(utilisateur, prefixe)

                        navigate(AddBonRetourRoute(clientId = clientId))
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == BON_RETOUR_ID }
                            ?.let {
                                navDrawerViewModel.onSelectedMenuChange(it)
                            }
                        navigate(BonRetourRoute(clientId = clientId))
                    }
                )


            }


            if (haveInvPatrimoineAuthorisation && client.cltInfo1?.lowercase() == PATRIMOINE) {

                val affectationList = invPatList.filter { it.key.dEV_info3 == TypePatrimoine.AFFECTATION.typePat }
                DetailComponent(
                    isDescriptionVisisble = affectationList.isNotEmpty(),
                    describtionText = stringResource(id = R.string.nbr_affectation, affectationList.size.toString()),
                    onAddClick = {
                        generateCodeM(utilisateur, AFF)
                        navigate(AjoutAffectationPatrimoineRoute(clientId = clientId))
                        invPatViewModel.setTabState(
                            tabs = TypePat.AFFECTATION.typePat,
                            typeInv = TypePatrimoine.AFFECTATION.typePat
                        )
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == INVENTAIRE_PATRIMOINE_ID }
                            ?.let { navDrawerViewModel.onSelectedMenuChange(it) }

                        invPatViewModel.setTabState(
                            tabs = TypePat.AFFECTATION.typePat,
                            typeInv = TypePatrimoine.AFFECTATION.typePat
                        )
                        navigate(InventairePatrimoineRoute(clientId = clientId))
                    }
                )


                val inventaireList = invPatList.filter { it.key.dEV_info3 == TypePatrimoine.INVENTAIRE.typePat }
                DetailComponent(
                    isDescriptionVisisble = inventaireList.isNotEmpty(),
                    describtionText = stringResource(id = R.string.nbr_patrimoineInventaire, inventaireList.size.toString()),
                    onAddClick = {
                        generateCodeM(utilisateur, INV)
                        navigate(AjoutInventairePatrimoineRoute(clientId = clientId))
                        invPatViewModel.setTabState(
                            tabs = TypePat.INVENTAIRE.typePat,
                            typeInv = TypePatrimoine.INVENTAIRE.typePat
                        )
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == INVENTAIRE_PATRIMOINE_ID }
                            ?.let { navDrawerViewModel.onSelectedMenuChange(it) }

                        invPatViewModel.setTabState(
                            tabs = TypePat.INVENTAIRE.typePat,
                            typeInv = TypePatrimoine.INVENTAIRE.typePat
                        )
                        navigate(InventairePatrimoineRoute(clientId = clientId))
                    }
                )


                val entreList = invPatList.filter { it.key.dEV_info3 == TypePatrimoine.ENTREE.typePat }
                DetailComponent(
                    isDescriptionVisisble = entreList.isNotEmpty(),
                    describtionText = stringResource(id = R.string.nbr_patrimoine_deplacement_in, entreList.size.toString()),
                    onAddClick = {
                        generateCodeM(utilisateur, DEP_IN)
                        navigate(AjoutDepInPatrimoineRoute(clientId = clientId))
                        invPatViewModel.setTabState(
                            tabs = TypePat.DEP_IN.typePat,
                            typeInv = TypePatrimoine.ENTREE.typePat
                        )
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == INVENTAIRE_PATRIMOINE_ID }
                            ?.let { navDrawerViewModel.onSelectedMenuChange(it) }

                        invPatViewModel.setTabState(
                            tabs = TypePat.DEP_IN.typePat,
                            typeInv = TypePatrimoine.ENTREE.typePat
                        )
                        navigate(InventairePatrimoineRoute(clientId = clientId))
                    }
                )


                val sortiList = invPatList.filter { it.key.dEV_info3 == TypePatrimoine.SORTIE.typePat }
                DetailComponent(
                    isDescriptionVisisble = sortiList.isNotEmpty(),
                    describtionText = stringResource(id = R.string.nbr_patrimoine_deplacement_out, sortiList.size.toString()),
                    onAddClick = {
                        generateCodeM(utilisateur, DEP_OUT)
                        navigate(AjoutDepOutPatrimoineRoute(clientId = clientId))
                        invPatViewModel.setTabState(
                            tabs = TypePat.DEP_OUT.typePat,
                            typeInv = TypePatrimoine.SORTIE.typePat
                        )
                    },
                    onDetailClick = {
                        proCaisseDrawerItems.find { it.id == INVENTAIRE_PATRIMOINE_ID }
                            ?.let { navDrawerViewModel.onSelectedMenuChange(it) }

                        invPatViewModel.setTabState(
                            tabs = TypePat.DEP_OUT.typePat,
                            typeInv = TypePatrimoine.SORTIE.typePat
                        )
                        navigate(InventairePatrimoineRoute(clientId = clientId))
                    }
                )

            }


        }
    }
}