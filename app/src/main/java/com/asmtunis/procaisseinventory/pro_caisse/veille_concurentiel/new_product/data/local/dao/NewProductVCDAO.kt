package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.NEW_PRODUCT_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVCWithImages
import kotlinx.coroutines.flow.Flow


@Dao
interface NewProductVCDAO {
    @get:Query("SELECT * FROM $NEW_PRODUCT_TABLE where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    val all: Flow<List<NewProductVC>>

    @Query("SELECT * FROM $NEW_PRODUCT_TABLE WHERE CodeVCLanPM = :code")
    fun getByCodeM(code: String?): Flow<NewProductVC>

    @Query("SELECT * FROM $NEW_PRODUCT_TABLE WHERE CodeVCLanP = :code")
    fun getByCode(code: String?): Flow<NewProductVC>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(vcNewProductList: List<NewProductVC>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(vcNewProduct: NewProductVC)

    @get:Query("SELECT count(*) FROM $NEW_PRODUCT_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: Flow<Int>

    @get:Query("SELECT count(*) FROM $NEW_PRODUCT_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDeleteMubtale: Flow<Int>

    @get:Query("SELECT * FROM $NEW_PRODUCT_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    val noSyncedToAddOrUpdate: Flow<List<NewProductVC>?>

    @get:Query("SELECT * FROM $NEW_PRODUCT_TABLE where isSync=0 and Status='DELETED' ")
    val getNoSyncedToDelete: Flow<List<NewProductVC>>

    @get:Query("SELECT count(*) FROM $NEW_PRODUCT_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val countNonSync: Int

    @get:Query("SELECT count(*) FROM $NEW_PRODUCT_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDelete: Int

    @Query("delete from $NEW_PRODUCT_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $NEW_PRODUCT_TABLE where CodeVCLanP=:CodeAutre")
    fun deleteById(CodeAutre: String?)

    @Query("DELETE FROM $NEW_PRODUCT_TABLE where CodeVCLanP=:code or CodeVCLanPM = :code")
    fun deleteByCode(code: String)

    @Delete
    fun delete(newProductVC: NewProductVC)

    @Query("UPDATE $NEW_PRODUCT_TABLE SET Status = 'DELETED' , IsSync = 0 where CodeVCLanPM = :codeMobile  or CodeVCLanP=:code")
    fun setDeleted(code: String, codeMobile: String)

    @Query("UPDATE $NEW_PRODUCT_TABLE SET Status = :status , IsSync = :isSync where CodeVCLanPM = :code  or CodeVCLanP=:code")
    fun restDeleted(code: String,status : String, isSync:Boolean)






    @Query("UPDATE $NEW_PRODUCT_TABLE SET CodeVCLanP = :code , Status = 'SELECTED' , IsSync = 1 where CodeVCLanPM =:codeMobile")
    fun updateCloudCode(code: String, codeMobile: String)



    @Transaction
    @Query(
        "SELECT * FROM $NEW_PRODUCT_TABLE " +
            "WHERE CodeVCLanP LIKE '%' || :searchString || '%' " +
            "and ( CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +
            "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END) " +
               // " and Status !='DELETED' "+
            " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeVCLanP END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeVCLanP END DESC, " +

                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN ProduitLanP END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN ProduitLanP END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByNum(searchString: String, sortBy: String, filterByTypComm: String, filterByConcurrent: String, isAsc: Int): Flow<List<NewProductVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $NEW_PRODUCT_TABLE " +
                "WHERE ProduitLanP LIKE '%' ||  :searchString || '%' " +
                "and ( CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +
                "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END) " +
               //" and Status !='DELETED' "+
                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeVCLanP END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeVCLanP END DESC, " +

                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN ProduitLanP END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN ProduitLanP END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByNewProduct(searchString: String, sortBy: String, filterByTypComm: String, filterByConcurrent: String, isAsc: Int): Flow<List<NewProductVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $NEW_PRODUCT_TABLE " +
            "WHERE PrixLanP LIKE '%' || :searchString || '%' " +
            "and ( CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +
            "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END) " +
               //" and Status !='DELETED' "+
            " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeVCLanP END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeVCLanP END DESC, " +

                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN ProduitLanP END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN ProduitLanP END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByPrixProduct(searchString: String, filterByTypComm: String, filterByConcurrent: String, sortBy: String?, isAsc: Int?): Flow<List<NewProductVCWithImages>>


    @Transaction
    @Query(
        "SELECT * FROM $NEW_PRODUCT_TABLE " +
            "WHERE  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END   " +
            "and  CASE WHEN :filterByConcurrent !=  ''THEN CodeConcur=:filterByConcurrent ELSE CodeConcur !=:filterByConcurrent  END " +
               // "and Status !='DELETED' "+
            " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 1 THEN CodeVCLanP END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCLanP'  AND :isAsc = 2 THEN CodeVCLanP END DESC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 1 THEN ProduitLanP END ASC, " +
                "CASE WHEN :sortBy = 'ProduitLanP'  AND :isAsc = 2 THEN ProduitLanP END DESC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'dateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun getAllFiltred(isAsc: Int, filterByTypComm: String, filterByConcurrent: String, sortBy: String): Flow<List<NewProductVCWithImages>>

}
