package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.zone_consomation

import androidx.activity.compose.BackHandler
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.Scaffold
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.SITE_FINANCIER
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.SITE_RECEPTION
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.SOCIETE
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheck
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheckResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.SyncInvBatimentViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.dialogues.CustomAlertDialogue

/**
 * Societe ---->  SITE FINANCIER  ----> SITE RECEPTION  ----> ZONE DE CONSOMMATION(==Batiment)
 */
@Composable
fun ZoneConsomationDetailScreen(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    proCaisseViewModels: ProCaisseViewModels,
    syncInvBatimentViewModel: SyncInvBatimentViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel,
) {

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val haveCameraDevice = dataViewModel.getHaveCameraDevice()
    val isDarkTheme = settingViewModel.isDarkTheme
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val batimentViewModel = proCaisseViewModels.batimentViewModel
    val deplacementOutByUserViewModel = proCaisseViewModels.deplacementOutByUserViewModel

    val context = LocalContext.current
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val utilisateur = mainViewModel.utilisateur
    val density = LocalDensity.current
    val selectedZoneConsomation = batimentViewModel.selectedZoneConsomation
    val affectCodeBareBatimentState = syncInvBatimentViewModel.responseAffectCodeBareBatimentState
    val zoneConsomationCB = batimentViewModel.zoneConsomationCB
    val showAddCBBatiment = batimentViewModel.showAddCBBatiment

 //   val showAffectationCBDialogue = batimentViewModel.showAffectationCBDialogue

    val immobilisationTreeList = batimentViewModel.immobilisationTreeList

 //   val region = immobilisationTreeList.firstOrNull { it.tyEmpImNom == REGION }
    val societe = immobilisationTreeList.firstOrNull { it.tyEmpImNom == SOCIETE }
    val siteFinacier = immobilisationTreeList.firstOrNull { it.tyEmpImNom == SITE_FINANCIER }
    val siteReception = immobilisationTreeList.firstOrNull { it.tyEmpImNom == SITE_RECEPTION }

    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!

    val barCodeInfo = barCodeViewModel.barCodeInfo

    LaunchedEffect(key1 = barCodeInfo) {
        val barCodeValue = barCodeInfo.value

        if (barCodeValue.isEmpty()) return@LaunchedEffect
        batimentViewModel.onZoneConsomationCBChange(barCodeValue, "1")
        syncInvBatimentViewModel.syncAffectCodeBareBatiment(
            batimentCheck = BatimentCheck(
                cLICode = selectedZoneConsomation.cLICode,
                cltImoCB = barCodeValue
            ),
            showErrorResult = true
        )

        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
    }

    BackHandler(enabled = true) {
        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
        navigatePopUpTo(ZoneConsomationRoute, ZoneConsomationDetailRoute, true)

    }

    LaunchedEffect(key1 =  affectCodeBareBatimentState) {
        if (affectCodeBareBatimentState == RemoteResponseState<BatimentCheckResponse>() ) return@LaunchedEffect

  if(affectCodeBareBatimentState.error != null) { return@LaunchedEffect }

        if(zoneConsomationCB.isNotEmpty()){
           selectedZoneConsomation.cliImoCB = zoneConsomationCB
           batimentViewModel.onSelectedZoneConsomationChange(selectedZoneConsomation, "6")
        }


        batimentViewModel.onshowAddCBBatimentChange(false)
    }
    LaunchedEffect(key1 = Unit) {
        selectPatrimoineVM.resetSelectedPatrimoineArticles()
    }


    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    navigate(ZoneConsomationRoute)

                },
                showNavIcon = true,
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.detail_zone_consomation),
                titleVisibilty = !batimentViewModel.showSearchView,
            )
        }
    ) { padding ->


        CustomAlertDialogue(
            title = stringResource(id = R.string.affectation_bar_code),
            msg = (affectCodeBareBatimentState.message ?: "") + (if(affectCodeBareBatimentState.error!= null)"\nVérification Offline: "+ affectCodeBareBatimentState.error else ""),
            imageVector = Icons.Filled.Info,
            confirmText = stringResource(id = R.string.quitter),
            cancelText = "",
            openDialog = affectCodeBareBatimentState.message != null,
            setDialogueVisibility = {
                syncInvBatimentViewModel.resetResponseAffectCodeBareBatimentState()
                batimentViewModel.onshowAddCBBatimentChange(false)
            },
            customAction = {
                syncInvBatimentViewModel.resetResponseAffectCodeBareBatimentState()
                batimentViewModel.onZoneConsomationCBChange("", "2")

            }
        )



        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact-> {

                ZoneConsomationDetailColumnView(
                    isDarkTheme = isDarkTheme,
                    haveCameraDevice = haveCameraDevice,
                    navigate = { navigate(it) },
                    generateCodeM = { prefix, route->
                        invPatViewModel.restInvPatrimoine()
                        selectPatrimoineVM.resetSelectedPatrimoineArticles()

                        mainViewModel.generateCodeM(
                            utilisateur = utilisateur,
                            prefix = prefix
                        )
                        navigate(route)
                    },
                    padding = padding,
                    navigationDrawerViewModel = navigationDrawerViewModel,
                    syncInvBatimentViewModel = syncInvBatimentViewModel,
                    batimentViewModel = batimentViewModel,
                    barCodeViewModel = barCodeViewModel,
                    showAddCBBatiment = showAddCBBatiment,
                    zoneConsomationCB = zoneConsomationCB,
                    societe = societe,
                    siteFinacier = siteFinacier,
                    siteReception = siteReception,
                    selectedZoneConsomation = selectedZoneConsomation,
                    affectCodeBareBatimentState = affectCodeBareBatimentState
                )


            }
            
            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium  -> {
                ZoneConsomationDetailRowView(
                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                    isDarkTheme = settingViewModel.isDarkTheme,
                    navigate = { navigate(it) },
                    onClick = { prefix, route->
                        invPatViewModel.restInvPatrimoine()
                        selectPatrimoineVM.resetSelectedPatrimoineArticles()

                        mainViewModel.generateCodeM(
                            utilisateur = utilisateur,
                            prefix = prefix
                        )
                        navigate(route)
                    },
                    padding = padding,
                    navigationDrawerViewModel = navigationDrawerViewModel,
                    syncInvBatimentViewModel = syncInvBatimentViewModel,
                    batimentViewModel = batimentViewModel,
                    barCodeViewModel = barCodeViewModel,
                    showAddCBBatiment = showAddCBBatiment,
                    zoneConsomationCB = zoneConsomationCB,
                    societe = societe,
                    siteFinacier = siteFinacier,
                    siteReception = siteReception,
                    selectedZoneConsomation = selectedZoneConsomation,
                    affectCodeBareBatimentState = affectCodeBareBatimentState
                )

            }
            else -> {
                ZoneConsomationDetailColumnView(
                    haveCameraDevice = haveCameraDevice,
                    isDarkTheme = isDarkTheme,
                    navigate = { navigate(it) },
                    generateCodeM = { prefix, route->
                    invPatViewModel.restInvPatrimoine()
                    selectPatrimoineVM.resetSelectedPatrimoineArticles()

                    mainViewModel.generateCodeM(
                        utilisateur = utilisateur,
                        prefix = prefix
                    )
                    navigate(route)
                },
                padding = padding,
                navigationDrawerViewModel = navigationDrawerViewModel,
                syncInvBatimentViewModel = syncInvBatimentViewModel,
                batimentViewModel = batimentViewModel,
                barCodeViewModel = barCodeViewModel,
                showAddCBBatiment = showAddCBBatiment,
                zoneConsomationCB = zoneConsomationCB,
                societe = societe,
                siteFinacier = siteFinacier,
                siteReception = siteReception,
                selectedZoneConsomation = selectedZoneConsomation,
                affectCodeBareBatimentState = affectCodeBareBatimentState
                )
            }
        }













    }
}








