package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.remote.api

import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executeDeleteApiCall
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.ResponseAddBatchVisiteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow

class DistributionNumeriqueApiImpl(private val client: HttpClient) : DistributionNumeriqueApi {


    override suspend fun getAllTypeService(baseConfig: String): Flow<DataResult<List<TypeServicesDn>>> = flow {
        val result = executePostApiCall<List<TypeServicesDn>>(
            client = client,
            endpoint = Urls.GET_ALL_TYPE_SERVICE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getAllFamille(baseConfig: String): Flow<DataResult<List<FamilleDn>>> = flow {

        val result = executePostApiCall<List<FamilleDn>>(
            client = client,
            endpoint = Urls.GET_ALL_FAMILLE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getAllSuperficie(baseConfig: String): Flow<DataResult<List<SuperficieDn>>> = flow {

        val result = executePostApiCall<List<SuperficieDn>>(
            client = client,
            endpoint = Urls.GET_ALL_SUPERFICIE,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getAllTypePVente(baseConfig: String): Flow<DataResult<List<TypePointVenteDn>>> = flow {

        val result = executePostApiCall<List<TypePointVenteDn>>(
            client = client,
            endpoint = Urls.GET_ALL_TYPE_P_VENTE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getAllVisiteByUser(baseConfig: String): Flow<DataResult<List<VisitesDn>>> = flow {

        /*url {
                parameters.append("user", Globals.USER_ID)
            }

         */
        val queryParams = mapOf("user" to Globals.USER_ID)

        val result = executePostApiCall<List<VisitesDn>>(
            client = client,
            endpoint = Urls.GET_ALL_VISITE,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getAllLigneVisite(baseConfig: String): Flow<DataResult<List<LigneVisitesDn>>> = flow {
        val queryParams = mapOf("user" to Globals.USER_ID)
        val result = executePostApiCall<List<LigneVisitesDn>>(
            client = client,
            endpoint = Urls.GET_ALL_LIGNE_VISITE,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getVisiteByCode(baseConfig: String): Flow<DataResult<LigneVisitesDn>> = flow {

        val queryParams = mapOf("user" to Globals.USER_ID)
        val result = executePostApiCall<LigneVisitesDn>(
            client = client,
            endpoint = Urls.GET_VISITE_BY_CODE,
            queryParams = queryParams,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchVisite(baseConfig: String): Flow<DataResult<List<ResponseAddBatchVisiteDn>>> = flow {

        val result = executePostApiCall<List<ResponseAddBatchVisiteDn>>(
            client = client,
            endpoint = Urls.ADD_BATCH_VISITE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun deleteVisite(baseConfig: String): Flow<DataResult<List<ResponseAddBatchVisiteDn>>> = flow {
        val result = executeDeleteApiCall<List<ResponseAddBatchVisiteDn>>(
            client = client,
            endpoint = Urls.DELETE_VISITE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }


}