package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class BonRetourWithClient(
    @Embedded
    @SerialName("bonRetour")
    var bonRetour: BonRetour? = null,

    @Relation(
        parentColumn = "BOR_codefrs",
        entityColumn = "CLI_Code"
    )
    @SerialName("client")
    var client: Client? = null

    )
