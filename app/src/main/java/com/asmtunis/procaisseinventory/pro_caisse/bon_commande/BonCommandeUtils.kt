package com.asmtunis.procaisseinventory.pro_caisse.bon_commande

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle

object BonCommandeUtils {

    fun bcTobl(
        bonCommandeWithClient: BonCommandeWithClient,
        lgBonCommandeWithArticle: List<LigneBonCommandeWithArticle>,
        setTableList: (lgBCWithArticle: LigneBonCommandeWithArticle) -> Unit,
        setModify: () -> Unit
    ) {
        if (bonCommandeWithClient.bonCommande != BonCommande()) {
            for (lgBonCommande in lgBonCommandeWithArticle) {
                setTableList(lgBonCommande)
            }

            setModify()
        }
    }
}