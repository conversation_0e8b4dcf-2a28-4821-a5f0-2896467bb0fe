package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.TYPE_SERVICE_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import kotlinx.coroutines.flow.Flow


@Dao
interface TypeServicesDAO {
    @get:Query("SELECT * FROM $TYPE_SERVICE_TABLE")
    val all: Flow<List<TypeServicesDn>>

    //LiveData<List<DNTypeServices>> getAll();
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(DNtypeServices: List<TypeServicesDn>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(DNtypeService: TypeServicesDn)





    @Query("SELECT * FROM $TYPE_SERVICE_TABLE WHERE CodeTypeSv = :codeTypeSv")
    fun getbyCode(codeTypeSv: String?): TypeServicesDn?


    @Query("delete from $TYPE_SERVICE_TABLE")
    fun deleteAll()


}
