package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.dao.LigneBonCommandeDAO
import kotlinx.coroutines.flow.Flow


class LigneBonCommandeLocalRepositoryImpl(
        private val ligneBonCommandeDAO: LigneBonCommandeDAO
    ) : LigneBonCommandeLocalRepository {
    override fun upsertAll(value: List<LigneBonCommande>)  = ligneBonCommandeDAO.insertAll(value)

    override fun upsert(value: LigneBonCommande) = ligneBonCommandeDAO.insert(value)
    override fun setSynced(newNum: String, oldNum: String) = ligneBonCommandeDAO.setSynced(newNum, oldNum)
    override fun setToInserted(codeM: String) = ligneBonCommandeDAO.setToInserted(codeM = codeM)

    override fun deletebyCodeMAndArtCode(codeM: String, artCode: String) = ligneBonCommandeDAO.deleteByCodeMAndArtCode(codeM, artCode)
    override fun deleteByCodeM(codeM: String) = ligneBonCommandeDAO.deleteByCodeM(codeM)
    override fun deleteByLgDevNumBon(code: String) = ligneBonCommandeDAO.deleteByLgDevNumBon(code = code)

    override fun deleteAll() = ligneBonCommandeDAO.deleteAll()

    override fun getAll(): Flow<List<LigneBonCommande>> = ligneBonCommandeDAO.all
}