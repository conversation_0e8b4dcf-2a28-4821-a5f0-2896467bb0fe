package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.ui

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.AutreViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.NewProductViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.PrixViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.PromotionViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.VeilleConcurentielViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VcFilterContainer(
    vcViewModel: VeilleConcurentielViewModel,
    newProdViewModel: NewProductViewModel,
    promotionViewModel: PromotionViewModel,
    prixViewModel: PrixViewModel,
    autreViewModel: AutreViewModel,
    currentPage: Int,
    filterListNewProduct: Array<String>,
    listFilterNewProduct: ListSearch,
    listOrderNewProduct: ListOrder,
    concurentList: List<ConcurrentVC>,
    filterListPromo: Array<String>,
    listFilterPromo: ListSearch,
    listOrderPromo: ListOrder,

    filterListPrix: Array<String>,
    listFilterPrix: ListSearch,
    listOrderPrix: ListOrder,

    filterListAutre: Array<String>,
    listFilterAutre: ListSearch,
    listOrderAutre: ListOrder,
) {
    val context = LocalContext.current

    when (currentPage) {
        0 -> {
            FilterContainer(
                filterList = filterListNewProduct,
                listFilter = listFilterNewProduct,
                listOrder = listOrderNewProduct,
                orderList = context.resources.getStringArray(R.array.new_product_vc_order),
                onShowCustomFilterChange  = {
                    vcViewModel.onShowCustomFilterChange(false)
                },
                onEvent = {
                    newProdViewModel.onEventNewProductVC(event = it)
                },
                customFilterContent= {
                    val state = newProdViewModel.newProductFilterListstate


                    FilterSectionComposable (
                        title  = stringResource(id = R.string.filter_by_type_communication),
                        currentFilterLable =  state.filterByTypeCommunication,
                        onAllEvent = {
                            newProdViewModel.onEventNewProductVC(ListEvent.SecondCustomFilter(""))
                        },
                        onEvent = {
                            newProdViewModel.onEventNewProductVC(ListEvent.SecondCustomFilter(vcViewModel.typeCommunicationVCList[it].codeTypeCom))
                        },

                        filterCount =  vcViewModel.typeCommunicationVCList.size,
                        customFilterCode  = {
                            vcViewModel.typeCommunicationVCList[it].codeTypeCom
                        },
                        filterLabel  = {
                            vcViewModel.typeCommunicationVCList[it].typeCommunication
                        }
                    )


                    FilterSectionComposable (
                        title  = stringResource(id = R.string.filter_by_concurrant),
                        currentFilterLable =  state.filterByConcurent,
                        onAllEvent = {
                            newProdViewModel.onEventNewProductVC(ListEvent.FirstCustomFilter(""))
                        },
                        onEvent = {
                            newProdViewModel.onEventNewProductVC(ListEvent.FirstCustomFilter(concurentList[it].codeconcurrent))
                        },

                        filterCount =  concurentList.size,
                        customFilterCode  = {
                            concurentList[it].codeconcurrent
                        },
                        filterLabel  = {
                            concurentList[it].concurrent
                        }
                    )

                }
            )
        }
        1 -> {
            FilterContainer(
                filterList = filterListPromo,
                listFilter = listFilterPromo,
                listOrder = listOrderPromo,
                orderList = context.resources.getStringArray(R.array.promo_vc_order),
                onShowCustomFilterChange  = {
                    vcViewModel.onShowCustomFilterChange(false)
                },
                onEvent = {
                    promotionViewModel.onEventPromoVC(event = it)
                },
                customFilterContent= {
                    val state = promotionViewModel.promotionVCListstate
                    FilterSectionComposable (
                        title  = stringResource(id = R.string.filter_by_type_communication),
                        currentFilterLable =  state.filterByTypeCommunication,
                        onAllEvent = {
                            promotionViewModel.onEventPromoVC(ListEvent.SecondCustomFilter(""))
                        },
                        onEvent = {
                            promotionViewModel.onEventPromoVC(ListEvent.SecondCustomFilter(vcViewModel.typeCommunicationVCList[it].codeTypeCom))
                        },

                        filterCount =  vcViewModel.typeCommunicationVCList.size,
                        customFilterCode  = {
                            vcViewModel.typeCommunicationVCList[it].codeTypeCom
                        },
                        filterLabel  = {
                            vcViewModel.typeCommunicationVCList[it].typeCommunication
                        }
                    )


                }
            )

        }
        2 -> {
            FilterContainer(
                filterList = filterListPrix,
                listFilter = listFilterPrix,
                listOrder = listOrderPrix,
                orderList = context.resources.getStringArray(R.array.promo_vc_order),
                onShowCustomFilterChange  = {
                    vcViewModel.onShowCustomFilterChange(false)
                },
                onEvent = {
                    prixViewModel.onEventPrixVC(event = it)
                },
                customFilterContent= {
                    val state = prixViewModel.prixVCListstate

                    FilterSectionComposable (
                        title  = stringResource(id = R.string.filter_by_type_communication),
                        currentFilterLable =  state.filterByTypeCommunication,
                        onAllEvent = {
                            prixViewModel.onEventPrixVC(ListEvent.SecondCustomFilter(""))
                        },
                        onEvent = {
                            prixViewModel.onEventPrixVC(ListEvent.SecondCustomFilter(vcViewModel.typeCommunicationVCList[it].codeTypeCom))
                        },

                        filterCount =  vcViewModel.typeCommunicationVCList.size,
                        customFilterCode  = {
                            vcViewModel.typeCommunicationVCList[it].codeTypeCom
                        },
                        filterLabel  = {
                            vcViewModel.typeCommunicationVCList[it].typeCommunication
                        }
                    )

                }
            )







        }
        3 -> {
            FilterContainer(
                filterList = filterListAutre,
                listFilter = listFilterAutre,
                listOrder = listOrderAutre,
                orderList = context.resources.getStringArray(R.array.autre_vc_order),
                onShowCustomFilterChange  = {
                    vcViewModel.onShowCustomFilterChange(false)
                },
                onEvent = {
                    autreViewModel.onEventAutreVC(event = it)
                },
                customFilterContent= {
                    val state = autreViewModel.autreListstate


                    FilterSectionComposable (
                        title = stringResource(id = R.string.filter_by_concurrant),
                        currentFilterLable = state.filterByConcurent,
                        onAllEvent = {
                            autreViewModel.onEventAutreVC(ListEvent.FirstCustomFilter(""))
                        },
                        onEvent = {
                            autreViewModel.onEventAutreVC(ListEvent.FirstCustomFilter(concurentList[it].codeconcurrent))
                        },

                        filterCount = concurentList.size,
                        customFilterCode  = {
                            concurentList[it].codeconcurrent
                        },
                        filterLabel  = {
                            concurentList[it].concurrent
                        }
                    )


                    FilterSectionComposable (
                        title = stringResource(id = R.string.filter_by_type_communication),
                        currentFilterLable =  state.filterByTypeCommunication,
                        onAllEvent = {
                            autreViewModel.onEventAutreVC(ListEvent.SecondCustomFilter(""))
                        },
                        onEvent = {
                            autreViewModel.onEventAutreVC(ListEvent.SecondCustomFilter(vcViewModel.typeCommunicationVCList[it].codeTypeCom))
                        },

                        filterCount = vcViewModel.typeCommunicationVCList.size,
                        customFilterCode  = {
                            vcViewModel.typeCommunicationVCList[it].codeTypeCom
                        },
                        filterLabel  = {
                            vcViewModel.typeCommunicationVCList[it].typeCommunication
                        }
                    )
                }
            )
        }
    }
}