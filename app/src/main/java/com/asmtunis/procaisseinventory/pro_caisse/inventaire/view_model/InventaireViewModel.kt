package com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.currentMonth
import com.asmtunis.procaisseinventory.core.Globals.currentYear
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.filter.InventairePatrimoineFilterListState
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@HiltViewModel
class InventaireViewModel @Inject constructor(
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork
) : ViewModel() {

    var inventaireByCodeM: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    fun onGetInventaireByCodeM(codeM: String) {
        viewModelScope.launch(ioDispatcher) {
            proCaisseLocalDb.invePatrimoine.getByCodeM(codeM).collect {
                inventaireByCodeM = it?: emptyMap()
            }
        }

    }



    var selectedMonth by mutableStateOf(currentMonth)
        private set

    fun onMonthChange(value: String) {
        selectedMonth = value
    }

    var selectedYear by mutableStateOf(currentYear.toString())
        private set

    fun onYearChange(value: String) {
        selectedYear = value
    }


    var showAlertDialog by mutableStateOf(Pair(false, SelectedPatrimoine()))
        private set

    fun onShowAlertDialogChange(value: Pair<Boolean, SelectedPatrimoine>) {
        showAlertDialog = value
    }


    var showDestination by mutableStateOf(true)
        private set

    fun onShowDestinationChange(value: Boolean) {
        showDestination = value
    }


    var marqueExpanded by mutableStateOf(false)
        private set

    fun onMarqueExpandedChange(value: Boolean) {
        marqueExpanded = value
    }


    var typeMouvementExpanded by mutableStateOf(false)
        private set

    fun onTypeMouvementChange(value: Boolean) {
        typeMouvementExpanded = value
    }


    var selectedTypeMouvement: TypeMouvement by mutableStateOf(TypeMouvement())
        private set

    fun onSelectedTypeMouvementChange(value: TypeMouvement) {
        selectedTypeMouvement = value
    }

    fun saveInvPat(
        idStationOrigine: String = "",
        deleteLgAndImages: Boolean = false,
        status: String = ItemStatus.INSERTED.status,
        listSelectedPatrimoine: List<SelectedPatrimoine>,
        articleMapByBarCode: Map<String, Article>,
        codeM: String,
        exercice: String,
        client: Client = Client(),
        selectedZoneConsomation: Immobilisation = Immobilisation(),
        utilisateur: Utilisateur,
        typeInv: String,
        devEtat: String,
        destinationSociete: Immobilisation = Immobilisation(),
        destinationSiteFinancier: Immobilisation = Immobilisation(),
        selectedDeplacementOutByUser: DeplacementOutByUserWithImmobilisation = DeplacementOutByUserWithImmobilisation(),
        onComplete : () -> Unit = {}
    ) {
        viewModelScope.launch(ioDispatcher) {

            if(listSelectedPatrimoine.isEmpty()) {
                viewModelScope.launch(mainDispatcher) {
                    onComplete()
                }
                return@launch
            }
            val date = getCurrentDateTime()
            if (deleteLgAndImages) {
                deleteExistingInventoryData(codeM = codeM)
            }

            saveInvPat(
                idStationOrigine = idStationOrigine,
                status = status,
                exercice = exercice,
                client = client,
                selectedZoneConsomation = selectedZoneConsomation,
                utilisateur = utilisateur,
                typeInv = typeInv,
                devEtat = devEtat,
                destinationSociete = destinationSociete,
                destinationSiteFinancier = destinationSiteFinancier,
                codeM = codeM,
                date = date

            )

            saveLgInvPat(
                status = status,
                articleMapByBarCode = articleMapByBarCode,
                listSelectedPatrimoine = listSelectedPatrimoine,
                exercice = exercice,
                typeInv = typeInv,
                devEtat = devEtat,
                codeM = codeM,
                selectedDeplacementOutByUser = selectedDeplacementOutByUser,
                date = date
            )

            viewModelScope.launch(mainDispatcher) {
                onComplete()
            }
        }
    }
    private suspend fun deleteExistingInventoryData(codeM: String) {
        proCaisseLocalDb.bonCommande.deleteByCodeM(codeM = codeM)
        proCaisseLocalDb.ligneBonCommande.deleteByLgDevNumBon(code = codeM)
        proCaisseLocalDb.inventairePieceJoint.deleteByDevNumNotSync(devNum = codeM)
    }

    private suspend fun saveLgInvImage(
        selectedPatrimoine: SelectedPatrimoine,
        numLgM: String,
        devEtat: String,
        codeM: String,
        date: String,
        typeInv: String,
    ) {


        val listImage = mutableListOf<ImagePieceJoint>()

        val imageList = selectedPatrimoine.imageList


        for (i in imageList.indices) {
            if (imageList[i].isSync) return
            val imagePieceJoint = ImagePieceJoint(
                codeIMG = "IMG_${numLgM}_${i + 1}",
                codeMob = numLgM,
                dateOp = date,
                image = imageList[i].image,
                cheminImg = "",
                devNum = codeM,
                typeVC = devEtat + ";" + typeInv.lowercase(Locale.ROOT),
                vcNumSerie = selectedPatrimoine.numSerie
            )

            imagePieceJoint.status = ItemStatus.INSERTED.status
            imagePieceJoint.isSync = false

            listImage.add(imagePieceJoint)


        }

        proCaisseLocalDb.inventairePieceJoint.insertAll(listImage)


    }


    private suspend fun saveLgInvPat(
        status: String,
        articleMapByBarCode: Map<String, Article>,
        listSelectedPatrimoine: List<SelectedPatrimoine>,
        exercice: String,
        devEtat: String,
        codeM: String,
        typeInv: String,
        date: String,
        selectedDeplacementOutByUser: DeplacementOutByUserWithImmobilisation = DeplacementOutByUserWithImmobilisation()

    ) {
        val listLgBonCommande = mutableListOf<LigneBonCommande>()


        listSelectedPatrimoine.forEachIndexed { index, selectedPatrimoine ->
            if(selectedPatrimoine.numSerie.isBlank()) return@forEachIndexed
            val numLg = index + 1
            val numLgM = codeM + "_$numLg"

            val article = articleMapByBarCode[selectedPatrimoine.articleCode]?: Article()
            val lgBonCommande = LigneBonCommande(
                lGDEVNumBon = codeM,
                lGDEVCodeM = numLgM,
                lGDEVExerc = exercice,
                lGDEVCodeArt = article.aRTCodeBar,  // todo verify that  all selectedPatrimoine.article != ARTICLE()
                lGDEVQte = "1.0",
                lGDEVUnite = article.uNITEARTICLECodeUnite,
                lGDEVPUHT = "0.0",
                lGDEVTva = "",
                lGDEVNetht = "",
                lGDEVRemise = "0.0",
                //  lGDEVStation = utilisateur.Station,
                //  lGDEVUser = utilisateur.Code_Ut,
                lGDEVNumOrdre = numLg,
                lGDEVTarif = "",
                lGDEVQtePiece = "",
                lGDEVExport = "",
                lGDEVDDm = date,
                lGDEVMntTTC = "0.0",
                lGDEVMntHT = "0.0",
                lGDEVPUTTC = "0.0",
                lGDEVTauxFodec = "",
                lGDEVTauxDc = "",
                lGDEVMntBrutHT = "0.0",
                lGDEVMntFodec = "",
                lGDEVMntDc = "",
                lGDEVMntTva = "0.0",
                lGDEVQteGratuite = "",
                lGDevNumSerie = selectedPatrimoine.numSerie.removeSuffix(" "),
                codeLigne = "",
                msgLigne = "",
                lGDEVCMarq = selectedPatrimoine.marqueCode,
                lgDEVNote = selectedPatrimoine.note,

                lgDevNDevIN = if (typeInv == TypePatrimoine.ENTREE.typePat) selectedDeplacementOutByUser.deplacementOutByUser?.dEVNum?: "" else ""
            )

            lgBonCommande.status = status
            lgBonCommande.isSync = false

            listLgBonCommande.add(lgBonCommande)






            if (selectedPatrimoine.imageList.isNotEmpty()) {
                saveLgInvImage(
                    selectedPatrimoine = selectedPatrimoine,
                    numLgM = numLgM,
                    devEtat = devEtat,
                    codeM = codeM,
                    typeInv = typeInv,
                    date = date
                )
            }
        }


        proCaisseLocalDb.ligneBonCommande.upsertAll(listLgBonCommande)

    }

    private suspend fun saveInvPat(
        idStationOrigine: String = "",
        status: String,
        exercice: String,
        client: Client,
        selectedZoneConsomation: Immobilisation,
        utilisateur: Utilisateur,
        typeInv: String,
        devEtat: String,
        codeM: String,
        date: String,
        destinationSociete: Immobilisation,
        destinationSiteFinancier: Immobilisation
    ) {
        val bonCommande = BonCommande(
            devCodeM = codeM,
            dEVNum = codeM,
            dEVExerc = exercice,
            dEVDate = date,
            dEVCodeClient = client.cLICode.ifEmpty { selectedZoneConsomation.cLICode },
            dEVStationOrigine = idStationOrigine.ifEmpty { utilisateur.Station },//todo see for pat (not inv pat bati) if use idStationOrigine or not
            dEVEtat = devEtat,
            dEVStation = utilisateur.Station,
            dEVUser = utilisateur.codeUt,
            dEVMntht = "0.0",
            dEVMntNetHt = "0.0",
            dEVMntTva = "0.0",
            dEVMntTTC = "0.0",
            dEVTauxRemise = "",
            dEVRemise = "",
            dEVRegler = "",
            dEVExport = "",
            dEVDDm = date,
            dEVExoNum = "",
            dEVExoVal = "",
            dEVTimbre = "0.0",
            dEVExonoration = "0",
            dEVChauffeur = "",
            dEVVehicule = "",
            dEVObservation = "",
            // dEVClient = client.cLICode,
            dEVClientName = selectedZoneConsomation.cLINomPren,
            dEVMntFodec = "0.0",
            dEVMntDC = "0.0",
            dEVEtatBon = "1",
            bONLIVNum = "",
            bONLIVExerc = exercice,
            code = "",
            msg = "",
            dEV_info3 = typeInv,
            dEVTyMvtCode = selectedTypeMouvement.tyMvtCode,
            devCodeSF = destinationSiteFinancier.cLICode,
            devCodeSO = destinationSociete.cLICode
        )
        bonCommande.status = status
        bonCommande.isSync = false

        proCaisseLocalDb.invePatrimoine.upsert(bonCommande)
    }


    var invPatListNotFiltered: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    fun getInvPatList(station: String, devEtat: String) {
        viewModelScope.launch {
            proCaisseLocalDb.invePatrimoine.getAll(station = station, devEtat = devEtat).collect {
                invPatListNotFiltered = it
            }
        }
    }


    var showCustomFilter: Boolean by mutableStateOf(false)
        private set

    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }

    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set

    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var floatingBtnIsVisible: Boolean by mutableStateOf(true)
        private set

    fun onFloatingBtnIsVisibleChange(value: Boolean) {
        floatingBtnIsVisible = value
    }


    var showSearchView: Boolean by mutableStateOf(false)
        private set

    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }

    var typeInvetaireState: String by mutableStateOf(TypePatrimoine.AFFECTATION.typePat)
        private set


    var tabsState: String by mutableStateOf(TypePat.AFFECTATION.typePat)
        private set

    fun setTabState(tabs: String, typeInv: String) {
        tabsState = tabs

    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set

    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }

    var invPatrimoineListstate: InventairePatrimoineFilterListState by mutableStateOf(InventairePatrimoineFilterListState())
        private set

    fun setInvPatrimoineListstateFilter(state: ListEvent.FirstCustomFilter) {
        invPatrimoineListstate = invPatrimoineListstate.copy(
            filterByTypeInventaire = state.firstFilter
        )
    }

    var selectedLigneInvPatrimoine: LigneBonCommande by mutableStateOf(LigneBonCommande())
        private set

    fun onSelectedLigneInvPatrimoineChange(value: LigneBonCommande) {
        selectedLigneInvPatrimoine = value
    }

    var selectedListLgInvPatrimoine = mutableStateListOf<LigneBonCommande>()
        private set
    var selectedInvPatrimoine: BonCommande by mutableStateOf(BonCommande())
        private set


    var selectedInvPatrimoineWithLigne: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    fun onSelectedInvPatrimoineChange(value: Map<BonCommande, List<LigneBonCommande>>) {
        selectedInvPatrimoineWithLigne = value
        value.forEach { (key, value) ->
            run {
                selectedInvPatrimoine = key
                selectedListLgInvPatrimoine.addAll(value)
            }
        }

    }


    fun restInvPatrimoine() {
        selectedListLgInvPatrimoine.clear()
        selectedInvPatrimoine = BonCommande()
    }

    var isLoadingFromDB: Boolean by mutableStateOf(false)
        private set
    fun onEvent(event: ListEvent, utilisateur: Utilisateur, devEtat: String) {
        when (event) {
            is ListEvent.Order -> {
                if (invPatrimoineListstate.listOrder::class == event.listOrder::class &&
                    invPatrimoineListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                invPatrimoineListstate = invPatrimoineListstate.copy(
                    listOrder = event.listOrder
                )
                filterInvPatrimoine(
                    from = "3",
                    invePatrimoineFilterListState = invPatrimoineListstate,
                    utilisateur = utilisateur,
                    devEtat = devEtat
                )
            }

            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()

            is ListEvent.ListSearch -> {
                invPatrimoineListstate = invPatrimoineListstate.copy(
                    search = event.listSearch
                )

                filterInvPatrimoine(
                    from = "4",
                    invePatrimoineFilterListState = invPatrimoineListstate,
                    utilisateur = utilisateur,
                    devEtat = devEtat
                )
            }

            is ListEvent.FirstCustomFilter -> {
                /**
                Filter in List

                 */
                /*  invPatrimoineListstate = invPatrimoineListstate.copy(
                      filterByTypeInventaire = event.firstFilter
                  )

                  filterInvPatrimoine(
                      invePatrimoineFilterListState = invPatrimoineListstate,
                      utilisateur = utilisateur
                  )*/
            }

            is ListEvent.SecondCustomFilter -> {
                //TODO IF WE ADD FILTERS
            }

            is ListEvent.ThirdCustomFilter -> TODO()
        }

    }

    /* fun filterInvPatrimoine(
         from: String,
         invePatrimoineFilterListState: InventairePatrimoineFilterListState,
         utilisateur: Utilisateur,
         devEtat: String
     ) {


         viewModelScope.launch(ioDispatcher) {
             Log.d("eerrddsssss","from "+ from )
             isLoadingFromDB = true
             val searchedText = searchTextState.text
             val searchValue = invePatrimoineFilterListState.search


             if (searchedText.isEmpty()) {
                 when (invePatrimoineFilterListState.listOrder.orderType) {
                     is OrderType.Ascending -> {
                         when (invePatrimoineFilterListState.listOrder) {
                             is ListOrder.Title ->
                                 proCaisseLocalDb.invePatrimoine.getAllFiltred(
                                     station = utilisateur.Station,
                                     isAsc = 1,
                                     sortBy = "DEV_Num",
                                     devEtat = devEtat
                                 ).collect {
                                     setInvPatrimoineList(invePatrimoine = it)
                                 }

                             is ListOrder.Date ->
                                 proCaisseLocalDb.invePatrimoine.getAllFiltred(
                                     station = utilisateur.Station,
                                     isAsc = 1,
                                     sortBy = "DDmM",
                                     devEtat = devEtat

                                 ).collect {
                                     setInvPatrimoineList(invePatrimoine = it)
                                 }

                             is ListOrder.Third ->
                                 proCaisseLocalDb.invePatrimoine.getAllFiltred(
                                     station = utilisateur.Station,
                                     isAsc = 1,
                                     sortBy = "DEV_MntTTC",
                                     devEtat = devEtat

                                 ).collect {
                                     setInvPatrimoineList(invePatrimoine = it)
                                 }
                             }
                     }

                     is OrderType.Descending -> {
                         when (invePatrimoineFilterListState.listOrder) {
                             is ListOrder.Title ->
                                 proCaisseLocalDb.invePatrimoine.getAllFiltred(
                                     station = utilisateur.Station,
                                     isAsc = 2,
                                     sortBy = "DEV_Num",
                                     devEtat = devEtat
                                 ).collect {
                                     setInvPatrimoineList(invePatrimoine = it)
                             }

                             is ListOrder.Date ->
                                 proCaisseLocalDb.invePatrimoine.getAllFiltred(
                                     station = utilisateur.Station,
                                     isAsc = 2,
                                     sortBy = "DDmM",
                                     devEtat = devEtat

                                 ).collect {
                                     setInvPatrimoineList(invePatrimoine = it)
                                 }

                             is ListOrder.Third ->
                                 proCaisseLocalDb.invePatrimoine.getAllFiltred(
                                     station = utilisateur.Station,
                                     isAsc = 2,
                                     sortBy = "DEV_MntTTC",
                                     devEtat = devEtat

                                 ).collect {
                                     setInvPatrimoineList(invePatrimoine = it)
                             }
                         }
                     }
                 }
             } else {
                 if (searchedText.isNotEmpty()) {
                     if (searchValue is ListSearch.FirstSearch) {
                         when (invePatrimoineFilterListState.listOrder.orderType) {
                             is OrderType.Ascending -> {
                                 when (invePatrimoineFilterListState.listOrder) {
                                     is ListOrder.Title ->
                                         proCaisseLocalDb.invePatrimoine.filterByBonCommandeNum(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_Num",
                                             isAsc = 1,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Date ->
                                         proCaisseLocalDb.invePatrimoine.filterByBonCommandeNum(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DDmM",
                                             isAsc = 1,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Third ->
                                         proCaisseLocalDb.invePatrimoine.filterByBonCommandeNum(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_MntTTC",
                                             isAsc = 1,
                                             devEtat = devEtat
                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }
                                 }
                             }

                             is OrderType.Descending -> {
                                 when (invePatrimoineFilterListState.listOrder) {
                                     is ListOrder.Title ->
                                         proCaisseLocalDb.invePatrimoine.filterByBonCommandeNum(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_Num",
                                             isAsc = 2,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Date ->
                                         proCaisseLocalDb.invePatrimoine.filterByBonCommandeNum(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DDmM",
                                             isAsc = 2,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Third ->
                                         proCaisseLocalDb.invePatrimoine.filterByBonCommandeNum(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_MntTTC",
                                             isAsc = 2,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }
                                 }
                             }
                         }
                     }

                     if (searchValue is ListSearch.SecondSearch) {
                         when (invePatrimoineFilterListState.listOrder.orderType) {
                             is OrderType.Ascending -> {
                                 when (invePatrimoineFilterListState.listOrder) {
                                     is ListOrder.Title ->
                                         proCaisseLocalDb.invePatrimoine.filterByClient(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_Num",
                                             isAsc = 1,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Date ->
                                         proCaisseLocalDb.invePatrimoine.filterByClient(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DDmM",
                                             isAsc = 1,
                                             devEtat = devEtat
                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Third ->
                                         proCaisseLocalDb.invePatrimoine.filterByClient(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_MntTTC",
                                             isAsc = 1,
                                             devEtat = devEtat
                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }
                                 }
                             }

                             is OrderType.Descending -> {
                                 when (invePatrimoineFilterListState.listOrder) {
                                     is ListOrder.Title ->
                                         proCaisseLocalDb.invePatrimoine.filterByClient(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_Num",
                                             isAsc = 2,
                                             devEtat = devEtat
                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Date ->
                                         proCaisseLocalDb.invePatrimoine.filterByClient(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DDmM",
                                             isAsc = 2,
                                             devEtat = devEtat
                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Third ->
                                         proCaisseLocalDb.invePatrimoine.filterByClient(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_MntTTC",
                                             isAsc = 2,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }
                                 }
                             }
                         }
                     }

                     if (searchValue is ListSearch.ThirdSearch) {
                         when (invePatrimoineFilterListState.listOrder.orderType) {
                             is OrderType.Ascending -> {
                                 when (invePatrimoineFilterListState.listOrder) {
                                     is ListOrder.Title ->
                                         proCaisseLocalDb.invePatrimoine.filterByNumSerie(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_Num",
                                             isAsc = 1,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Date ->
                                         proCaisseLocalDb.invePatrimoine.filterByNumSerie(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DDmM",
                                             isAsc = 1,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Third ->
                                         proCaisseLocalDb.invePatrimoine.filterByNumSerie(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_MntTTC",
                                             isAsc = 1,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }
                                 }
                             }

                             is OrderType.Descending -> {
                                 when (invePatrimoineFilterListState.listOrder) {
                                     is ListOrder.Title ->
                                         proCaisseLocalDb.invePatrimoine.filterByNumSerie(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_Num",
                                             isAsc = 2,
                                             devEtat = devEtat
                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                         }

                                     is ListOrder.Date ->
                                         proCaisseLocalDb.invePatrimoine.filterByNumSerie(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DDmM",
                                             isAsc = 2,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }

                                     is ListOrder.Third ->
                                         proCaisseLocalDb.invePatrimoine.filterByNumSerie(
                                             station = utilisateur.Station,
                                             searchString = searchedText,
                                             sortBy = "DEV_MntTTC",
                                             isAsc = 2,
                                             devEtat = devEtat

                                         ).collect {
                                             setInvPatrimoineList(invePatrimoine = it)
                                     }
                                 }
                             }
                         }
                     }
                 }
             }
         }

     }*/
    fun filterInvPatrimoine(
        from: String,
        invePatrimoineFilterListState: InventairePatrimoineFilterListState,
        utilisateur: Utilisateur,
        devEtat: String
    ) {
        viewModelScope.launch(ioDispatcher) {
            isLoadingFromDB = true
            val searchedText = searchTextState.text
            val searchValue = invePatrimoineFilterListState.search

            val isAsc = if (invePatrimoineFilterListState.listOrder.orderType is OrderType.Ascending) 1 else 2
            val sortBy = when (invePatrimoineFilterListState.listOrder) {
                is ListOrder.Title -> "DEV_Num"
                is ListOrder.Date -> "DDmM"
                is ListOrder.Third -> "DEV_MntTTC"
            }


            val resultFlow = when {
                searchedText.isEmpty() -> proCaisseLocalDb.invePatrimoine.getAllFiltred(
                    station = utilisateur.Station,
                    isAsc = isAsc,
                    sortBy = sortBy,
                    devEtat = devEtat,
                    nbrMonth = selectedMonth,
                    year = selectedYear
                )
                searchValue is ListSearch.FirstSearch -> proCaisseLocalDb.invePatrimoine.filterByBonCommandeNum(
                    station = utilisateur.Station,
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    devEtat = devEtat,
                    nbrMonth = selectedMonth,
                    year = selectedYear
                )
                searchValue is ListSearch.SecondSearch -> proCaisseLocalDb.invePatrimoine.filterByClient(
                    station = utilisateur.Station,
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    devEtat = devEtat,
                    nbrMonth = selectedMonth,
                    year = selectedYear
                )
                searchValue is ListSearch.ThirdSearch -> proCaisseLocalDb.invePatrimoine.filterByNumSerie(
                    station = utilisateur.Station,
                    searchString = searchedText,
                    sortBy = sortBy,
                    isAsc = isAsc,
                    devEtat = devEtat,
                    nbrMonth = selectedMonth,
                    year = selectedYear
                )
                else -> null
            }
//           Pager(
//               PagingConfig(
//                   pageSize = 20,
//                   prefetchDistance = 10,
//               )
//           ) {
//               pagedData!!
//           }.flow
//               .cachedIn(viewModelScope)
//               .collectLatest { pagingData ->
//                   pagingData.map { item ->
//                       // Transform each `item` into the desired type
//                       val map = item.entries.associate { it.key to it.value }
//                       setInvPatrimoineList(invePatrimoine = map)
//                   }
//               }




            resultFlow?.collect { result ->
                setInvPatrimoineList(invePatrimoine = result)
            }
        }
    }

    var filteredDepOutList: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    var filteredDepInList: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    var filteredInventaireList: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    var filteredAffectationList: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set


    var depOutListNotFiltred: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    var depInListNotFiltred: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    var inventaireListNotFiltred: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    var affectationListNotFiltred: Map<BonCommande, List<LigneBonCommande>> by mutableStateOf(emptyMap())
        private set

    private fun setInvPatrimoineList(invePatrimoine: Map<BonCommande, List<LigneBonCommande>>) {
        invPatrimoineListstate = invPatrimoineListstate.copy(lists = invePatrimoine)

        val depOutList = mutableMapOf<BonCommande, List<LigneBonCommande>>()
        val depInList = mutableMapOf<BonCommande, List<LigneBonCommande>>()
        val inventaireList = mutableMapOf<BonCommande, List<LigneBonCommande>>()
        val affectationList = mutableMapOf<BonCommande, List<LigneBonCommande>>()

        invePatrimoine.forEach { (key, value) ->
            when (key.dEV_info3?.lowercase(Locale.ROOT)) {
                TypePatrimoine.SORTIE.typePat -> depOutList[key] = value
                TypePatrimoine.ENTREE.typePat -> depInList[key] = value
                TypePatrimoine.INVENTAIRE.typePat -> inventaireList[key] = value
                TypePatrimoine.AFFECTATION.typePat -> affectationList[key] = value
            }
        }

        filteredDepOutList = depOutList
        filteredDepInList = depInList
        filteredInventaireList = inventaireList
        filteredAffectationList = affectationList

        isLoadingFromDB = false
    }


    fun deleteInventaire() {
        viewModelScope.launch(ioDispatcher) {
            //    proCaisseLocalDb.ligneBonCommande.deleteByCodeM(codeM = selectedInvPatrimoine.devCodeM)
            proCaisseLocalDb.invePatrimoine.deleteByIdM(codeM = selectedInvPatrimoine.devCodeM)
            proCaisseLocalDb.ligneBonCommande.deleteByLgDevNumBon(code = selectedInvPatrimoine.devCodeM)
            proCaisseLocalDb.inventairePieceJoint.deleteByDevNumNotSync(devNum = selectedInvPatrimoine.devCodeM)


            showCustomModalBottomSheet = false
        }

    }


    fun setInventaireToInserted() {
        viewModelScope.launch(ioDispatcher) {
            proCaisseLocalDb.invePatrimoine.setToInserted(devNumM = selectedInvPatrimoine.devCodeM)
            proCaisseLocalDb.ligneBonCommande.setToInserted(codeM = selectedInvPatrimoine.devCodeM)
            proCaisseLocalDb.inventairePieceJoint.setToInserted(devNum = selectedInvPatrimoine.devCodeM)
        }

    }


}