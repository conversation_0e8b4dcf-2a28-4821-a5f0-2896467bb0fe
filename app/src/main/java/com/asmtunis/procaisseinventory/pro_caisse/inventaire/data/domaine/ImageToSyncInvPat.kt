package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine

import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ImageToSyncInvPat(
    @SerialName("children")
    val children: List<ImagePieceJoint>,
    @SerialName("parent")
    val parent: Parent
)