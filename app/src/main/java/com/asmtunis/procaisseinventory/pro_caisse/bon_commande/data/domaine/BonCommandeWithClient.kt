package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BonCommandeWithClient(
@Embedded
@SerialName("bonCommande")
var bonCommande: BonCommande? = null,

    @Relation(
    parentColumn = "DEV_CodeClient",
    entityColumn = "CLI_Code"
)
@SerialName("client")
var client: Client? = null,

    )
