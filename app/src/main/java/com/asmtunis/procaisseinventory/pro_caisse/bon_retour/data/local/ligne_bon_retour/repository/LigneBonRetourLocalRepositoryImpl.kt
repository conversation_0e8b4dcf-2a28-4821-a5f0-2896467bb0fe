package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.repository

import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.dao.LigneBonRetourDAO


class LigneBonRetourLocalRepositoryImpl(
        private val ligneBonRetourDAO: LigneBonRetourDAO
    ) : LigneBonRetourLocalRepository {
    override fun upsertAll(value: List<LigneBonRetour>)  = ligneBonRetourDAO.insertAll(value)

    override fun upsert(value: LigneBonRetour) = ligneBonRetourDAO.insert(value)
    override fun setSynced(newNum: String, oldNum: String) = ligneBonRetourDAO.setSynced(newNum, oldNum)
    override fun deleteByCodeM(code: String, exercice: String)  = ligneBonRetourDAO.deleteById(codeRetour = code, exercice = exercice)

    override fun deleteAll() = ligneBonRetourDAO.deleteAll()
}