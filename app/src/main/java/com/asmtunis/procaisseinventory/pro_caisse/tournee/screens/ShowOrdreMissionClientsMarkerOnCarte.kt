package com.asmtunis.procaisseinventory.pro_caisse.tournee.screens

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.res.stringResource
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.ColorsUtils
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMissionWithClient
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.compose.MarkerInfoWindow
import com.google.maps.android.compose.MarkerState

@Composable
fun ShowOrdreMissionClientsMarkerOnCarte(
    lgOrdMissionWithCoordClient: List<LigneOrdreMissionWithClient>,
    onInfoWindowClick: (LigneOrdreMissionWithClient) -> Unit,
    onInfoWindowLongClick: (LigneOrdreMissionWithClient) -> Unit,
    etatOrdreMission: List<EtatOrdreMission>,
) {


    for (ordreMissionWithLine in lgOrdMissionWithCoordClient) {


        val client: Client = ordreMissionWithLine.client ?: Client(
            cLINomPren = stringResource(id = R.string.code_client_value, ordreMissionWithLine.ligneOrdreMission.lIGORClt),
            cLIAdresse = ordreMissionWithLine.ligneOrdreMission.address
        )
        val ligneOrdreMission = ordreMissionWithLine.ligneOrdreMission
         val clientOrder = ligneOrdreMission.lIGOROrdre?: -1

        val etatOrdMission = etatOrdreMission.firstOrNull { it.codeEtatOrd == ordreMissionWithLine.ligneOrdreMission.lIGOREtat }


        val markerState = remember { MarkerState(
            LatLng(
                //client.cltLatitude?: 0.0,
                ligneOrdreMission.lIGOR_Latitude,
                ligneOrdreMission.lIGOR_Longitude
                //client.cltLongitude?: 0.0
            )
        ) }
        MarkerInfoWindow (
            //  MarkerInfoWindow(
            // draggable = true,
            // state = rememberMarkerState(position = LatLng(ordreMissionWithLine.ligneOrdreMission.lIGOR_Latitude, ordreMissionWithLine.ligneOrdreMission.lIGOR_Longitude)), //MarkerState(position = LatLng(ordreMissionWithLine.ligneOrdreMission.lIGOR_Latitude, ordreMissionWithLine.ligneOrdreMission.lIGOR_Longitude)),
            state = markerState, //MarkerState(position = LatLng(ordreMissionWithLine.ligneOrdreMission.lIGOR_Latitude, ordreMissionWithLine.ligneOrdreMission.lIGOR_Longitude)),
            title = client.cLINomPren,
            snippet = client.cLIAdresse,
            tag = client.cLICode,
            // icon = BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED),
            icon = BitmapDescriptorFactory.defaultMarker(
                if (etatOrdMission != null)
                    ColorsUtils.convertColorToHue(etatOrdMission.couleur)
                else BitmapDescriptorFactory.HUE_RED
            ),
            onClick = {
                if (!it.isInfoWindowShown) it.showInfoWindow()
                else it.hideInfoWindow()
                // false to show direction button
                false
            },
            onInfoWindowClick = { onInfoWindowClick(ordreMissionWithLine) },
            onInfoWindowLongClick = { onInfoWindowLongClick(ordreMissionWithLine) }
        ) { marker ->
            CustomMarkerInfoWindow (marker = marker, client = client, clientOrder = clientOrder)
        }
    }
}