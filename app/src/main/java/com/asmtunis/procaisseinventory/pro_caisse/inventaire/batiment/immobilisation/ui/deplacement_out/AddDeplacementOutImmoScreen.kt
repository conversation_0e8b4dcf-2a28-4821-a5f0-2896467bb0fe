package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.deplacement_out

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.Scaffold
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationRoute
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ColumnView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ItemDetailData
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.RowView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.floatingActionButtonPosition
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.custom_cards.ItemDetail
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import java.util.Locale

@Composable
fun AddDeplacementOutImmoScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel: MainViewModel,
    barCodeViewModel: BarCodeViewModel,
    cameraViewModel: CameraViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val batimentViewModel = proCaisseViewModels.batimentViewModel
    val fiterValue = selectPatrimoineVM.fiterValue
    val context = LocalContext.current
    val imageList = mainViewModel.imageList
    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)
    val selectedSocite = batimentViewModel.selectedSocite
    val selectedSiteFinancier = batimentViewModel.selectedSiteFinancier
    val utilisateur = mainViewModel.utilisateur

    val typeMouvementList = mainViewModel.typeMouvementList
    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList

    val selectedZoneConsomation = batimentViewModel.selectedZoneConsomation
    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine
    val selectedTypeMouvement = invPatViewModel.selectedTypeMouvement
    val marqueList = mainViewModel.marqueList
    val invPatByNumSerie = selectPatrimoineVM.invPatByNumSerie
    val codeM = mainViewModel.codeM

    val showDestination = invPatViewModel.showDestination
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val typeMouvementExpanded = invPatViewModel.typeMouvementExpanded
    val immobilisationTreeList = batimentViewModel.immobilisationTreeList

    val barCodeInfo = barCodeViewModel.barCodeInfo
    val haveCamera = dataViewModel.getHaveCameraDevice()
    val patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState

    // val region = immobilisationTreeList.firstOrNull { it.tyEmpImNom == Constant.REGION }
    val societe = immobilisationTreeList.firstOrNull { it.tyEmpImNom == Constant.SOCIETE }
    val siteFinacier = immobilisationTreeList.firstOrNull { it.tyEmpImNom == Constant.SITE_FINANCIER }
    val siteReception = immobilisationTreeList.firstOrNull { it.tyEmpImNom == Constant.SITE_RECEPTION }

    val tyMvtDesig = selectedTypeMouvement.tyMvtDesig

    LaunchedEffect(key1 = cameraViewModel.listImgeUri.size) {
        if (selectedPatrimoine.numSerie.isEmpty()) return@LaunchedEffect

        if (selectedPatrimoineList.first {
                it.numSerie == selectedPatrimoine.numSerie
            }.imageList.any { it.imgUrl == cameraViewModel.currentImageUri.toString() }
        ) {
            return@LaunchedEffect
        }

        /* selectPatrimoineVM.addItemToSelectedPatrimoineList(
             selectedPatrimoine.copy(imageList = cameraViewModel.listImgeUri.filter { it.devNum == selectedPatrimoine.numSerie }),
         )*/

        val combinedImageSet = mutableSetOf<ImagePieceJoint>()

        combinedImageSet.addAll(
            cameraViewModel.listImgeUri.filter {
                it.vcNumSerie == selectedPatrimoine.numSerie
            },
        )

        combinedImageSet.addAll(selectedPatrimoine.imageList)

        selectPatrimoineVM.addItemToSelectedPatrimoineList(
            selectedPatrimoine.copy(imageList = combinedImageSet.toList()),
        )
    }
    val density = LocalDensity.current

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    mainViewModel.onShowDismissScreenAlertDialogChange(true)
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,
                actions = {
                    IconButton(
                        onClick = {
                            navigate(ZoneConsomationRoute)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Home,
                            contentDescription = stringResource(
                                id = R.string.quitter
                            )
                        )
                    }
                }
            )
        },
        floatingActionButtonPosition = floatingActionButtonPosition(windowSize = windowSize),
        floatingActionButton = {
            Column {
                FloatingActionButton(
                    onClick = {
                        selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
                        selectPatrimoineVM.resetPatrimoineVerificationState()
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.cd_achat_button),
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                AnimatedVisibility(
                    visible = selectedTypeMouvement != TypeMouvement() && selectedPatrimoineList.isNotEmpty(),
                    enter =
                    slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit =
                    fadeOut(
                        animationSpec =
                        keyframes {
                            this.durationMillis = 120
                        },
                    ),
                ) {
                    FloatingActionButton(
                        onClick = {
                            val invStationOrigineIsFromUtil =
                                dataViewModel.getInventaireStationOrigineIsFromUtilisateur()
                            //val societe = batimentViewModel.immobilisationTreeList.firstOrNull { it.tyEmpImNom == SOCIETE }


                            val station = mainViewModel.stationList.firstOrNull {
                                it.sTATDesg.lowercase(Locale.ROOT) == societe?.cLINomPren?.lowercase(
                                    Locale.ROOT
                                )
                            }

                            invPatViewModel.saveInvPat(
                                articleMapByBarCode = articleMapByBarCode,
                                idStationOrigine = if (invStationOrigineIsFromUtil) utilisateur.Station else station?.sTATCode
                                    ?: "",
                                codeM = codeM,
                                listSelectedPatrimoine = selectedPatrimoineList,
                                exercice = mainViewModel.exerciceList.first().exerciceCode,
                                utilisateur = utilisateur,
                                typeInv = TypePatrimoine.SORTIE.typePat,
                                devEtat = Constants.IMMOBILISATION,
                                selectedZoneConsomation = selectedZoneConsomation,
                                destinationSociete = batimentViewModel.selectedSocite,
                                destinationSiteFinancier = batimentViewModel.selectedSiteFinancier,
                                onComplete = { navigate(ZoneConsomationRoute) }
                            )


                        },
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = stringResource(id = R.string.cd_achat_button),
                        )
                    }
                }
            }
        },
    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                navigate(ZoneConsomationDetailRoute)
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )
        CustomAlertDialogue(
            title = context.getString(R.string.delete),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = invPatViewModel.showAlertDialog.first,
            setDialogueVisibility = {
                invPatViewModel.onShowAlertDialogChange(Pair(it, SelectedPatrimoine()))
            },
            customAction = {
                selectPatrimoineVM.deleteItemToSelectedPatrimoineList(invPatViewModel.showAlertDialog.second)
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)
        )
        if (selectPatrimoineVM.showSetNumeSerie) {
            SetNumSerieView(
                haveCamera = haveCamera,
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                selectedPatrimoine = selectedPatrimoine,
                selectedPatrimoineList = selectedPatrimoineList,
                onNumSerieChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it))
                },
                onDismiss = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    selectPatrimoineVM.resetPatrimoineVerificationState()
                    selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                    selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                },
                onConfirm = {
                    val controlInvPat =
                        ControleInventaire(
                            LG_DEV_NumSerie = selectedPatrimoine.numSerie,
                            DEV_CodeClient = selectedZoneConsomation.cLICode,
                            DEV_info3 = TypePatrimoine.SORTIE.typePat,
                        )
                    selectPatrimoineVM.patrimoineVerification(
                        baseConfig = mainViewModel.selectedBaseconfig,
                        controlPatrimoine = controlInvPat,
                    )
                },
                onAddInvPat = {
                    val codeArt =
                        if (invPatByNumSerie.isNotEmpty()) {
                            invPatByNumSerie.entries.first().value.first().lGDEVCodeArt
                        } else {
                            "N/A"
                        }

                    updateInvPatQty(
                        imageList = imageList,
                        invPatByNumSerie = invPatByNumSerie,
                        articleCode = articleMapByBarCode[codeArt]?.aRTCode?: "",
                        numeSerie = selectedPatrimoine.numSerie,
                        patrimoineVerificationState = patrimoineVerificationState,
                        selectedPatrimoineList = selectedPatrimoineList,
                        addItemToSelectedPatrimoineList = {
                            selectPatrimoineVM.deleteSelectedPatrimoine(selectPatrimoineVM.backUpSelectedPatrimoine)// delete current line to insert a new one
                            selectPatrimoineVM.addItemToSelectedPatrimoineList(it)
                        },
                        marque =
                        if (invPatByNumSerie.isNotEmpty()) {
                            marqueList.firstOrNull {
                                it.mARCode == (invPatByNumSerie.entries.first().value.first().lGDEVCMarq ?: "")
                            } ?: Marque()
                        } else {
                            Marque()
                        },
                        note = selectedPatrimoine.note,
                    )
                },
                onBareCodeScan = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) },
                    )
                },
                barCodeInfo = barCodeInfo,
                patrimoineVerificationState = patrimoineVerificationState,
                showDropDownMenuComposable = false,
                onNoteChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it))
                },
            )
        }

        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {

                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.emplacement_origine),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = if (invPatViewModel.showDestination) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog,
                    onClickFirstItemDetail = { invPatViewModel.onShowDestinationChange(!invPatViewModel.showDestination) },
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(
                            it
                        )
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    },

                    extraContent = {
                        BatimentTreeView(
                            showDestination = showDestination,
                            density = density,
                            societe = societe,
                            siteReception = siteReception,
                            selectedSocite = selectedSocite,
                            selectedSiteFinancier = selectedSiteFinancier,
                            tyMvtDesig = tyMvtDesig,
                            typeMouvementExpanded = typeMouvementExpanded,
                            typeMouvementList = typeMouvementList,
                            selectedTypeMouvement = selectedTypeMouvement,
                            onGenericDropdownMenuClick = {
                                invPatViewModel.onSelectedTypeMouvementChange(it)
                                invPatViewModel.onTypeMouvementChange(false)
                            },
                            onGenericDropdownMenuItemExpandedChange = {
                                invPatViewModel.onTypeMouvementChange(it)
                            },

                            )
                    }


                )


            }

            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium -> {
                RowView (
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    padding = padding,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.emplacement_origine),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = if (invPatViewModel.showDestination) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog,
                    onClickFirstItemDetail = { invPatViewModel.onShowDestinationChange(!invPatViewModel.showDestination) },
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(
                            it
                        )
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    },
                    extraContent = {
                        BatimentTreeView(
                            showDestination = showDestination,
                            density = density,
                            societe = societe,
                            siteReception = siteReception,
                            selectedSocite = selectedSocite,
                            selectedSiteFinancier = selectedSiteFinancier,
                            tyMvtDesig = tyMvtDesig,
                            typeMouvementExpanded = typeMouvementExpanded,
                            typeMouvementList = typeMouvementList,
                            selectedTypeMouvement = selectedTypeMouvement,
                            onGenericDropdownMenuClick = {
                                invPatViewModel.onSelectedTypeMouvementChange(it)
                                invPatViewModel.onTypeMouvementChange(false)
                            },
                            onGenericDropdownMenuItemExpandedChange = {
                                invPatViewModel.onTypeMouvementChange(it)
                            }

                        )
                    }


                )

            }

            else -> {
                ColumnView (
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.emplacement_origine),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = if (invPatViewModel.showDestination) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog,
                    onClickFirstItemDetail = { invPatViewModel.onShowDestinationChange(!invPatViewModel.showDestination) },
                    onOpenVerticalalImagePagerDialogChange = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(
                            it
                        )
                    },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if (it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    },
                    extraContent = {
                        BatimentTreeView(
                            showDestination = showDestination,
                            density = density,
                            societe = societe,
                            siteReception = siteReception,
                            selectedSocite = selectedSocite,
                            selectedSiteFinancier = selectedSiteFinancier,
                            tyMvtDesig = tyMvtDesig,
                            typeMouvementExpanded = typeMouvementExpanded,
                            typeMouvementList = typeMouvementList,
                            selectedTypeMouvement = selectedTypeMouvement,
                            onGenericDropdownMenuClick = {
                                invPatViewModel.onSelectedTypeMouvementChange(it)
                                invPatViewModel.onTypeMouvementChange(false)
                            },
                            onGenericDropdownMenuItemExpandedChange = {
                                invPatViewModel.onTypeMouvementChange(it)
                            }

                            )
                    }


                )
            }
        }

    }


}

@Composable
fun BatimentTreeView(
    showDestination: Boolean,
    density: Density,
    societe: Immobilisation?,
    siteReception: Immobilisation?,
    selectedSocite: Immobilisation,
    selectedSiteFinancier: Immobilisation,
    tyMvtDesig: String,
    typeMouvementExpanded: Boolean,
    typeMouvementList: List<TypeMouvement>,
    selectedTypeMouvement: TypeMouvement,
    onGenericDropdownMenuClick: (TypeMouvement) -> Unit,
    onGenericDropdownMenuItemExpandedChange: (Boolean) -> Unit
) {
    AnimatedVisibility(
        visible = showDestination,
        enter =
        slideInVertically {
            with(density) { 40.dp.roundToPx() }
        } + fadeIn(),
        exit =
        fadeOut(
            animationSpec =
            keyframes {
                this.durationMillis = 120
            },
        ),
    ) {
        Column {
            ItemDetail(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(top = 6.dp, bottom = 6.dp),
                title = stringResource(id = R.string.societe),
                dataText = societe?.cLINomPren ?: "Null",
                icon = Icons.Default.Info
            )
            ItemDetail(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(top = 6.dp, bottom = 6.dp),
                title = stringResource(id = R.string.site_Reception),
                dataText = siteReception?.cLINomPren ?: "Null",
                icon = Icons.Default.Info
            )

            ItemDetail(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(top = 6.dp, bottom = 6.dp),
                title = stringResource(id = R.string.societe_destination),
                dataText = selectedSocite.cLINomPren,
                icon = Icons.Default.AccountBalance,
            )

            ItemDetail(
                modifier = Modifier
                    .fillMaxWidth(0.95f)
                    .padding(top = 6.dp, bottom = 6.dp),
                title = stringResource(id = R.string.site_financier_destination),
                dataText = selectedSiteFinancier.cLINomPren,
                icon = Icons.Default.AccountBalance,
            )
        }

    }


    Spacer(modifier = Modifier.height(16.dp))
    GenericDropdownMenu(
        modifier = Modifier.fillMaxWidth(0.85f),
        designation = tyMvtDesig,
        errorValue = null,
        itemList = typeMouvementList,
        label = stringResource(id = R.string.selectionne_type_mouvement),
        readOnly = true,
        getItemDesignation = { it.tyMvtDesig },
        itemExpanded = typeMouvementExpanded,
        selectedItem = selectedTypeMouvement,
        getItemTrailing = { it.tyMvtCode },
        onClick = {
            onGenericDropdownMenuClick(it)

        },
        onItemExpandedChange = {
            onGenericDropdownMenuItemExpandedChange(it)

        },
        lottieAnimEmpty = {
            LottieAnim(lotti = R.raw.emptystate)
        },
        lottieAnimError = {
            LottieAnim(lotti = R.raw.connection_error, size = it)
        }

    )

    Spacer(modifier = Modifier.height(16.dp))
}
