package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.filter

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch



data class BonCommandeFilterListState(
    val lists: Map<BonCommandeWithClient, List<LigneBonCommandeWithArticle>> = emptyMap(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch()
)