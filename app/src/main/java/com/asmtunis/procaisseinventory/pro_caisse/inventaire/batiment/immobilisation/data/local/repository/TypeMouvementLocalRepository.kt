package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import kotlinx.coroutines.flow.Flow


interface TypeMouvementLocalRepository {
        fun getAll(): Flow<List<TypeMouvement>?>

        fun insert(item: TypeMouvement)

        fun insertAll(items: List<TypeMouvement>)

        fun deleteAll()

    }