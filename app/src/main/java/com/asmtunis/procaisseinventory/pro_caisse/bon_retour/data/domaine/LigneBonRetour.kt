package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Entity(tableName = ProCaisseConstants.LIGNE_BON_RETOUR_TABLE//,
   // primaryKeys = ["NumBon_Retour", "LIG_BonEntree_CodeArt", "LIG_BonEntree_Exerc", "LIG_BonEntree_Unite"]
)

@Serializable
data class LigneBonRetour(
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @SerialName("CMP")
    val cMP: String? = "",

    @ColumnInfo(name = "LIG_BonEntree_CodeArt")
    @SerialName("LIG_BonEntree_CodeArt")
    val lIGBonEntreeCodeArt: String = "",
    @SerialName("LIG_BonEntree_CodeGroupe")
    val lIGBonEntreeCodeGroupe: String? = "",
    @SerialName("LIG_BonEntree_DDm")
    val lIGBonEntreeDDm: String = "",
    @SerialName("LIG_BonEntree_DatePerisage")
    val lIGBonEntreeDatePerisage: String? = "",

    @ColumnInfo(name = "LIG_BonEntree_Exerc")
    @SerialName("LIG_BonEntree_Exerc")
    val lIGBonEntreeExerc: String = "",
    @SerialName("LIG_BonEntree_export")
    val lIGBonEntreeExport: String = "",
    @SerialName("LIG_BonEntree_MntBrutHT")
    val lIGBonEntreeMntBrutHT: String? = "",
    @SerialName("LIG_BonEntree_MntDc")
    val lIGBonEntreeMntDc: String? = "",
    @SerialName("LIG_BonEntree_MntFodec")
    val lIGBonEntreeMntFodec: String? = "",
    @SerialName("LIG_BonEntree_MntNetHt")
    val lIGBonEntreeMntNetHt: String = "",
    @SerialName("LIG_BonEntree_MntTTC")
    val lIGBonEntreeMntTTC: String = "",
    @SerialName("LIG_BonEntree_MntTva")
    val lIGBonEntreeMntTva: String = "",
    @SerialName("LIG_BonEntree_NumOrdre")
    val lIGBonEntreeNumOrdre: String? = "",
    @SerialName("LIG_BonEntree_PUHT")
    val lIGBonEntreePUHT: String = "",
    @SerialName("LIG_BonEntree_PUTTC")
    val lIGBonEntreePUTTC: String = "",
    @SerialName("LIG_BonEntree_Qte")
    val lIGBonEntreeQte: String = "",
    @SerialName("LIG_BonEntree_QteGratuite")
    val lIGBonEntreeQteGratuite: String = "",
    @SerialName("LIG_BonEntree_QtePiece")
    val lIGBonEntreeQtePiece: String? = "",
    @SerialName("LIG_BonEntree_QteRejete")
    val lIGBonEntreeQteRejete: String = "",
    @SerialName("LIG_BonEntree_QteVendu")
    val lIGBonEntreeQteVendu: String = "",
    @SerialName("LIG_BonEntree_Remise")
    val lIGBonEntreeRemise: String = "",
    @SerialName("LIG_BonEntree_Station")
    val lIGBonEntreeStation: String = "",
    @SerialName("LIG_BonEntree_Taux")
    val lIGBonEntreeTaux: String = "",
    @SerialName("LIG_BonEntree_TauxComp")
    val lIGBonEntreeTauxComp: String = "",
    @SerialName("LIG_BonEntree_TauxDc")
    val lIGBonEntreeTauxDc: String? = "",
    @SerialName("LIG_BonEntree_TauxFodec")
    val lIGBonEntreeTauxFodec: String? = "",
    @SerialName("LIG_BonEntree_Tva")
    val lIGBonEntreeTva: String = "",
    @SerialName("LIG_BonEntree_Type")
    val lIGBonEntreeType: String? = "",


    @ColumnInfo("LIG_BonEntree_Unite")
    @SerialName("LIG_BonEntree_Unite")
    val lIGBonEntreeUnite: String = "",
    @SerialName("LIG_BonEntree_User")
    val lIGBonEntreeUser: String? = "",
    @SerialName("LIG_MargeVente")
    val lIGMargeVente: String = "",
    @SerialName("LIG_NBL_Ticket")
    val lIGNBLTicket: String? = "",
    @SerialName("LIG_PVENTEHT")
    val lIGPVENTEHT: String? = "",
    @SerialName("LIG_PrixVentePub")
    val lIGPrixVentePub: String = "",
    @SerialName("LIG_QteRetour")
    val lIGQteRetour: String? = "",
    @SerialName("LIG_QteVendue")
    val lIGQteVendue: String = "",
    @SerialName("LIG_Retour")
    val lIGRetour: String? = "",
    @SerialName("LIG_TauxEchange")
    val lIGTauxEchange: String = "",

    @ColumnInfo(name = "NumBon_Retour")
    @SerialName("NumBon_Retour")
    val numBonRetour: String = "",

    @SerialName("NumBon_Retour_M")
    val numBonRetourM: String? = "",

    @SerialName("QteAStock")
    val qteAStock: String? = ""
): BaseModel()