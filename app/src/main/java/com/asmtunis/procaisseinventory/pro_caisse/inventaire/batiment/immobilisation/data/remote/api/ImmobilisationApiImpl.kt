package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class ImmobilisationApiImpl(private val client: HttpClient) : ImmobilisationApi {
    override suspend fun getImmobilisation(baseConfig: String): Flow<DataResult<List<Immobilisation>>> = flow {

        val result = executePostApiCall<List<Immobilisation>>(
            client = client,
            endpoint = Urls.GET_IMMOBILISATION,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getBatimentByUser(baseConfig: String): Flow<DataResult<List<BatimentByUser>>> = flow {

        val result = executePostApiCall<List<BatimentByUser>>(
            client = client,
            endpoint = Urls.GET_BATIMENT_BY_USER,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getAllTypeMouvement(baseConfig: String): Flow<DataResult<List<TypeMouvement>>> = flow {

        val result = executePostApiCall<List<TypeMouvement>>(
            client = client,
            endpoint = Urls.GET_ALL_TYPE_MOUVEMENT,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}