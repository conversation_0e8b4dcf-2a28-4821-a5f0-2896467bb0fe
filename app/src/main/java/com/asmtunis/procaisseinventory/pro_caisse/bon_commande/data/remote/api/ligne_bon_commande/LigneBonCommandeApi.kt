package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.ligne_bon_commande

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.coroutines.flow.Flow


interface LigneBonCommandeApi {
    suspend fun getLigneBonCommande(baseConfig: String): Flow<DataResult<List<LigneBonCommande>>>
    suspend fun addBatchLigneCommande(baseConfig: String): Flow<DataResult<List<LigneBonCommande>>>
}