package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine

import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.InvPatBatchResponse

/**
 * Sealed class representing the different states of asset operations.
 * This enables a unidirectional data flow pattern for state management.
 */
sealed class AssetOperationState {
    /**
     * Initial state when no operation is in progress.
     */
    object Idle : AssetOperationState()

    /**
     * State representing an operation in progress.
     */
    data class Loading(val operationType: String) : AssetOperationState()

    /**
     * State representing a successful operation.
     */
    data class Success(
        val operationType: String,
        val data: List<InvPatBatchResponse>,
        val message: String? = null
    ) : AssetOperationState()

    /**
     * State representing a failed operation.
     */
    data class Error(
        val operationType: String,
        val message: String,
        val code: Int? = null
    ) : AssetOperationState()
}

/**
 * Extension function to convert RemoteResponseState to AssetOperationState
 */
fun RemoteResponseState<List<InvPatBatchResponse>>.toAssetOperationState(operationType: String): AssetOperationState {
    return when {
        loading -> AssetOperationState.Loading(operationType)
        error != null -> AssetOperationState.Error(operationType, error, null)
        data != null -> AssetOperationState.Success(operationType, data, message)
        else -> AssetOperationState.Idle
    }
}

/**
 * Data class representing the complete state of the asset operations ViewModel.
 * This consolidates all the individual state variables into a single object.
 */
data class AssetOperationsViewModelState(
    val affectationState: AssetOperationState = AssetOperationState.Idle,
    val affectationBatimentState: AssetOperationState = AssetOperationState.Idle,
    val depInState: AssetOperationState = AssetOperationState.Idle,
    val depInBatimentState: AssetOperationState = AssetOperationState.Idle,
    val depOutState: AssetOperationState = AssetOperationState.Idle,
    val depOutBatimentState: AssetOperationState = AssetOperationState.Idle,
    val inventaireState: AssetOperationState = AssetOperationState.Idle,
    val inventaireBatimentState: AssetOperationState = AssetOperationState.Idle,
    
    val affectationNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    val affectationBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    val depInNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    val depInBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    val depOutNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    val depOutBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    val inventaireNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    val inventaireBatimentNotSync: Map<BonCommande, List<LigneBonCommandeWithImageList>> = emptyMap(),
    
    val isConnected: Boolean = false,
    val autoSyncEnabled: Boolean = true
)
