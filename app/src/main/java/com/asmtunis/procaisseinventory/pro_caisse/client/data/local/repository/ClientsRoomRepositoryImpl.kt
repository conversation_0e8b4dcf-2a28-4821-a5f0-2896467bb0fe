package com.asmtunis.procaisseinventory.pro_caisse.client.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.client.data.local.dao.ClientDAO
import kotlinx.coroutines.flow.Flow

class ClientsRoomRepositoryImpl(
    private val clientDAO: ClientDAO
) : ClientRoomRepository {

    override fun upsert(value: Client) = clientDAO.insert(value)

    override fun upsertAll(value: List<Client>) = clientDAO.insertAll(value)

    override fun delete(codeclt: Client) = clientDAO.delete(codeclt)

    override fun deleteAll() = clientDAO.deleteAll()
    override fun updateSyncClient(codeM: String, code : String) = clientDAO.updateSyncClient(codeM,code)
    override fun updateSoldClient(codeClt: String, soldClient: String) = clientDAO.updateSoldClient(codeClt, soldClient)
    override fun updateMoneyClient(
        codeClt: String,
        soldClient: String,
        cliCredit: String,
        cliDebit: String
    ) = clientDAO.updateMoneyClient(
        codeClt = codeClt,
        soldClient = soldClient,
        CLICredit = cliCredit,
        CLIDebit = cliDebit
    )

    override fun getAll(): Flow<List<Client>?> = clientDAO.all
    override fun count(): Flow<Int>  = clientDAO.count()

    override fun getNotSync(): Flow<List<Client>?> = clientDAO.getNotSync
    override fun getOneByCode(code: String): Flow<Client?> = clientDAO.getOneByCode(code)

    override fun filterByName(
        filterString: String,
        sortBy: String?,
        sold: Int?,
        isAsc: Int?,
        filterType : String,
        filterByClientEtat: String
    ): Flow<List<Client>> = clientDAO.filterByName(filterString,sortBy,sold,isAsc, filterType, filterByClientEtat = filterByClientEtat)

    override fun filterByCLICode(
        filterString: String,
        sortBy: String?,
        sold: Int?,
        isAsc: Int?,
        filterType : String,
        filterByClientEtat: String
    ): Flow<List<Client>> = clientDAO.filterByCLICode(filterString,sortBy,sold,isAsc, filterType, filterByClientEtat = filterByClientEtat)

    override fun filterByCLIMat(
        filterString: String,
        sortBy: String?,
        sold: Int?,
        isAsc: Int?,
        filterType : String,
        filterByClientEtat: String
    ): Flow<List<Client>> = clientDAO.filterByCLIMat(filterString,sortBy,sold,isAsc, filterType, filterByClientEtat = filterByClientEtat)

    override fun getAllFiltred(isAsc: Int?,
                               sortBy: String?,
                               sold: Int?,
                               filterType : String,
                               filterByClientEtat: String
    ): Flow<List<Client>> = clientDAO.getAllFiltred(sortBy = sortBy,sold = sold,isAsc= isAsc,filterType= filterType, filterByClientEtat = filterByClientEtat)

}
