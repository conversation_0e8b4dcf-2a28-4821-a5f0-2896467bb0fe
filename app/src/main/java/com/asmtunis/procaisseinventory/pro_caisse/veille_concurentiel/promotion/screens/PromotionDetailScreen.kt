package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.screens

import android.Manifest
import android.app.Activity
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.twotone.Send
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.twotone.Edit
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcFormEvent
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.text_validation.VcTextValidationViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.image_pager.HorizontalImagePager
import com.asmtunis.procaisseinventory.shared_ui_components.image_pager.VerticalImagePager
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.drop_down_menu.LargeDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField
import java.util.Locale


@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PromotionDetailScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    cameraViewModel: CameraViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    vcTextValidationViewModel: VcTextValidationViewModel = hiltViewModel(),
    proCaisseViewModels: ProCaisseViewModels
) {
    val vcViewModel = proCaisseViewModels.veilleConcurentielViewModel
    val promotionViewModel = proCaisseViewModels.promotionViewModel

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val articleFilter = vcViewModel.articleFilter

    val codeM = mainViewModel.codeM
    val imageCodeM = mainViewModel.imageCodeM

    val stateAddNewVc = vcTextValidationViewModel.stateAddNewVc
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            Log.d("appDebug", "Accepted")
        } else {
            Log.d("appDebug", "Denied")
        }
    }

    val scrollState = rememberScrollState()

    val promotion = promotionViewModel.selectedPromotionWithImages.promoVC?: PromoVC()



    val listImgeUri = cameraViewModel.listImgeUri

    val typeCommunication = vcViewModel.typeCommunicationVCList


    val validationAddVcEvents = vcTextValidationViewModel.validationAddVcEvents

   val utilisateur = mainViewModel.utilisateur
   val typeCommunicationVCList = vcViewModel.typeCommunicationVCList

    val imageUriList = cameraViewModel.imageUriList
    val modify = promotionViewModel.modify

    LaunchedEffect(key1 = validationAddVcEvents) {
        promotionViewModel.handleAddVisiteEvents(
            validationAddVcEvents = validationAddVcEvents,
            popBackStack = { popBackStack() },
            utilisateur = utilisateur,
            codeM = codeM,
            saveImageList = {
                vcViewModel.saveImageList(
                    context = context,
                    userID = utilisateur.codeUt,
                    imageCodeM = imageCodeM,
                    vcCodeM = codeM,
                    imageUriList = imageUriList,
                )
            }
        )
    }

    LaunchedEffect(key1 = Unit) {
       if(modify) return@LaunchedEffect

        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.produitChanged(promotion.articleConcur))
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.prixChanged(promotion.prixConcur.toString()))
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.tauxChanged(promotion.tauxPromo.toString()))
        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.noteChanged(promotion.noteOp))

        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.typeCommunicationChanged(typeCommunicationVCList.firstOrNull { it.codeTypeCom == promotion.codeTypeCom }?: TypeCommunicationVC(codeTypeCom = promotion.codeTypeCom)))

        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.LocalProductChanged(articleMapByBarCode[promotion.codeArtLocal]?: Article(aRTDesignation = promotion.codeArtLocal?: "")))
    }




    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title =

                if (promotion.status == ItemStatus.DELETED.status) {
                    stringResource(id = R.string.visite_supprime, promotion.codeVCPromo)

                } else
                    if (promotion.codeVCPromo.isNotEmpty()) {
                        if (!modify) {
                            promotion.codeVCPromo
                        } else stringResource(id = R.string.modification, promotion.codeVCPromo)
                    } else stringResource(id = R.string.vc_promo),
            )
        },
        //    containerColor = colorResource(id = R.color.black),
        floatingActionButton = {
            FloatingActionButton(onClick = {

                if (promotion.status != ItemStatus.DELETED.status) {
                    if (!modify) {
                        promotionViewModel.onModifyChange(true)
                    } else {
                        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.SubmitAddPromotionVc)
                    }
                } else {

                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.resources.getString(R.string.deja_supprimer),
                        type =  ToastType.Info,
                    )
                }

            }) {
                Icon(
                    imageVector = if (modify) Icons.AutoMirrored.TwoTone.Send else Icons.TwoTone.Edit,
                    contentDescription = stringResource(id = R.string.cd_addVisite_button)
                )
            }
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .padding(padding)
                .verticalScroll(scrollState)
                // .fillMaxSize()
                .fillMaxWidth()
                .wrapContentHeight()
                //  .background(colorResource(id = R.color.white))
                .wrapContentSize(Alignment.Center),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            EditTextField(
                text = stateAddNewVc.produit,
                errorValue = stateAddNewVc.produitError?.asString(),
                label = stringResource(R.string.product),
                onValueChange = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.produitChanged(it))
                },
                readOnly = !modify,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )



            Row(
                modifier = Modifier.fillMaxWidth(0.95f),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                EditTextField(
                    modifier = Modifier.fillMaxWidth(0.45f),
                    text = stateAddNewVc.prix,
                    errorValue = stateAddNewVc.prixError?.asString(),
                    label = stringResource(R.string.prix),
                    onValueChange = {
                        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.prixChanged(it))
                    },
                    readOnly = !modify,
                    enabled = true,
                    leadingIcon = Icons.Default.DateRange,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )

                EditTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = stateAddNewVc.taux,
                    errorValue = stateAddNewVc.tauxError?.asString(),
                    label = stringResource(R.string.taux),
                    onValueChange = {
                        vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.tauxChanged(it))
                    },
                    readOnly = !modify,
                    enabled = true,
                    leadingIcon = Icons.Default.Home,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )


            }


            EditTextField(
                text = stateAddNewVc.note,
                errorValue = stateAddNewVc.noteError?.asString(),
                label = stringResource(R.string.note_field),
                onValueChange = {
                    vcTextValidationViewModel.onAddNewVcEvent(VcFormEvent.noteChanged(it))
                },
                readOnly = !modify,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )
              Spacer(modifier = Modifier.height(9.dp))
            GenericDropdownMenu (
                modifier = Modifier.fillMaxWidth(0.95f),
                designation = stateAddNewVc.typeCommunication.typeCommunication,
                errorValue = stateAddNewVc.typeCommunicationError?.asString(),
                label = stringResource(R.string.type_communication),
                readOnly = modify,
                itemList = typeCommunication,
                itemExpanded = vcViewModel.typeCommunicationExpanded,
                selectedItem = stateAddNewVc.typeCommunication,
                getItemTrailing = { it.typeCommunication },
                getItemDesignation = { it.codeTypeCom },
                onClick = {
                    vcTextValidationViewModel.onAddNewVcEvent(
                        VcFormEvent.typeCommunicationChanged(
                            it
                        )
                    )
                    vcViewModel.onTypeCommunicationExpandedChange(false)
                },
                onItemExpandedChange = {
                    vcViewModel.onTypeCommunicationExpandedChange(it)
                },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }
            )


            Spacer(modifier = Modifier.height(9.dp))

            LargeDropdownMenu (
                modifier = Modifier.fillMaxWidth(0.95f),
                designation =  stateAddNewVc.localProduct.aRTDesignation,
                errorValue = stateAddNewVc.localProductError?.asString(),
                fiterValue = articleFilter,
                onFilterValueChange = { vcViewModel.onArticleFilterChange(it)},
                showFilter = true,
                isSync = { it.isSync },
                label = stringResource(R.string.local_product),
                readOnly = modify,
                itemExpanded = vcViewModel.openArticleLocalExpanded,
                itemList = (if(articleFilter.isNotEmpty()) articleMapByBarCode.filter {
                    it.value.aRTDesignation.lowercase(Locale.ROOT).contains(articleFilter.lowercase(Locale.ROOT))
                } else articleMapByBarCode).values.toList(),
                selectedItem = stateAddNewVc.localProduct,
                getItemDesignation = { it.aRTDesignation },
                getItemTrailing = { it.aRTCode },
                onClick = {
                    if(!it.isSync) {
                        showToast(
                            context = context,
                            toaster = toaster,
                            message = context.resources.getString(R.string.syncbefore_use_art),
                            type =  ToastType.Info,
                        )
                        return@LargeDropdownMenu
                    }
                    vcTextValidationViewModel.onAddNewVcEvent(
                        VcFormEvent.LocalProductChanged(
                            it
                        )
                    )
                    vcViewModel.onOpenArticleLocalChange(false)
                },
                onItemExpandedChange = {
                    vcViewModel.onOpenArticleLocalChange(it)
                }
            )







            Spacer(modifier = Modifier.height(12.dp))
            HorizontalImagePager(
                onClicks = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(true) },
                canModify = modify,
                cameraViewModel = cameraViewModel,

                imageList =  listImgeUri,
                onDeleteClick = {
                  vcViewModel.onImageDeleted(it)
                    if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                }
            )

            if (cameraViewModel.openVerticalalImagePagerDialog && listImgeUri.isNotEmpty()) {
                VerticalImagePager(
                    canModify = modify,
                    onDismissRequest = {
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(false)
                    },
                    imageList = listImgeUri,
                    onDeleteClick = {
                        vcViewModel.onImageDeleted(it)
                        if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    }
                )
            }



            if (modify) {
                AskPermission(
                    permission = listOf(Manifest.permission.CAMERA),
                    permissionNotAvailableContent = { permissionState ->
                        Column(
                            modifier = Modifier
                                //  .background(colorResource(id = R.color.black))
                                .fillMaxSize(),
                            // .padding(padding),
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            LottieAnim(lotti = R.raw.emptystate)
                            Spacer(modifier = Modifier.height(16.dp))

                            val textToShow = if (permissionState.shouldShowRationale) {
                                stringResource(id = R.string.access_camera_request_permession)
                            } else {
                                stringResource(id = R.string.camera_not_available)
                            }
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(textToShow)
                            Spacer(modifier = Modifier.height(8.dp))
                            Button(onClick = { permissionState.launchMultiplePermissionRequest() }) {
                                Text(stringResource(id = R.string.request_camera_auth))
                            }
                        }
                    },
                    content = {
                        Spacer(modifier = Modifier.height(12.dp))
                        Button(
                            modifier = Modifier.wrapContentWidth(),
                            onClick = {
                                cameraViewModel.onNumChange(value = imageCodeM)

                                navigate(MainImageTiketRoute)

                            }
                        ) {

                            Text(text = if (cameraViewModel.imageUriList.isNotEmpty()) context.getString(R.string.prendreautrephotos)
                            else context.getString(R.string.prendrephotos))
                        }

                        Spacer(modifier = Modifier.height(16.dp))
                    }
                )
            }
        }
    }
}


