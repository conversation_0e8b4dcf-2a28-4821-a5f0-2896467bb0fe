package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine

import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class TicketUpdate (
    @SerialName("TIK_NumTicket")
    var tIKNumTicket : Int = 0,

    @SerialName("TIK_IdCarnet")
    var tIKIdCarnet: String? = null,

    @SerialName("TIK_Exerc")
    var tIKExerc: String? = null,

    @SerialName("TIK_NumTicket_M")
    var tIKNumTicketM: String? = null,

    @SerialName("TIK_NumeroBL")
    var tIKNumeroBL: String? = null,

    @SerialName("message")
    var message: String? = null,

    @SerialName("code")
    var code: String? = null,

    @SerialName("CodeClient")
    var codeClient: String? = null,

    @SerialName("SoldeClient")
    var soldeClient: String? = null,

    @SerialName("Debit")
    var debit: String? = null,

    @SerialName("Credit")
    var credit: String? = null,

    @SerialName("DEV_Observation")
    var observation: String? = null,

    @SerialName("facture")
    var facture: Facture? = null
)
