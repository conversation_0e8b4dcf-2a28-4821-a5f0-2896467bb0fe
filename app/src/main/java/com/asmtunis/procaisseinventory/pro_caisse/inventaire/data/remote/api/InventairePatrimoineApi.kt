package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.AddPieceJointInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControlInventaireResponse
import kotlinx.coroutines.flow.Flow


interface InventairePatrimoineApi {
    suspend fun addBatchInvPat(baseConfig: String): Flow<DataResult<List<InvPatBatchResponse>>>
    suspend fun controlInventaire(baseConfig: String): Flow<DataResult<ControlInventaireResponse>>
    suspend fun addBatchPiecJointInventaire(baseConfig: String): Flow<DataResult<List<AddPieceJointInventaire>>>
    suspend fun getPiecesJointInventaire(baseConfig: String): Flow<DataResult<List<ImagePieceJoint>>>
}