package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class PromoVCWithImages (
    @Embedded
    @SerialName("PromoVC")
    var promoVC: PromoVC? = null,

    @Relation(
        parentColumn = "CodeVCPromoM",
        entityColumn = "Code_TypeVC"
    )
    @SerialName("ImagePieceJoint")
    var imageList: List<ImagePieceJoint>? = null,
)