package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.FAMILLE_TABLE)
@Serializable
data class FamilleDn(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "CodeFamille")
    @SerialName("CodeFamille")
    var codeFamille: String = "",

    @ColumnInfo(name = "DesgFamille")
    @SerialName("DesgFamille")
    var desgFamille: String = "",

    @ColumnInfo(name = "NoteFM")
    @SerialName("NoteFM")
    var noteFamille: String? = null,

    @ColumnInfo(name = "EtatFM")
    @SerialName("EtatFM")
    var etatFamille: Int? = null
)
