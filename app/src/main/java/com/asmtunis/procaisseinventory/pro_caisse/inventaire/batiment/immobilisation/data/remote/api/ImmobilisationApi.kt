package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import kotlinx.coroutines.flow.Flow


interface ImmobilisationApi {
    suspend fun getImmobilisation(baseConfig: String): Flow<DataResult<List<Immobilisation>>>
    suspend fun getBatimentByUser(baseConfig: String): Flow<DataResult<List<BatimentByUser>>>
    suspend fun getAllTypeMouvement(baseConfig: String): Flow<DataResult<List<TypeMouvement>>>
}