package com.asmtunis.procaisseinventory.pro_caisse.client.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow

class ClientsApiImpl(private val client: HttpClient) : ClientsApi {

    override suspend fun getClients(
        baseConfig: String,
        cltEquivalent: String?
    ): Flow<DataResult<List<Client>>> = flow {
        val result = executePostApiCall<List<Client>>(
            client = client,
            endpoint = Urls.GET_CLIENTS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addClients(baseConfig: String): Flow<DataResult<List<Client>>> = flow {
        val result = executePostApiCall<List<Client>>(
            client = client,
            endpoint = Urls.ADD_BATCH_CLIENT,
            baseConfig = baseConfig
        )
        emitAll(result)
    }
}
