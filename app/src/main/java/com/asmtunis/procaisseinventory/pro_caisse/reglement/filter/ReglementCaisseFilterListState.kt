package com.asmtunis.procaisseinventory.pro_caisse.reglement.filter

import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch


data class ReglementCaisseFilterListState(
    val lists: List<ReglementCaisseWithTicketAndClient> = emptyList(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch(),
    val filterByTypePayment: String = "",
    val filterByTypeReglement: String = ""
)