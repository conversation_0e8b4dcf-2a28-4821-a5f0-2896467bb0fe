package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.BatimentsByUserDAO
import kotlinx.coroutines.flow.Flow

 
class BatimentsByUserLocalRepositoryImpl(private val batimentsDAO: BatimentsByUserDAO) :
    BatimentsByUserLocalRepository {
    override fun getAll(): Flow<List<BatimentByUser>?> = batimentsDAO.all

    override fun insert(item: BatimentByUser) = batimentsDAO.insert(item)

    override fun insertAll(items: List<BatimentByUser>) = batimentsDAO.insertAll(items)

    override fun deleteAll() = batimentsDAO.deleteAll()


    override fun getAllFiltred(isAsc: Int, sortBy: String): Flow<List<BatimentByUser>> =
        batimentsDAO.getAllFiltred(
            isAsc = isAsc,
            sortBy = sortBy
        )

    override fun filterByCltImoCB(
        filterString: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<BatimentByUser>> =
        batimentsDAO.filterByCltImoCB(
            filterString = filterString,
            sortBy = sortBy,
            isAsc = isAsc
        )

    override fun filterByCLICode(
        filterString: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<BatimentByUser>> =
        batimentsDAO.filterByCLICode(
            filterString = filterString,
            sortBy = sortBy,
            isAsc = isAsc
        )

    override fun filterByName(
        filterString: String,
        sortBy: String,
        isAsc: Int
    ): Flow<List<BatimentByUser>> =
        batimentsDAO.filterByName(
            filterString = filterString,
            sortBy = sortBy,
            isAsc = isAsc
        )


}