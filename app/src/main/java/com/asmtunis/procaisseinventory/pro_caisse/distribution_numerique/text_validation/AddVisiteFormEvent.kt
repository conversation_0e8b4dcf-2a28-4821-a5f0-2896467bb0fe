package com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.text_validation

import com.asmtunis.countrycodepicker.data.CountryData
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC


sealed class AddVisiteFormEvent {

    data class NomGerantChanged(val nomGerant: String) : AddVisiteFormEvent()
    data class NomMagasinChanged(val nomMagasin: String) : AddVisiteFormEvent()
    data class PhoneNumber1Changed(val phonenumber1: String) : AddVisiteFormEvent()

    data class CountryDataChanged(val countryData: CountryData) : AddVisiteFormEvent()


    data class AdresseChanged(val adresse: String) : AddVisiteFormEvent()

    data class GouvernoratChanged(val gouvernorat: String) : AddVisiteFormEvent()
    data class DelegationChanged(val delegation: String) : AddVisiteFormEvent()
    data class TypePtVenteChanged(val typePtVente: TypePointVenteDn) : AddVisiteFormEvent()
    data class TypeServiceChanged(val typeService: TypeServicesDn) : AddVisiteFormEvent()
    data class SuperficieChanged(val superficie: SuperficieDn) : AddVisiteFormEvent()
    data class FamilleProduitChanged(val familleProduit: FamilleDn) : AddVisiteFormEvent()
    data class LigneVisitesDnChanged(val ligneVisitesDn: List<LigneVisitesDn>) : AddVisiteFormEvent()

    data class FournisseurChanged(val fournisseur: ConcurrentVC) : AddVisiteFormEvent()


    data class LongitudeChanged(val longitude: String) : AddVisiteFormEvent()
    data class LatitudeChanged(val latitude: String) : AddVisiteFormEvent()

        data class RemarqueChanged(val remarque: String) : AddVisiteFormEvent()

    object SubmitAddVisite : AddVisiteFormEvent()
}
