package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_ALL_DEPLACEMENT_OUT_BY_USER
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUser
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class DeplacementOutByUserApiImpl(private val client: HttpClient) : DeplacementOutByUserApi {
    override suspend fun getAllDeplacememntOutByUser(baseConfig: String): Flow<DataResult<List<DeplacementOutByUser>>> = flow {
        val result = executePostApiCall<List<DeplacementOutByUser>>(
            client = client,
            endpoint = GET_ALL_DEPLACEMENT_OUT_BY_USER,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }