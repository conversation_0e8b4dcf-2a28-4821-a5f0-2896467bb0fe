package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.PROMO_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVCWithImages
import kotlinx.coroutines.flow.Flow


@Dao
interface PromoVCDAO {
    @get:Query("SELECT * FROM $PROMO_TABLE where Status !='DELETED' order by strftime('%Y-%m-%d %H-%M-%S',DateOp) desc")
    val all: Flow<List<PromoVC>>

    @Query("SELECT * FROM $PROMO_TABLE WHERE CodeVCPromoM = :code")
    fun getByCodeM(code: String?): Flow<PromoVC>

    @Query("SELECT * FROM $PROMO_TABLE WHERE CodeVCPromo = :code")
    fun getByCode(code: String?): PromoVC

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(vcPromos: List<PromoVC>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(vcPromos: PromoVC)

    @get:Query("SELECT * FROM $PROMO_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') ")
    val noSyncedToAddOrUpdate: Flow<List<PromoVC>>

    @Query("UPDATE $PROMO_TABLE SET codeVCPromo = :code , Status = 'SELECTED' , IsSync = 1 where codeVCPromoM =:codeMobile")
    fun updateCloudCode(code: String, codeMobile: String)

    @Query("UPDATE $PROMO_TABLE SET Status = 'DELETED' , IsSync = 0 where codeVCPromoM = :codeMobile  or codeVCPromo=:code")
    fun setDeleted(code: String, codeMobile: String)

    @Query("UPDATE $PROMO_TABLE SET Status = :status , IsSync = :isSync where codeVCPromoM = :code  or codeVCPromo=:code")
    fun restDeleted(code: String,status : String, isSync:Boolean)



    @get:Query("SELECT * FROM $PROMO_TABLE where isSync=0 and Status='DELETED' ")
    val noSyncedToDelete: Flow<List<PromoVC>>

    @get:Query("SELECT count(*) FROM $PROMO_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCountMubtale: Flow<Int?>?

    @get:Query("SELECT count(*) FROM $PROMO_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDeleteMubtale: Flow<Int?>?

    @get:Query("SELECT count(*) FROM $PROMO_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val countNonSync: Int

    @get:Query("SELECT count(*) FROM $PROMO_TABLE where isSync=0 and Status='DELETED' ")
    val countNoSyncedToDelete: Int

    @Query("delete from $PROMO_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $PROMO_TABLE where CodeVCPromo=:codeAutre")
    fun deleteByCode(codeAutre: String)

    @Query("DELETE FROM $PROMO_TABLE where CodeVCPromo=:codeAutre or CodeVCPromoM = :codeMobile")
    fun deleteByIdAndCodeM(codeAutre: String?, codeMobile: String?)





    @Transaction
    @Query(
        "SELECT * FROM $PROMO_TABLE " +
                "WHERE CodeVCPromo LIKE '%' || :searchString || '%' " +
                "and  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPromo END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPromo END DESC, " +

                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByNum(searchString: String, filterByTypComm : String, sortBy: String, isAsc: Int): Flow<List<PromoVCWithImages>>


    @Transaction
    @Query(
        "SELECT * FROM $PROMO_TABLE " +
                "WHERE ArticleConcur LIKE '%' || :searchString || '%' " +
                "and CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPromo END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPromo END DESC, " +

                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByArtConcurrent(searchString: String, filterByTypComm : String, sortBy: String?, isAsc: Int?): Flow<List<PromoVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $PROMO_TABLE " +
                "WHERE CodeArtLocal LIKE '%' ||  :searchString || '%' " +
                "and  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END " +




                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPromo END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPromo END DESC, " +

                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun filterByCodeArtLocal(searchString: String, sortBy: String, filterByTypComm : String, isAsc: Int): Flow<List<PromoVCWithImages>>

    @Transaction
    @Query(
        "SELECT * FROM $PROMO_TABLE " +
                "WHERE  CASE WHEN :filterByTypComm !=  '' THEN CodeTypeCom =:filterByTypComm ELSE CodeTypeCom !=:filterByTypComm END   " +


                " ORDER BY " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 1 THEN CodeVCPromo END ASC, " +
                "CASE WHEN :sortBy = 'CodeVCPromo'  AND :isAsc = 2 THEN CodeVCPromo END DESC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 1 THEN PrixConcur END ASC, " +
                "CASE WHEN :sortBy = 'PrixConcur'  AND :isAsc = 2 THEN PrixConcur END DESC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END ASC, " +
                "CASE WHEN :sortBy = 'DateOp'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',DateOp) END DESC "
    )
    fun getAllFiltred(isAsc: Int, filterByTypComm : String, sortBy: String): Flow<List<PromoVCWithImages>>

}
