package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine

import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande

/**
 * Common interface for all asset operations in the patrimoine system.
 * This enables generic handling of different asset types.
 */
interface AssetOperation {
    val typePat: String
    val devEtat: String
}

/**
 * Data class representing an asset operation with its parent and children elements.
 * Used for generic handling of different asset types in sync operations.
 */
data class AssetOperationData(
    val parent: Bon<PERSON>ommand<PERSON>,
    val children: List<LigneBonCommande>,
    override val typePat: String,
    override val devEtat: String
) : AssetOperation
