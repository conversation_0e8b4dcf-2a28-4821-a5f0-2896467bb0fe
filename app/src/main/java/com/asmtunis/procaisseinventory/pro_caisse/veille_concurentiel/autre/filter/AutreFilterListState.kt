package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.filter


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVCWithImages
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch


data class AutreFilterListState(
    val lists: List<AutreVCWithImages> = emptyList(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch(),
    val filterByConcurent: String = "",
    val filterByTypeCommunication: String = ""
)