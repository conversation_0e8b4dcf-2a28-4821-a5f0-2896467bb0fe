package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.affectation

import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.twotone.SaveAs
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AjoutAffectationBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.AjoutInventaireBatimentRoute
import com.asmtunis.procaisseinventory.core.navigation.MainImageTiketRoute
import com.asmtunis.procaisseinventory.core.navigation.SelectPatrimoineRoute
import com.asmtunis.procaisseinventory.core.navigation.ZoneConsomationDetailRoute
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants.IMMOBILISATION
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.Constant.SOCIETE
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.consultation.goToInventaireBatimentScreen
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ColumnView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.ItemDetailData
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.ui.shared_ui.RowView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.AddMarqueBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.floatingActionButtonPosition
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import java.util.Locale

@Composable
fun AddAffectationImmoScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    args: AjoutAffectationBatimentRoute,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel : MainViewModel,
    cameraViewModel: CameraViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel,
    proCaisseViewModels: ProCaisseViewModels
){
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM
    val batimentViewModel = proCaisseViewModels.batimentViewModel
    val invPatViewModel = proCaisseViewModels.invPatViewModel

    val backUpSelectedPatrimoine = selectPatrimoineVM.backUpSelectedPatrimoine
    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!

    val imageList = mainViewModel.imageList

    val patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState
    val invPatList = invPatViewModel.invPatrimoineListstate.lists

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val numSerieFromInventaire = args.numSerie
    val codeInventaire = args.codeInventaire

    val haveCamera = dataViewModel.getHaveCameraDevice()
    val barCodeInfo = barCodeViewModel.barCodeInfo

    val immobilisationList = mainViewModel.immobilisationList
    val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val listImgeUri = cameraViewModel.listImgeUri

    val fiterValue = selectPatrimoineVM.fiterValue
    val utilisateur = mainViewModel.utilisateur
    val context = LocalContext.current
    val selectedZoneConsomation = batimentViewModel.selectedZoneConsomation
    val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList
    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine

    val codeM = mainViewModel.codeM

    val marqueList = mainViewModel.marqueList

    val selectedMarque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque()

    val numeSerie = selectedPatrimoine.numSerie


    val inventaireByCodeM = invPatViewModel.inventaireByCodeM

    val openVerticalalImagePagerDialog = cameraViewModel.openVerticalalImagePagerDialog

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val density = LocalDensity.current
    val articleMap = mainViewModel.articleMapByBarCode

    val typePieceFilter = selectPatrimoineVM.typePiece
    /*LaunchedEffect(key1 = Unit) {
      if (numeSerie.isEmpty())
          return@LaunchedEffect

       navigate(SelectPatrimoineRoute)
    }*/
    LaunchedEffect(key1 = Unit) {
        if(codeInventaire.isNotEmpty()) {
            invPatViewModel.onGetInventaireByCodeM(codeM = codeInventaire)
        }
        if(numSerieFromInventaire.isNotBlank()) selectPatrimoineVM.onKeepTypedNumSerieChange(true)
        selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = numSerieFromInventaire))
    }
    LaunchedEffect(key1 = listImgeUri.size) {
        if(selectedPatrimoine.numSerie.isEmpty()) return@LaunchedEffect

         if(selectedPatrimoineList.firstOrNull { it.numSerie == selectedPatrimoine.numSerie }?.imageList?.any{it.imgUrl == cameraViewModel.currentImageUri.toString()} != false)
             return@LaunchedEffect


        val combinedImageSet = mutableSetOf<ImagePieceJoint>()

        combinedImageSet.addAll(
            listImgeUri.filter {
                it.vcNumSerie == selectedPatrimoine.numSerie
            },
        )

        combinedImageSet.addAll(selectedPatrimoine.imageList)

        selectPatrimoineVM.addItemToSelectedPatrimoineList(
            selectedPatrimoine.copy(imageList = combinedImageSet.toList())
        )
    }

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    mainViewModel.onShowDismissScreenAlertDialogChange(true)

                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,
            )
        },
        floatingActionButtonPosition = floatingActionButtonPosition(windowSize = windowSize),
        floatingActionButton = {
            Column {

                FloatingActionButton(
                    onClick = {
                        selectPatrimoineVM.resetPatrimoineVerificationState()
                       navigate(SelectPatrimoineRoute)
                    }) {
                    Icon(
                        imageVector =  Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.cd_achat_button)
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
                AnimatedVisibility(
                    visible = selectedPatrimoineList.isNotEmpty(),
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {

                    FloatingActionButton(
                        onClick = {
                            selectPatrimoineVM.resetPatrimoineVerificationState()
                        val invStationOrigineIsFromUtil = dataViewModel.getInventaireStationOrigineIsFromUtilisateur()
                        val societe = batimentViewModel.immobilisationTreeList.firstOrNull { it.tyEmpImNom == SOCIETE }

                            val station = mainViewModel.stationList.firstOrNull { it.sTATDesg.lowercase(Locale.ROOT) == societe?.cLINomPren?.lowercase(Locale.ROOT)}

                            invPatViewModel.saveInvPat(
                                   articleMapByBarCode = articleMapByBarCode,
                                    idStationOrigine = if(invStationOrigineIsFromUtil) utilisateur.Station else station?.sTATCode?:"",
                                    listSelectedPatrimoine = selectedPatrimoineList,
                                    exercice = mainViewModel.exerciceList.first().exerciceCode,
                                    utilisateur = utilisateur,
                                    typeInv = TypePatrimoine.AFFECTATION.typePat,
                                    devEtat = IMMOBILISATION,
                                    selectedZoneConsomation = selectedZoneConsomation,
                                    codeM = codeM,
                                    onComplete = {
                                    if(numSerieFromInventaire.isEmpty() || codeInventaire.isEmpty()) {
                                        navigate(ZoneConsomationDetailRoute)
                                    }
                                    else {
                                        val listBonCommande: MutableList<BonCommande> = arrayListOf()
                                        val ligneBonCommande: MutableList<LigneBonCommande> = arrayListOf()

                                        inventaireByCodeM.forEach { (key, value) ->
                                            run {
                                                listBonCommande.add(key)
                                                ligneBonCommande.addAll(value)
                                            }
                                        }

                                        goToInventaireBatimentScreen(
                                            navigate = { navigate(it) },
                                            setCodM = { mainViewModel.setCodM(it) },
                                            marqueList = marqueList,
                                            articleMap = articleMap,
                                            ligneBonCommande = ligneBonCommande,
                                            immobilisationList = immobilisationList,
                                            bonCommand = listBonCommande.firstOrNull()?: BonCommande(),
                                            invPatList = inventaireByCodeM,
                                            imageList = imageList,
                                            selectPatrimoineVM = selectPatrimoineVM,
                                            batimentViewModel = batimentViewModel,
                                            invPatrimoineViewModel = invPatViewModel,
                                            autoOpenDialog = true,
                                            preFilledNumSerie = numSerieFromInventaire
                                        )
                                    }
                                }
                                )





                        }) {
                        BadgedBox(
                            // modifier = Modifier.size(100.dp).padding(top = 12.dp),
                            badge = {
                                Badge {
                                    Text(text = selectedPatrimoineList.size.toString())
                                }
                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.SaveAs,
                                contentDescription = stringResource(id = R.string.cd_achat_button)
                            )
                        }
                    }
                }

            }

        }
    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                navigate(ZoneConsomationDetailRoute)
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )
         CustomAlertDialogue(
            title = context.getString(R.string.delete),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = invPatViewModel.showAlertDialog.first,
            setDialogueVisibility = {
                invPatViewModel.onShowAlertDialogChange(Pair(it, SelectedPatrimoine()))
            },
            customAction = {
             selectPatrimoineVM.deleteItemToSelectedPatrimoineList(invPatViewModel.showAlertDialog.second)
            },
             confirmText = stringResource(id = R.string.oui),
             cancelText = stringResource(id = R.string.non)
        )

        if(mainViewModel.addNewMarqueIsVisible) {
            AddMarqueBottomSheet(
                toaster = toaster,
                marqueTxt = selectPatrimoineVM.marqueTxt,
                onMarqueTextChange = {
                    selectPatrimoineVM.onMarqueTextChange(it)
                },
                setVisibilty = {
                    mainViewModel.onAddNewMarqueVisibilityChange(it)
                },
                onSaveClick = {
                    mainViewModel.addNewMarque(designationMarque = selectPatrimoineVM.marqueTxt)
                    selectPatrimoineVM.onMarqueTextChange("")
                }
            )
        }


        if (selectPatrimoineVM.showSetNumeSerie) {
            SetNumSerieView(
                haveCamera = haveCamera,
                articleMapByBarCode = articleMapByBarCode,
                marqueList = marqueList,
                selectedPatrimoine = selectedPatrimoine,
                backUpSelectedPatrimoine = backUpSelectedPatrimoine,
                selectedPatrimoineList = selectedPatrimoineList,
                barCodeInfo = barCodeInfo,
                patrimoineVerificationState = patrimoineVerificationState,
                showDropDownMenuComposable = true,
                onNumSerieChange = {
                    selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it))
                },
                onDismiss = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    selectPatrimoineVM.resetPatrimoineVerificationState()
                    selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                    selectPatrimoineVM.onBackUpSelectedPatrimoineChange(SelectedPatrimoine())
                    selectPatrimoineVM.onShowSetNumeSerieChange(false)
                },
                onConfirm = {
                    val controlInvPat = ControleInventaire(
                        LG_DEV_NumSerie = numeSerie,
                        //TODO SEEN TO DELETE CLIENTS TABLE FROM INV PAT AND IMMO
                        DEV_CodeClient = mainViewModel.clientByCode.cLICode.ifEmpty { selectedZoneConsomation.cLICode },
                        DEV_info3 = invPatViewModel.typeInvetaireState
                    )
                    selectPatrimoineVM.patrimoineVerification(
                        baseConfig = mainViewModel.selectedBaseconfig,
                        controlPatrimoine = controlInvPat
                    )
                    selectPatrimoineVM.onKeepTypedNumSerieChange(false)
                },
                onAddInvPat = {
                    updateInvPatQty(
                        articleCode = selectedPatrimoine.articleCode,
                        numeSerie = numeSerie,
                        imageList = imageList,
                        patrimoineVerificationState = patrimoineVerificationState,
                        selectedPatrimoineList =  selectedPatrimoineList,
                        addItemToSelectedPatrimoineList = {
                            selectPatrimoineVM.deleteSelectedPatrimoine(backUpSelectedPatrimoine)// delete current line to insert a new one
                            selectPatrimoineVM.addItemToSelectedPatrimoineList(it)
                        },
                        marque = selectedMarque,
                        note = selectedPatrimoine.note
                    )
                },
                onBareCodeScan = {
                    openBareCodeScanner(
                        navigate = { navigate(it) },
                        onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                    )
                },

                dropDownMenuComposable = {
                    GenericDropdownMenu(
                        modifier = Modifier.fillMaxWidth(),
                        label = stringResource(R.string.type_piece),
                        designation = selectedMarque.mARDesignation,
                        itemList = if(typePieceFilter.isNotEmpty()) marqueList.filter { it.mARDesignation.lowercase(Locale.ROOT).contains(typePieceFilter.lowercase(Locale.ROOT)) } else marqueList,
                        fiterValue = typePieceFilter,
                        onFilterValueChange = { selectPatrimoineVM.onTypePieceChange(it) },
                        showFilter = true,
                        itemExpanded = invPatViewModel.marqueExpanded,
                        selectedItem = selectedMarque,
                        onItemExpandedChange = { invPatViewModel.onMarqueExpandedChange(it) },
                        getItemDesignation = { it.mARDesignation },
                        getItemSyncStatus = { it.isSync },
                        getItemTrailing = { it.mARCode },
                        onClick = {
                            selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(marqueCode = it.mARCode))

                            if(it.mARCode == Globals.AUTRE) {
                                selectPatrimoineVM.onShowSetNumeSerieChange(false)

                                mainViewModel.generteNewMarqueCode()
                                mainViewModel.onAddNewMarqueVisibilityChange(true)
                            }
                        },
                        lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                        lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
                    )
                },
                onNoteChange = { selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it)) }
            )
        }



        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact  -> {
                ColumnView(
                    articleMapByBarCode = articleMapByBarCode,
                    haveCamera = haveCamera,
                    marqueList = marqueList,
                    selectedPatrimoine = selectedPatrimoine,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(it) },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )

            }
            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium -> {
                RowView(
                    articleMapByBarCode = articleMapByBarCode,
                    haveCamera = haveCamera,
                    marqueList = marqueList,
                    selectedPatrimoine = selectedPatrimoine,
                    padding = padding,
                    firstItemDetail = ItemDetailData(
                        modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                        title = stringResource(id = R.string.zone_Consomation),
                        dataText = selectedZoneConsomation.cLINomPren,
                        icon = Icons.Default.AccountBalance,
                        tint = LocalContentColor.current
                    ),
                    secondItemDetail = null,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(it) },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                    onPressTakeImage = {
                        selectPatrimoineVM.setSelectedPat(it)

                        cameraViewModel.onNumChange(value = it.numSerie)
                        navigate(MainImageTiketRoute)
                    },
                    onPressSeeImage = {
                        selectPatrimoineVM.setSelectedPat(it)
                        cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                    }


                )

            }
            else -> {
                 ColumnView(
                     articleMapByBarCode = articleMapByBarCode,
                     haveCamera = haveCamera,
                     marqueList = marqueList,
                     selectedPatrimoine = selectedPatrimoine,
                     firstItemDetail = ItemDetailData(
                         modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                         title = stringResource(id = R.string.zone_Consomation),
                         dataText = selectedZoneConsomation.cLINomPren,
                         icon = Icons.Default.AccountBalance,
                         tint = LocalContentColor.current
                     ),
                     secondItemDetail = null,
                    padding = padding,
                    selectedPatrimoineList = selectedPatrimoineList,
                    fiterValue = fiterValue,
                    showFilterLine = selectPatrimoineVM.showFilterLine,
                    openVerticalalImagePagerDialog = openVerticalalImagePagerDialog,
                    onOpenVerticalalImagePagerDialogChange = { cameraViewModel.onOpenVerticalalImagePagerDialogChange(it) },
                    onPress = {
                        selectPatrimoineVM.onBackUpSelectedPatrimoineChange(it)
                        selectPatrimoineVM.setSelectedPat(it)
                        selectPatrimoineVM.onShowSetNumeSerieChange(true)
                    },
                    onShowFilterLineChange = {
                        selectPatrimoineVM.onShowFilterLineChange(it)
                    },
                    onFilterValueChange = {
                        selectPatrimoineVM.onFilterValueChange(it)
                    },
                    onLongPress = {
                        invPatViewModel.onShowAlertDialogChange(it)
                    },
                    onDeleteClick = {
                        selectPatrimoineVM.deleteImage(images = it)
                        if(it.codeIMG == "") cameraViewModel.deleteImageTaken(it.imgUrl!!.toUri())
                    },
                     onPressTakeImage = {
                         selectPatrimoineVM.setSelectedPat(it)

                         cameraViewModel.onNumChange(value = it.numSerie)
                         navigate(MainImageTiketRoute)
                     },
                     onPressSeeImage = {
                         selectPatrimoineVM.setSelectedPat(it)
                         cameraViewModel.onOpenVerticalalImagePagerDialogChange(true)
                     }


                )
            }
        }


    }
}




