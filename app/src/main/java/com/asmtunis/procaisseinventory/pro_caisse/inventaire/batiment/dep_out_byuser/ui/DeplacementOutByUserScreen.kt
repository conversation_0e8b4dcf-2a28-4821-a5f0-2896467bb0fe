package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui

import NavDrawer
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Menu
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserDetailRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.view_model.DeplacementOutByUserViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels

@Composable
fun DeplacementOutByUserScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    deplacementOutByUserViewModel: DeplacementOutByUserViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,

    ) {
    val uiWindowState = settingViewModel.uiWindowState


    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

  val isConnected = networkViewModel.isConnected

    val allBatimentListstate = deplacementOutByUserViewModel.depOutByUserListstate








    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,

        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        DeplacementOutByUserScreenContent(
            popBackStack = { popBackStack() },
            isConnected = isConnected,
            navIcon = Icons.TwoTone.Menu,
            drawer = drawer,
            showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
            title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
            immobilisationList = mainViewModel.immobilisationList,
            deplacementOutByUserViewModel = deplacementOutByUserViewModel,
            allBatimentListstate = allBatimentListstate,
            dataViewModel = dataViewModel,
            getProCaisseDataViewModel = getProCaisseDataViewModel,
            onItemClick = { item->
                deplacementOutByUserViewModel.restSelectedDeplacementOutByUser()

                deplacementOutByUserViewModel.onSelectedDeplacementOutByUserChange(
                    allBatimentListstate.lists.filter { it.key == item },
                )

                navigate(DeplacementOutByUserDetailRoute)
            }
          )
    }
}


