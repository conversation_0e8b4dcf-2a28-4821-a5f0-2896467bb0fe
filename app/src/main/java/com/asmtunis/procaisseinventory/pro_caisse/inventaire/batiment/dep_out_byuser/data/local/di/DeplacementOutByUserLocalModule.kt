package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.dao.DeplacementOutByUserDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.repository.DeplacementOutByUserLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.repository.DeplacementOutByUserLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class DeplacementOutByUserLocalModule {

    @Provides
    @Singleton
    fun provideDeplacementOutByUserDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.deplacementOutByUserDAO()

    @Provides
    @Singleton
    @Named("DeplacementOutByUser")
    fun provideDeplacementOutByUserRepository(
        deplacementOutByUserDAO: DeplacementOutByUserDAO
    ): DeplacementOutByUserLocalRepository = DeplacementOutByUserLocalRepositoryImpl(
        deplacementOutByUserDAO = deplacementOutByUserDAO
    )


}