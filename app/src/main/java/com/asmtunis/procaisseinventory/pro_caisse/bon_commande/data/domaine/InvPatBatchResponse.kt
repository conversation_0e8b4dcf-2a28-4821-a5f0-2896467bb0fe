package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.InvPatLigneResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable



@Serializable
data class InvPatBatchResponse(
    @SerialName("code")
    val code: Int,
    @SerialName("DEV_Code_M")
    val dEVCodeM: String,
    @SerialName("DEV_Exerc")
    val dEVExerc: String,
    @SerialName("DEV_info3")
    val dEVInfo3: String?,
    @SerialName("DEV_Num")
    val dEVNum: String,
    @SerialName("DEV_Station")
    val dEVStation: String,
    @SerialName("DEV_User")
    val dEVUser: String,
    @SerialName("lignes")
    val lignes: List<InvPatLigneResponse>,
    @SerialName("message")
    val message: String
)