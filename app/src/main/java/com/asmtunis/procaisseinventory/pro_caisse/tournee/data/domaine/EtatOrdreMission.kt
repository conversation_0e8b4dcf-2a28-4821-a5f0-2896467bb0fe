package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.ETAT_ORDRE_MISSION_TABLE)
@Serializable
class EtatOrdreMission(
    
    @ColumnInfo(name = "CodeEtatOrd")
    @SerialName("CodeEtatOrd")
    @PrimaryKey var codeEtatOrd: String = "",
    
    
    @ColumnInfo(name = "LibelleEtatOrd")
    @SerialName("LibelleEtatOrd")
    var libelleEtatOrd: String = "",

    
    @ColumnInfo(name = "DescriptionEtatOrd")
    @SerialName("DescriptionEtatOrd")
    var descriptionEtatOrd: String = "",

    
    @ColumnInfo(name = "export")
    @SerialName("export")
    var export: Int = -1,

     
    @ColumnInfo(name = "DDm")
    @SerialName("DDm") 
    var dDm: String = "",
    
     
    @ColumnInfo(name = "exportM")
    @SerialName("exportM") 
    var exportM: Int = -1,
    
    
     
    @ColumnInfo(name = "DDmM")
    @SerialName("DDmM") 
    var dDmM: String = "",
    
    
    @ColumnInfo(name = "Couleur")
    @SerialName("Couleur")
    var couleur: Int = -1
)


