package com.asmtunis.procaisseinventory.pro_caisse.client

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.AddCircleOutline
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R

@Composable
fun DetailComponent(
    isDescriptionVisisble : Boolean,
    isAddVisible : Boolean = true,
    describtionText : String,
    onAddClick :()->Unit,
    onDetailClick : () ->Unit
) {

    OutlinedCard(
        modifier = Modifier
            .fillMaxWidth()
            .padding(12.dp),
        shape = RoundedCornerShape(10.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        )
    ) {

            OutlinedButton(
                enabled = isDescriptionVisisble,
                onClick = { onDetailClick() },
                //border = BorderStroke(1.dp, MaterialTheme.colorScheme.),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp)
            ) {
                Text(text = describtionText)
            }


            OutlinedButton(
                enabled = isAddVisible,
                onClick = { onAddClick() },
                modifier = Modifier.fillMaxWidth().padding(8.dp)
            ) {

                Text(text = stringResource(id = R.string.add))
                Spacer(modifier = Modifier.width(9.dp))
                Icon(
                    imageVector = Icons.TwoTone.AddCircleOutline,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
            }




    }
}



