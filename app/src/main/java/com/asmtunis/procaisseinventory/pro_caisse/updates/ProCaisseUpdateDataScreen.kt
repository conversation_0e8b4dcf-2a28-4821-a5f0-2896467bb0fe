package com.asmtunis.procaisseinventory.pro_caisse.updates

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import kotlinx.coroutines.launch


@Composable
fun ProCaisseUpdateDataScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    navDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    mainViewModel: MainViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val haveBLAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BL}
    val haveBCAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BC}


    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val utilisateur = mainViewModel.utilisateur
    val exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode ?: ""

    val isLoadingProCaisseData = UpdateLoadingStateUtils.isLoadingProCaisseData(getProCaisseDataViewModel = getProCaisseDataViewModel)
    val isLoadingSharedData = UpdateLoadingStateUtils.isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)
    val isLoadingCommenSharedData = UpdateLoadingStateUtils.isLoadingCommenSharedData(getSharedDataViewModel = getSharedDataViewModel)

    val allUpdateDataTypes = MobilityyUpdateDataType.entries.toMutableList()

    LaunchedEffect(key1 = Unit) {
        if (!haveBLAuthorisation && !haveBCAuthorisation) {
            allUpdateDataTypes.remove(MobilityyUpdateDataType.TIMBRE)

        }
    }

    val isConnected = networkViewModel.isConnected

    Scaffold(
        topBar = {
            AppBar(
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.updated_inventory),
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier.padding(padding),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Button(
                modifier = Modifier.fillMaxWidth(),
                onClick = {
                    scope.launch {
                        getProCaisseDataViewModel.getProcaisseData(
                            baseConfig = selectedBaseconfig,
                            utilisateur = utilisateur,
                            exerciceCode = exerciceCode
                        )

                        getSharedDataViewModel.getSharedData(
                            baseConfig = selectedBaseconfig,
                            utilisateur = utilisateur
                        )
                        getSharedDataViewModel.getCommenSharedData(baseConfig = selectedBaseconfig)
                    }
                },
                shape = MaterialTheme.shapes.medium,
                enabled = isConnected && !isLoadingProCaisseData && !isLoadingSharedData && !isLoadingCommenSharedData
            ) {
                if (isLoadingProCaisseData || isLoadingSharedData || isLoadingCommenSharedData)
                    LottieAnim(lotti = R.raw.loading, size = 25.dp)
                else Text(text = "Update db")
            }

            Spacer(modifier = Modifier.height(12.dp))

            LazyVerticalStaggeredGrid(
                columns = StaggeredGridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalItemSpacing = 16.dp,
                modifier = Modifier.padding(horizontal = 8.dp)
            ) {
                items(
                    count = allUpdateDataTypes.size,
                    key = {
                        allUpdateDataTypes[it].name
                    }
                ) { index ->
                    val updateType = allUpdateDataTypes[index]
                    val isLoading = UpdateLoadingStateUtils.isProcaisseUpdateLoading(
                        item = updateType,
                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                        getSharedDataViewModel = getSharedDataViewModel
                    )
                    Button(
                        onClick = {
                            UpdateLoadingStateUtils.handleProcaisseClick(
                                item = updateType,
                                exerciceCode = exerciceCode,
                                mainViewModel = mainViewModel,
                                baseConfig = selectedBaseconfig,
                                utilisateur = utilisateur,
                                getProCaisseDataViewModel = getProCaisseDataViewModel,
                                getSharedDataViewModel = getSharedDataViewModel
                            )
                        },
                        shape = MaterialTheme.shapes.medium,
                        enabled = isLoading && isConnected
                    ) {
                        if (!isLoading)
                            LottieAnim(lotti = R.raw.loading, size = 25.dp)
                        else Text(text = updateType.displayName)
                    }
                }
            }
        }
    }
}

