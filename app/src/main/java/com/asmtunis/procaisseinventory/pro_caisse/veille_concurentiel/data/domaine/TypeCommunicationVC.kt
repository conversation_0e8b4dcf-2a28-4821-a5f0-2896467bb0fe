package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.TYPE_COMMUNICATION_TABLE)
@Serializable
data class TypeCommunicationVC (
   /* @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,*/

    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "CodeTypeCom")
    @SerialName("CodeTypeCom")
    var codeTypeCom: String = "",

    @ColumnInfo(name = "TypeCommunication")
    @SerialName("TypeCommunication")
    var typeCommunication: String = "",

    @ColumnInfo(name = "NoteComm")
    @SerialName("NoteComm")
    var noteComm: String? = ""
    )
