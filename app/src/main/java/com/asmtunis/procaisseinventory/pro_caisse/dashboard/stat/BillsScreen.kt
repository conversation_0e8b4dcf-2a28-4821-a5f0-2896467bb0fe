package com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.ChevronRight
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.DashboardScreenViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.components.BaseRow
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.data.PieChartSliceData
import kotlin.math.abs

@Composable
fun BillsScreen(dashboardScreenVM: DashboardScreenViewModel) {

    val reglementPayments = dashboardScreenVM.reglementPayments
    val nbrBl = reglementPayments.nbrBl
    val mntCheque = reglementPayments.mntCheque
    val mntEspece = reglementPayments.mntEspece
    val mntTraite = reglementPayments.mntTraite

    val nombreBl = remember(nbrBl) {
        if (StringUtils.stringToDouble(nbrBl) == 0.0) 1.0 else StringUtils.stringToDouble(nbrBl)
    }

    val credit = remember(reglementPayments) {
        StringUtils.stringToDouble(reglementPayments.mntCredit) + reglementPayments.mntReglement
    }

    val regTotalMnt = remember(mntCheque, mntEspece, mntTraite) {
        mntCheque + mntEspece + mntTraite
    }


    val bills: List<PieChartSliceData> = remember(mntEspece, mntCheque, mntTraite, credit) {
        listOf(
            PieChartSliceData(
                radius = 25.dp,
                name = UiText.StringResource(resId = R.string.espece),
                amount = mntEspece,
                color = Color(0xFF2697FF)
            ),
            PieChartSliceData(
                radius = 22.dp,
                name = UiText.StringResource(resId = R.string.cheque),
                amount = mntCheque,
                color = Color(0xFF26E5FF)
            ),
            PieChartSliceData(
                radius = 16.dp,
                name = UiText.StringResource(resId = R.string.traite),
                amount = mntTraite,
                // amount = "0.0",
                color = Color(0xFFFFCF26)
            ),
            PieChartSliceData(
                radius = 13.dp,
                name = if (credit > 0) UiText.StringResource(resId = R.string.avance_label) else UiText.StringResource(
                    resId = R.string.credit_label
                ),
                amount = credit,
                color = Color(0xFFEE2727)
            )
        )
    }


    Scaffold { padding ->
        Column(
            modifier = Modifier
                .padding(padding)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            StatementBody(
                items = bills,
                circleLableTotal = regTotalMnt,
                priceFormatTotalSum = true,
                circleLabel = stringResource(R.string.payment_label),
                rows = { bill ->
                    BaseRow(
                        title = bill.name.asString() ?: "error: check string id UITEXT",
                        amount = abs(bill.amount),
                        color = bill.color,
                        showArrow = false,
                        imageVector = Icons.TwoTone.ChevronRight
                    )
                }
            )
        }
    }

}