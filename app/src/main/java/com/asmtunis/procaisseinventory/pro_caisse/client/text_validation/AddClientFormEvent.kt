package com.asmtunis.procaisseinventory.pro_caisse.client.text_validation

import com.asmtunis.countrycodepicker.data.CountryData

sealed class AddClientFormEvent {

    data class MatriculeFiscaleChanged(val matriculeFiscale: String) : AddClientFormEvent()
    data class NomChanged(val nom: String) : AddClientFormEvent()
    data class TypeClientChanged(val typeClient: String) : AddClientFormEvent()
    data class HaveCreditChanged(val haveCredit: Boolean) : AddClientFormEvent()
    data class HaveTimbreChanged(val haveTimbre: Boolean) : AddClientFormEvent()
    data class PhoneNumber1Changed(val phonenumber1: String) : AddClientFormEvent()
    data class PhoneNumber2Changed(val phonenumber2: String) : AddClientFormEvent()

    data class GouvernoratChanged(val gouvernorat: String) : AddClientFormEvent()
    data class DelegationChanged(val delegation: String) : AddClientFormEvent()

    data class AdresseChanged(val adresse: String) : AddClientFormEvent()
    data class EmailChanged(val email: String) : AddClientFormEvent()
    data class ProffessionChanged(val proffession: String) : AddClientFormEvent()
    data class NomsocieteChanged(val nomsociete: String) : AddClientFormEvent()

    data class CountryData1Changed(val countryData1: CountryData) : AddClientFormEvent()
    data class CountryData2Changed(val countryData2: CountryData) : AddClientFormEvent()

    data class LongitudeChanged(val longitude: String) : AddClientFormEvent()
    data class LatitudeChanged(val latitude: String) : AddClientFormEvent()

    object SubmitAddClient : AddClientFormEvent()
}
