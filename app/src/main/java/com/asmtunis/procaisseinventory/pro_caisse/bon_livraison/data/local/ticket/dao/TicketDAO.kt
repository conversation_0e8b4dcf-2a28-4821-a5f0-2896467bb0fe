package com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.dao

import androidx.room.*
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.LIGNE_TICKET_TABLE
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.TICKET_TABLE
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.domaine.ClientCA
import kotlinx.coroutines.flow.Flow

@Dao
interface TicketDAO {
    @Transaction
    @Query(
        "SELECT * FROM $TICKET_TABLE " +
            "LEFT JOIN $LIGNE_TICKET_TABLE ON $TICKET_TABLE.TIK_NumTicket = $LIGNE_TICKET_TABLE.LT_NumTicket" +
            " order by strftime('%Y-%m-%d %H-%M',TIK_DateHeureTicket) desc",
    )
    fun all(): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>

    @Query("SELECT   SUM(TIK_MtTTC) AS cA, TIK_CodClt as cliCode,TIK_NomClient as cliNompren from $TICKET_TABLE WHERE (TIK_IdSCaisse = :idSCaisse) AND (ifnull(TIK_Annuler, 'False') <> 'True') group by TIK_CodClt,TIK_NomClient order by SUM(TIK_MtTTC) desc LIMIT :number")
    fun getNTopClientsBySCaisse(
        idSCaisse: String?,
        number: Int,
    ): Flow<List<ClientCA>>

    @Query(
        """SELECT   SUM(TIK_MtTTC) AS CA FROM $TICKET_TABLE WHERE (TIK_IdSCaisse = :idSCaisse) AND (ifnull(TIK_Annuler, 'False') <> 'True')""",
    )
    fun getCA(idSCaisse: String): Flow<Double>

    @Query(
        "SELECT COUNT(TIK_NumTicket) AS NbreTicket FROM $TICKET_TABLE WHERE (TIK_IdSCaisse = :idSCaisse) AND (ifnull(TIK_Annuler, 'False') <> 'True')",
    )
    fun getTicketNumber(idSCaisse: String): Flow<String>

    @Query(
        "SELECT * FROM $TICKET_TABLE " +
            "JOIN $LIGNE_TICKET_TABLE ON $TICKET_TABLE.TIK_NumTicket = $LIGNE_TICKET_TABLE.LT_NumTicket " +
            "where TIK_CodClt= :codeClient and  TIK_IdSCaisse=:sCIdSCaisse" +
            " order by strftime('%Y-%m-%d %H-%M',TIK_DateHeureTicket) desc",
    )
    fun getByClient(
        codeClient: String,
        sCIdSCaisse: String,
    ): Flow<Map<Ticket, List<LigneTicket>>>

    @Query(" select ifnull(SUM(TIK_MtTTC)*(-1),0) as Mnt_Credit from $TICKET_TABLE where TIK_Etat='Credit' and TIK_IdSCaisse= :caisse")
    fun getMntCredit(caisse: String): Flow<String?>

    @Query(
        "SELECT * FROM $TICKET_TABLE where TIK_is_Contrat = 0 and TIK_Num_Contrat IS NULL and TIK_IdSCaisse= :session and TIK_station= :station order by strftime('%Y-%m-%d %H-%M-%S',TIK_DateHeureTicket) desc",
    )
    fun getAllTicketBySessionMutable(
        session: String,
        station: String,
    ): Flow<List<Ticket>>



    @Query(
        "SELECT count(*) FROM $TICKET_TABLE where TIK_is_Contrat = 0 and TIK_Num_Contrat IS NULL and TIK_IdSCaisse= :session and TIK_station= :station",
    )
    fun getAllCountBySession(
        session: String,
        station: String,
    ): Flow<Int>

    @Query(
        "SELECT count(*) FROM $TICKET_TABLE where TIK_is_Contrat = 0 and TIK_Num_Contrat IS NULL and TIK_IdSCaisse= :session and TIK_station= :station",
    )
    fun getAllCountBySessionMutable(
        session: String,
        station: String,
    ): Flow<Int>

    @Query(
        "SELECT * FROM $TICKET_TABLE where TIK_IdSCaisse=:session and (TIK_is_Contrat = 1 and TIK_Num_Contrat IS NOT NULL)  order by strftime('%Y-%m-%d %H-%M-%S',TIK_DateHeureTicket) desc",
    )
    fun getAllOrderBySession(session: String): Flow<List<Ticket>>

    @Query("SELECT * FROM $TICKET_TABLE WHERE TIK_NumTicket = :num ")
    fun getOneByCode(num: Int): Flow<Ticket>

    @Query("SELECT TIK_NumTicket FROM $TICKET_TABLE WHERE TIK_NumTicket_M = :tik_num_ticket_m ")
    fun getOneByMCode(tik_num_ticket_m: String): Flow<String>

    @Query("SELECT * FROM $TICKET_TABLE WHERE TIK_NumTicket = :num and TIK_Exerc=:exercie")
    fun getOneByCode(
        num: Int,
        exercie: String,
    ): Flow<Ticket>

    @Query("SELECT * FROM $TICKET_TABLE WHERE TIK_NumTicket_M = :num and TIK_Exerc=:exercie")
    fun getOneByCodeM(
        num: String,
        exercie: String,
    ): Flow<Ticket>

    @Query("SELECT * FROM $TICKET_TABLE WHERE TIK_NumTicket = :num ")
    fun getOneByCodeMutable(num: String): Flow<Ticket>

    @get:Query("SELECT * FROM $TICKET_TABLE WHERE  isSync=0 and (Status='INSERTED'  or Status='UPDATED') order by TIK_NumTicket asc")
    val nonSync: Flow<List<Ticket>>

    @Query(
        "SELECT * FROM $TICKET_TABLE WHERE  TIK_CodClt=:codeClient and TIK_Etat='Credit' and TIK_Annuler=0 and TIK_Exerc=:exercice order by strftime('%Y-%m-%d %H-%M',TIK_DateHeureTicket) desc",
    )
    fun getClientCredit(
        codeClient: String,
        exercice: String,
    ): Flow<List<Ticket>>

    @Query("SELECT MAX(TIK_NumTicket) + 1 FROM $TICKET_TABLE where TIK_IdCarnet= :carnet")
    fun getNewNumTicket(carnet: String): Flow<Int>

    @get:Query("SELECT count(*) FROM $TICKET_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    val noSyncCount: Flow<Int>

    /**@get:Query("SELECT * FROM $TICKET_TABLE " +
            "JOIN $LIGNE_TICKET_TABLE ON $TICKET_TABLE.TIK_NumTicket = $LIGNE_TICKET_TABLE.LT_NumTicket " +
            "where $TICKET_TABLE.isSync=0 and  ($TICKET_TABLE.Status='INSERTED'  or $TICKET_TABLE.Status='UPDATED') order by TIK_NumTicket desc")
    val notSynced: Flow<Map<Ticket, List<LigneTicket>>>*/

    //@Transaction
    @Query("SELECT * FROM $TICKET_TABLE where $TICKET_TABLE.isSync=0 and  ($TICKET_TABLE.Status='INSERTED'  or $TICKET_TABLE.Status='UPDATED') order by TIK_NumTicket desc",)
    fun notSynced(): Flow<List<TicketWithFactureAndPayments>?>



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Ticket)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Ticket>)

    @Query("UPDATE $TICKET_TABLE SET TIK_CodClt = :code_client where TIK_CodClt = :oldCodeClient")
    fun updateCodeClient(
        code_client: String,
        oldCodeClient: String,
    )

    @Query("UPDATE $TICKET_TABLE SET Syn_Error_Msg =:errorMsg where TIK_NumTicket_M = :tikNumTicketM")
    fun updateSyncErrorMsg(
        tikNumTicketM: String,
        errorMsg: String,
    )

    @Query(
        "UPDATE $TICKET_TABLE SET TIK_NumeroBL = :tikNumBl, TIK_DDm=:tikDdm, TIK_NumTicket=:tikNumTicket, isSync = 1, Status= 'SELECTED', Syn_Error_Msg ='' where TIK_NumTicket_M = :tikNumTicketM",
    )
    fun updateTicketNumber(
        tikNumBl: String,
        tikNumTicket: Int,
        tikNumTicketM: String,
        tikDdm: String,
    )

    @Query(
        "UPDATE $TICKET_TABLE SET TIK_NumTicket=:tikNumTicket, TIK_DDm=:tikDdm, isSync = 1, Status= 'SELECTED', Syn_Error_Msg ='' where TIK_NumTicket_M = :tikNumTicketM",
    )
    fun updateBLNumber(
        tikNumTicket: Int,
        tikNumTicketM: String,
        tikDdm: String,
    )
    @Query(
        "UPDATE $TICKET_TABLE SET TIK_IdSCaisse=:sessionId where TIK_NumTicket_M = :tikNumTicketM",
    )
    fun updateSessionId(
        tikNumTicketM: String,
        sessionId: String,
    )

    @Query(
        "UPDATE $TICKET_TABLE SET Status=:status where TIK_NumTicket_M = :tikNumTicketM",
    )
    fun updateTicketStatus(
        tikNumTicketM: String,
        status: String,
    )

    // @Query("DELETE FROM Ticket where Status='SELECTED'")
    //  void deleteAll();
    @Query("DELETE FROM $TICKET_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $TICKET_TABLE where TIK_NumTicket=:code and TIK_Exerc=:exercice")
    fun deleteByCode(
        code: Int,
        exercice: String,
    )

    @Query("DELETE FROM $TICKET_TABLE where TIK_NumTicket_M=:code and TIK_Exerc=:exercice")
    fun deleteByCodeM(
        code: String,
        exercice: String,
    )

    @Delete
    fun delete(ticket: Ticket)

    @Query(
        "SELECT ifnull(MAX(cast(substr(TIK_Num_Contrat,length(:prefix) + 1 ,length('TIK_Num_Contrat'))as integer)),0)+1 FROM   $TICKET_TABLE WHERE substr(TIK_Num_Contrat, 0 ,length(:prefix)+1) = :prefix",
    )
    fun getNewCode(prefix: String): Flow<String>

    @get:Query("SELECT strftime('%Y-%m-%d',TIK_DDm) FROM $TICKET_TABLE order by strftime('%Y-%m-%d %H-%M',TIK_DDm) desc limit 1")
    val dDM: Flow<String>

    @Transaction
    @Query("SELECT * FROM $TICKET_TABLE where $TICKET_TABLE.TIK_NumTicket=:numTicket")
    fun ticketWithLinesAndPaymentByNumTicket(numTicket: String): Flow<TicketWithFactureAndPayments>
    @Transaction
    @Query(
        "SELECT * FROM $TICKET_TABLE " +
            "LEFT JOIN $LIGNE_TICKET_TABLE ON $TICKET_TABLE.TIK_NumTicket = $LIGNE_TICKET_TABLE.LT_NumTicket" +
            " WHERE $TICKET_TABLE.TIK_CodClt LIKE '%' || :searchString || '%'  or  $TICKET_TABLE.TIK_NomClient  LIKE '%' || :searchString || '%' " +
            " and ( CASE WHEN :filterByTicketEtat !=  '' THEN TIK_Etat =:filterByTicketEtat ELSE TIK_Etat !=:filterByTicketEtat END " +
            "and  CASE WHEN :filterByTicketSource !=  ''THEN TIK_Source LIKE '%' || :filterByTicketSource|| '%' ELSE TIK_Source !=:filterByTicketSource  END )" +
           // "and  CASE WHEN :filterBySessionCaisse !=  '' THEN TIK_IdSCaisse LIKE '%' || :filterBySessionCaisse|| '%' ELSE TIK_IdSCaisse !=:filterBySessionCaisse  END ) " +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 1 THEN TIK_NumTicket END ASC, " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 2 THEN TIK_NumTicket END DESC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 1 THEN (CAST (TIK_MtTTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 2 THEN (CAST (TIK_MtTTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END ASC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END DESC ",
    )

    fun filterByClient(
        searchString: String,
     //   filterBySessionCaisse: String,
        filterByTicketEtat: String,
       filterByTicketSource: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>
    @Transaction
    @Query(
        "SELECT * FROM $TICKET_TABLE " +
            "LEFT JOIN $LIGNE_TICKET_TABLE ON $TICKET_TABLE.TIK_NumTicket = $LIGNE_TICKET_TABLE.LT_NumTicket" +
            " WHERE $TICKET_TABLE.TIK_NumTicket LIKE '%' || :searchString || '%' " +
            " and ( CASE WHEN :filterByTicketEtat !=  '' THEN TIK_Etat =:filterByTicketEtat ELSE TIK_Etat !=:filterByTicketEtat END " +
            "and  CASE WHEN :filterByTicketSource !=  ''THEN TIK_Source LIKE '%' || :filterByTicketSource|| '%' ELSE TIK_Source !=:filterByTicketSource  END )" +
           // "and  CASE WHEN :filterBySessionCaisse !=  '' THEN TIK_IdSCaisse LIKE '%' || :filterBySessionCaisse|| '%' ELSE TIK_IdSCaisse !=:filterBySessionCaisse  END ) " +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 1 THEN TIK_NumTicket END ASC, " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 2 THEN TIK_NumTicket END DESC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 1 THEN (CAST (TIK_MtTTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 2 THEN (CAST (TIK_MtTTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END ASC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END DESC ",
    )

    fun filterByTicketNum(
        searchString: String,
      //  filterBySessionCaisse: String,
        filterByTicketEtat: String,
       filterByTicketSource: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>
    @Transaction
    @Query(
        "SELECT * FROM $TICKET_TABLE " +
            "LEFT JOIN $LIGNE_TICKET_TABLE ON $TICKET_TABLE.TIK_NumTicket = $LIGNE_TICKET_TABLE.LT_NumTicket" +
            " WHERE $TICKET_TABLE.TIK_NumeroBL LIKE '%' || :searchString || '%' " +
            " and ( CASE WHEN :filterByTicketEtat !=  '' THEN TIK_Etat =:filterByTicketEtat ELSE TIK_Etat !=:filterByTicketEtat END " +
            "and  CASE WHEN :filterByTicketSource !=  ''THEN TIK_Source LIKE '%' || :filterByTicketSource|| '%' ELSE TIK_Source !=:filterByTicketSource  END )" +
           // "and  CASE WHEN :filterBySessionCaisse !=  '' THEN TIK_IdSCaisse LIKE '%' || :filterBySessionCaisse|| '%' ELSE TIK_IdSCaisse !=:filterBySessionCaisse  END ) " +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 1 THEN TIK_NumTicket END ASC, " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 2 THEN TIK_NumTicket END DESC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 1 THEN (CAST (TIK_MtTTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 2 THEN (CAST (TIK_MtTTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END ASC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END DESC ",
    )

    fun filterByNumBL(
        searchString: String,
      //  filterBySessionCaisse: String,
        filterByTicketEtat: String,
        filterByTicketSource: String,
        sortBy: String,
        isAsc: Int,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>
    @Transaction
    @Query(
        "SELECT * FROM $TICKET_TABLE " +
            "LEFT JOIN $LIGNE_TICKET_TABLE ON $TICKET_TABLE.TIK_NumTicket = $LIGNE_TICKET_TABLE.LT_NumTicket " +
            "WHERE  CASE WHEN :filterByTicketEtat !=  '' THEN TIK_Etat =:filterByTicketEtat ELSE TIK_Etat !=:filterByTicketEtat END   " +
            "and  CASE WHEN :filterByTicketSource !=  ''  THEN TIK_Source LIKE '%' || :filterByTicketSource || '%' ELSE TIK_Source !=:filterByTicketSource END " +

         //   "and  CASE WHEN :filterBySessionCaisse !=  '' THEN TIK_IdSCaisse LIKE '%' || :filterBySessionCaisse|| '%' ELSE TIK_IdSCaisse !=:filterBySessionCaisse  END " +

            " ORDER BY " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 1 THEN TIK_NumTicket END ASC, " +
            "CASE WHEN :sortBy = 'TIK_NumTicket'  AND :isAsc = 2 THEN TIK_NumTicket END DESC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 1 THEN (CAST (TIK_MtTTC AS REAL)) END ASC, " +
            "CASE WHEN :sortBy = 'TIK_MtTTC'  AND :isAsc = 2 THEN (CAST (TIK_MtTTC AS REAL)) END DESC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END ASC, " +
            "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',$TICKET_TABLE.TIK_DateHeureTicket) END DESC ",
    )

    fun getAllFiltred(
        isAsc: Int,
     // filterBySessionCaisse: String,
        filterByTicketEtat: String,
       filterByTicketSource: String,
        sortBy: String,
    ): Flow<Map<TicketWithFactureAndPayments, List<LigneTicketWithArticle>>>
}
