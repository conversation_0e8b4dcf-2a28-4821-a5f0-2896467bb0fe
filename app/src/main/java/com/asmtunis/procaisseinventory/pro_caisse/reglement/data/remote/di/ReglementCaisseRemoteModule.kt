package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.remote.api.ReglementCaisseApi
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.remote.api.ReglementCaisseApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object ReglementCaisseRemoteModule {

    @Provides
    @Singleton
    fun provideReglementCaisseApi(client: HttpClient): ReglementCaisseApi = ReglementCaisseApiImpl(client)

}