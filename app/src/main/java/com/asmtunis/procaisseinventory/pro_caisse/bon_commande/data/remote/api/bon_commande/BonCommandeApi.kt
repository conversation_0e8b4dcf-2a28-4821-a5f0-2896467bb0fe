package com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.remote.api.bon_commande

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import kotlinx.coroutines.flow.Flow


interface BonCommandeApi {
    suspend fun getBonCommandes(baseConfig: String): Flow<DataResult<List<BonCommande>>>
    suspend fun addBatchBonCommande(baseConfig: String): Flow<DataResult<List<InvPatBatchResponse>>>
}