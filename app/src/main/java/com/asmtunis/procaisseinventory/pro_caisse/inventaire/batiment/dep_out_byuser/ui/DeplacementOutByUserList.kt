package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringPlural
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel

@Composable
fun DeplacementOutByUserList(
    selectedBaseconfig: BaseConfig,
    immobilisationList: List<Immobilisation>,
    isConnected: Boolean,
    listState: LazyListState,
    filteredZoneConsomation: Map<DeplacementOutByUserWithImmobilisation, List<LigneBonCommande>>,
    deplacementOutByUser: MutableList<DeplacementOutByUserWithImmobilisation>,
    ligneBonCommande: MutableList<LigneBonCommande>,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    onItemClick: (index: DeplacementOutByUserWithImmobilisation) -> Unit
) {
    // val state = remember { deplacementOutByUserViewModel.state.value }

    val context = LocalContext.current


    val isRefreshing  = getProCaisseDataViewModel.deplacementOutByUserResponseState.loading

    PullToRefreshLazyColumn(
        items = deplacementOutByUser,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !deplacementOutByUser.any { !(it.immobilisation?.isSync?: true) } && isConnected,
        onRefresh = {

            getProCaisseDataViewModel.getDeplacementOutByUser(
                baseConfig = selectedBaseconfig,
                listImmobilisation =  immobilisationList
            )
        },
        key = {depOutByUser ->
            depOutByUser.deplacementOutByUser!!.dEVNum
        },
        content = { depOutByUser ->
            val firstTextRemarque = if(depOutByUser.deplacementOutByUser?.dEVEtatBon== "1") " (${stringResource(id = R.string.en_instance)})" else ""

            OutlinedCard(
                modifier = Modifier.padding(start = 9.dp,end = 9.dp, top = 12.dp)
            )  {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                    modifier =
                    Modifier
                        .wrapContentHeight()
                        .fillMaxWidth()
                        .padding(top = 12.dp, start = 9.dp)
                        .clickable {
                           // onItemClick(deplacementOutByUser.indexOf(depOutByUser))
                            onItemClick(depOutByUser)
                        },
                    // .background("#063041".color)
                ) {

                    Column(
                        modifier =
                        Modifier
                            //  .background(colorResource(id =if(state.lists[index].solde.toDouble() < 0.0) R.color.teal_700 else R.color.white ))
                            .wrapContentSize(),
                        // .padding(padding) ,
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.Start,
                    ) {
                        Text(
                            text =
                            buildAnnotatedString {
                                append(depOutByUser.deplacementOutByUser!!.dEVNum)

                                if(firstTextRemarque.isNotEmpty())
                                    withStyle(
                                        SpanStyle(
                                            color = MaterialTheme.colorScheme.error,
                                            fontSize = MaterialTheme.typography.bodyMedium.fontSize
                                        )
                                    ) {
                                        append(firstTextRemarque)
                                    }




                            },
                            fontSize = MaterialTheme.typography.titleMedium.fontSize,
                            fontWeight = MaterialTheme.typography.titleMedium.fontWeight,
                            maxLines = 2,
                            //   color = if(deplacementOutByUser[index].deplacementOutByUser?.dEVEtatBon == "1") MaterialTheme.colorScheme.error else LocalContentColor.current
                        )



                        Text(
                            text = (depOutByUser.immobilisation?.cLINomPren?: depOutByUser.immobilisation?.cLICode?: "") + " ("+depOutByUser.deplacementOutByUser!!.dEVCodeClient+")",
                            fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                            fontWeight = MaterialTheme.typography.bodyMedium.fontWeight,
                        )

                        Text(
                            text =
                            stringPlural(
                                nbr = filteredZoneConsomation[depOutByUser]?.size ?: 0,
                                single = stringResource(id = R.string.article_title),
                                plural = stringResource(id = R.string.article_field_title)
                            ),
                            fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                            fontWeight = MaterialTheme.typography.bodyMedium.fontWeight,
                        )

                        Text(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(end = 12.dp),
                            text = depOutByUser.deplacementOutByUser?.dEVDDm?.replace(".000", "")?.removeSuffix(" 00:00:00")?: "",
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                            textAlign = TextAlign.End
                        )
                    }
                }
            }
        },
    )

}