package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class LigneBonCommandeWithImageList(
    @Embedded
    @SerialName("ligneBonCommande")
    var ligneBonCommande: LigneBonCommande? = null,



    @Relation(
        parentColumn = "LG_DEV_Code_M",
        entityColumn = "Code_Mob"
    )
    @SerialName("imageList")
    var imageList: List<ImagePieceJoint>? = null,
)