package com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.remote.di


import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.remote.api.VeilleConcurentielApi
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.remote.api.VeilleConcurentielApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object VeilleConcurentielModule {

    @Provides
    @Singleton
    fun provideVeilleConcurentielApi(client: HttpClient): VeilleConcurentielApi = VeilleConcurentielApiImpl(client)

}