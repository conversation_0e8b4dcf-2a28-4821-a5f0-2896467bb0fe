package com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen

import android.util.Log
import android.widget.Toast
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.domaine.ClientCA
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.jaikeerthick.composable_graphs.composables.bar.BarGraph
import com.jaikeerthick.composable_graphs.composables.bar.model.BarData
import com.jaikeerthick.composable_graphs.composables.bar.style.BarGraphColors
import com.jaikeerthick.composable_graphs.composables.bar.style.BarGraphStyle
import com.jaikeerthick.composable_graphs.composables.bar.style.BarGraphVisibility
import com.jaikeerthick.composable_graphs.style.LabelPosition

@Composable
fun ClientBarChart(
    listNTopClients: List<ClientCA>,
    toaster: ToasterState
) {
    val context = LocalContext.current

    // Prepare bar data, converting strings to appropriate types.  Error handling is crucial here.
    val barData: List<BarData> = remember(listNTopClients) { // Use remember to avoid recreating barData on every recomposition
        listNTopClients.mapNotNull { client -> // Use mapNotNull to handle potential errors and filter out nulls
            try {
                val amount = stringToDouble(client.cA)
                BarData(x = client.cliNompren, y = amount)
            } catch (e: NumberFormatException) {
                Log.e("ClientBarChart", "Error converting CA to Double for client ${client.cliNompren}: ${client.cA}", e)
                // Show a Toast to the user that the CA value for the client cannot be displayed
                Toast.makeText(context, "Error showing CA value for ${client.cliNompren}", Toast.LENGTH_SHORT).show()
                null // Skip this client if conversion fails.  Important for robustness.
            } catch (e: Exception) {
                Log.e("ClientBarChart", "Unexpected error processing client ${client.cliNompren}", e)
                Toast.makeText(context, "Error showing CA value for ${client.cliNompren}", Toast.LENGTH_SHORT).show()
                null
            }
        }
    }

    val visibility = remember { //Use remember to avoid recreating visibility on every recomposition
        BarGraphVisibility(
            isYAxisLabelVisible = true,
            isXAxisLabelVisible = true,
            isGridVisible = true
        )
    }

    val style = remember {  //Use remember to avoid recreating style on every recomposition
        BarGraphStyle(
            colors = BarGraphColors(),
            visibility = visibility,
            yAxisLabelPosition = LabelPosition.LEFT
        )
    }


    if (barData.isNotEmpty()) {
        BarGraph(
            data = barData,
            style = style,
            onBarClick = { barData ->
                showToast(
                    context = context,
                    toaster = toaster,
                    message = "${barData.x}\n${convertStringToPriceFormat(barData.y.toString())}",
                    type = ToastType.Info,
                )
            }
        )
    } else {
        // Handle the case where there is no data to display.  This prevents a crash.
        Text("No data to display.") // You can replace this with a more informative message or a loading indicator.
    }
}