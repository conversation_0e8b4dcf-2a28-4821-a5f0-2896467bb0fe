package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.TypeMouvementDAO
import kotlinx.coroutines.flow.Flow


class TypeMouvementLocalRepositoryImpl(private val typeMouvementDAO: TypeMouvementDAO) :
        TypeMouvementLocalRepository {
    override fun getAll(): Flow<List<TypeMouvement>?> = typeMouvementDAO.all

    override fun insert(item: TypeMouvement) = typeMouvementDAO.insert(item)

    override fun insertAll(items: List<TypeMouvement>) = typeMouvementDAO.insertAll(items)

    override fun deleteAll() = typeMouvementDAO.deleteAll()
}