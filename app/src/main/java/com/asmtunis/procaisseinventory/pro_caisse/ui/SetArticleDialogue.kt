package com.asmtunis.procaisseinventory.pro_caisse.ui

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.dokar.sonner.ToasterState

@Composable
fun SetArticleDialogue(
    toaster: ToasterState,
    hasPromo : Boolean,
    isProInventory: Boolean = false,
    proCaisseAuthorization: List<Authorization> = emptyList(),
    setShowPriceCategoryChange : (Boolean) -> Unit,
    onShowSetArticleChange : (Boolean) -> Unit,
    setShowPriceCategorySingleArticleChange : (Boolean) -> Unit,
    getSingleArticlePrice : (SelectedArticle) -> String,
    onSelectedPriceCategorieChange : (String) -> Unit,
    selectedArticle : SelectedArticle,
    priceCategoryList: List<String>,
    updateQty :(String)-> Unit,
    onRemiseChange :(String)-> Unit,
    onPrixCaisseChange :(String)-> Unit,
    onPrixTotalChange :(String)-> Unit,
    onPrixVenteChange :(String)-> Unit = {},
    showPriceCategorySingleArticle : Boolean,
    onConfirm : () -> Unit,
    onReset: () -> Unit,
    onPrixHTChange: (String) -> Unit = {},
    showTva: Boolean = false,
    tvaList: List<Tva> = emptyList(),
    selectedTva: Tva = Tva(),
    onSelectedTvaChange: (Tva) -> Unit = {},
    onTvaExpandedChange: (Boolean) -> Unit = {},
    tvaExpanded: Boolean = false,
    showUnitArticle: Boolean = true,
    unitArticleExpanded: Boolean = false,
    unitArticleList: List<UniteArticle> = emptyList(),
    onSelectedUnitArticleChange: (UniteArticle) -> Unit = {},
    onUnitArticleExpandedChange: (Boolean) -> Unit = {},
) {
    val context = LocalContext.current
    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.

            setShowPriceCategoryChange(false)

        },
        properties = DialogProperties(
            usePlatformDefaultWidth = true
        ),
        content = {
            Card(
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp),
                modifier = Modifier
            ) {
                SetArticleView(
                    toaster = toaster,
                    context = context,
                    showTva = showTva,
                    tvaList = tvaList,
                    selectedTva = selectedTva,
                    onSelectedTvaChange = onSelectedTvaChange,
                    onTvaExpandedChange = onTvaExpandedChange,
                    tvaExpanded = tvaExpanded,
                    hasPromo = hasPromo,
                    isProInventory = isProInventory,
                    proCaisseAuthorization = proCaisseAuthorization,
                    selectedArticle = selectedArticle,
                    prixUnitaire = getSingleArticlePrice(selectedArticle),
                    priceCategoryList = priceCategoryList,
                    unitArticleList = unitArticleList,
                    showUnitArticle = showUnitArticle,
                    unitArticleExpanded = unitArticleExpanded,
                    onSelectedUnitArticleChange = { onSelectedUnitArticleChange(it) },
                    onUnitArticleExpandedChange = { onUnitArticleExpandedChange(it) },
                    onQuantityChange = {
                        updateQty(it)
                    },

                    onRemiseChange = {
                        onRemiseChange(it)
                    },
                    showPriceCategorix = {
                        setShowPriceCategorySingleArticleChange(it)
                    },
                    priceCategoryVisibility = showPriceCategorySingleArticle,
                    onDismiss = {
                                onReset()
                              onShowSetArticleChange(false)
                    },
                    onConfirm = {
                        onConfirm()
                    //    addItemToSelectedArticleMobilityList(selectedArticleMobility)

                       onShowSetArticleChange(false)
                    },
                    onPrixCaisseChange = {
                        onPrixCaisseChange(it)
                    },
                    onSelectedPriceCategorieChange = {
                        onSelectedPriceCategorieChange(it)
                    },
                    onPrixTotalChange = {
                        onPrixTotalChange(it)
                    },

                    onPrixHTChange = {
                        onPrixHTChange(it)
                    },
                    onPrixVenteChange = {
                        onPrixVenteChange(it)
                    }
                )

            }

        }
    )
}