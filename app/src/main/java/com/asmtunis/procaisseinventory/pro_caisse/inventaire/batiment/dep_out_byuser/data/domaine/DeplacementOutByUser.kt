package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.DEPLACEMENT_OUT_BY_USER,
    primaryKeys = ["DEV_Num"]
)
@Serializable
data class DeplacementOutByUser(
    @SerialName("BON_LIV_Exerc")
    @ColumnInfo("BON_LIV_Exerc")
    var bONLIVExerc: String? = "",

    @SerialName("BON_LIV_Num")
    @ColumnInfo("BON_LIV_Num")
    var bONLIVNum: String? = "",

    @SerialName("CodeSit")
    @ColumnInfo("CodeSit")
    var codeSit: String = "",

    @SerialName("CodeSte")
    @ColumnInfo("CodeSte")
    var codeSte: String = "",

    @SerialName("CodeUtilisateur")
    @ColumnInfo("CodeUtilisateur")
    var codeUtilisateur: String = "",

    @SerialName("DDm")
    @ColumnInfo("DDm")
    var dDm: String? = "",

    @SerialName("DDmM")
    @ColumnInfo("DDmM")
    var dDmM: String? = "",

    @SerialName("DEV_Chauffeur")
    @ColumnInfo("DEV_Chauffeur")
    var dEVChauffeur: String? = "",

    @SerialName("DEV_Client")
    @ColumnInfo("DEV_Client")
    var dEVClient: String? = "",

    @SerialName("DEV_CodeClient")
    @ColumnInfo("DEV_CodeClient")
    var dEVCodeClient: String = "",

    @SerialName("DEV_Code_M")
    @ColumnInfo("DEV_Code_M")
    var dEVCodeM: String = "",

    @SerialName("DEV_Code_SF")
    @ColumnInfo("DEV_Code_SF")
    var dEVCodeSF: String = "",

    @SerialName("DEV_Code_SO")
    @ColumnInfo("DEV_Code_SO")
    var dEVCodeSO: String = "",

    @SerialName("DEV_DDm")
    @ColumnInfo("DEV_DDm")
    var dEVDDm: String = "",

    @SerialName("DEV_Date")
    @ColumnInfo("DEV_Date")
    var dEVDate: String = "",

    @SerialName("DEV_Etat")
    @ColumnInfo("DEV_Etat")
    var dEVEtat: String = "",

    @SerialName("DEV_EtatBon")
    @ColumnInfo("DEV_EtatBon")
    var dEVEtatBon: String = "",

    @SerialName("DEV_Exerc")
    @ColumnInfo("DEV_Exerc")
    var dEVExerc: String = "",

    @SerialName("DEV_ExoNum")
    @ColumnInfo("DEV_ExoNum")
    var dEVExoNum: String? = "",

    @SerialName("DEV_ExoVal")
    @ColumnInfo("DEV_ExoVal")
    var dEVExoVal: String? = "",

    @SerialName("DEV_Exonoration")
    @ColumnInfo("DEV_Exonoration")
    var dEVExonoration: String = "",

    @SerialName("DEV_export")
    @ColumnInfo("DEV_export")
    var dEVExport: String? = "",

    @SerialName("DEV_info1")
    @ColumnInfo("DEV_info1")
    var dEVInfo1: String? = "",

    @SerialName("DEV_info2")
    @ColumnInfo("DEV_info2")
    var dEVInfo2: String? = "",

    @SerialName("DEV_info3")
    @ColumnInfo("DEV_info3")
    var dEVInfo3: String = "",

    @SerialName("DEV_Latitude")
    @ColumnInfo("DEV_Latitude")
    var dEVLatitude: String? = "",

    @SerialName("DEV_Longitude")
    @ColumnInfo("DEV_Longitude")
    var dEVLongitude: String? = "",

    @SerialName("DEV_MntDC")
    @ColumnInfo("DEV_MntDC")
    var dEVMntDC: String = "",

    @SerialName("DEV_MntFodec")
    @ColumnInfo("DEV_MntFodec")
    var dEVMntFodec: String = "",

    @SerialName("DEV_MntNetHt")
    @ColumnInfo("DEV_MntNetHt")
    var dEVMntNetHt: String = "",

    @SerialName("DEV_MntTTC")
    @ColumnInfo("DEV_MntTTC")
    var dEVMntTTC: String = "",

    @SerialName("DEV_MntTva")
    @ColumnInfo("DEV_MntTva")
    var dEVMntTva: String = "",

    @SerialName("DEV_Mntht")
    @ColumnInfo("DEV_Mntht")
    var dEVMntht: String = "",

    @SerialName("DEV_Num")
    @ColumnInfo("DEV_Num")
    var dEVNum: String = "",

    @SerialName("DEV_Observation")
    @ColumnInfo("DEV_Observation")
    var dEVObservation: String? = "",

    @SerialName("DEV_Regler")
    @ColumnInfo("DEV_Regler")
    var dEVRegler: String? = "",

    @SerialName("DEV_Remise")
    @ColumnInfo("DEV_Remise")
    var dEVRemise: String? = "",

    @SerialName("DEV_Station")
    @ColumnInfo("DEV_Station")
    var dEVStation: String = "",

    @SerialName("DEV_StationOrigine")
    @ColumnInfo("DEV_StationOrigine")
    var dEVStationOrigine: String = "",

    @SerialName("DEV_TauxRemise")
    @ColumnInfo("DEV_TauxRemise")
    var dEVTauxRemise: String? = "",

    @SerialName("DEV_Timbre")
    @ColumnInfo("DEV_Timbre")
    var dEVTimbre: String = "",

    @SerialName("DEV_TyMvtCode")
    @ColumnInfo("DEV_TyMvtCode")
    var dEVTyMvtCode: String? = "",

    @SerialName("DEV_User")
    @ColumnInfo("DEV_User")
    var dEVUser: String = "",

    @SerialName("DEV_vehicule")
    @ColumnInfo("DEV_vehicule")
    var dEVVehicule: String? = "",

    @SerialName("DEV_Ville")
    @ColumnInfo("DEV_Ville")
    var dEVVille: String? = "",

    @SerialName("DEV_Zone")
    @ColumnInfo("DEV_Zone")
    var dEVZone: String? = "",

    @SerialName("export")
    @ColumnInfo("export")
    var export: String? = "",

    @SerialName("exportM")
    @ColumnInfo("exportM")
    var exportM: String? = "",

    @SerialName("lignes_devis")
   // @ColumnInfo("lignes_devis")
    @Ignore
    var lignesDevis: List<LigneBonCommande> = emptyList()
)