package com.asmtunis.procaisseinventory.view_model

import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.SelectPatrimoineViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.view_model.BonCommandeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.view_model.BonLivraisonViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model.BonRetourViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.text_validation.ClientTextValidationViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.view_model.ClientViewModel
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.view_model.DistributionNumeriqueViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.view_model.DeplacementOutByUserViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.view_model.InventaireBatimentViewModel
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.InventaireViewModel
import com.asmtunis.procaisseinventory.pro_caisse.reglement.view_model.ReglementCaisseViewModel
import com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model.TourneeViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.AutreViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.NewProductViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.PrixViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.PromotionViewModel
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.view_model.VeilleConcurentielViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.view_model.PaymentViewModel


data class ProCaisseViewModels (
    val batimentViewModel: InventaireBatimentViewModel,
    val clientViewModel: ClientViewModel,
    val distNumViewModel: DistributionNumeriqueViewModel,
    val veilleConcurentielViewModel: VeilleConcurentielViewModel,
    val newProdViewModel: NewProductViewModel,
    val promotionViewModel: PromotionViewModel,
    val prixViewModel: PrixViewModel,
    val autreViewModel: AutreViewModel,
    val clientTextValidationViewModel: ClientTextValidationViewModel,
    val reglementCaisseViewModel: ReglementCaisseViewModel,
    val bonLivraisonViewModel: BonLivraisonViewModel,
    val bonCommandeViewModel: BonCommandeViewModel,
    val bonRetourViewModel: BonRetourViewModel,
    val invPatViewModel: InventaireViewModel,
    val selectPatrimoineVM: SelectPatrimoineViewModel,
   // val dashboardScreenVM: DashboardScreenViewModel,
    val tourneeViewModel: TourneeViewModel,
    val deplacementOutByUserViewModel: DeplacementOutByUserViewModel,
    val paymentViewModel: PaymentViewModel,
)