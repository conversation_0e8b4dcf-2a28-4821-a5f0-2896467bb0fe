package com.asmtunis.procaisseinventory.network_errors.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
    class NetworkErrorsViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proCaisseLocalDb: ProCaisseLocalDb
    ) : ViewModel() {

        init {
            getNetworksErrors()
        }

    var networkErrorsList: List<NetworkError> by mutableStateOf(emptyList())
        private set
        private fun getNetworksErrors() {
            viewModelScope.launch {
                proCaisseLocalDb.networkErrors.getAll().collect {
                    networkErrorsList = emptyList()

                    networkErrorsList = it
                }
            }
        }


     fun deleteNetworkErrorsList() {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.networkErrors.deleteAll()
        }
    }


    fun deleteNetworkErrorByUrl(url: String, extraInfo: String) {
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.networkErrors.deleteByUrlAndExtraInfo(url = url, extraInfo = extraInfo)
        }
    }



}