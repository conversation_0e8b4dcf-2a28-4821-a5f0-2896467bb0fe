package com.asmtunis.procaisseinventory.network_errors.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.coroutines.flow.Flow


@Dao
interface NetworkErrorsDAO {
    @get:Query("SELECT * FROM ${ProCaisseConstants.NETWORK_ERRORS}")
    val all: Flow<List<NetworkError>>

    @Query("SELECT * FROM ${ProCaisseConstants.NETWORK_ERRORS} WHERE url = :url and extraInfo = :extraInfo ")
    fun getOneByUrlAndExtraInfo(url: String, extraInfo: String): Flow<NetworkError>



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: NetworkError)

    @Query("DELETE FROM ${ProCaisseConstants.NETWORK_ERRORS} where url = :url and extraInfo = :extraInfo")
    fun deleteByUrlAndExtraInfo(url: String, extraInfo: String)

    @Query("DELETE FROM ${ProCaisseConstants.NETWORK_ERRORS}")
    fun deleteAll()
}