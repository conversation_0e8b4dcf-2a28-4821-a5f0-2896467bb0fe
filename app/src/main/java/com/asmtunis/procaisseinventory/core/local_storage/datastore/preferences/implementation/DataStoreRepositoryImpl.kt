package com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.implementation

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

private const val PREFERENCES_NAME = "my_preferences"

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = PREFERENCES_NAME)

class DataStoreRepositoryImpl @Inject constructor(
    private val context: Context
) : DataStoreRepository {

    override suspend fun putString(key: String, value: String) {
        val preferencesKey = stringPreferencesKey(key)
        context.dataStore.edit { preferences ->
            preferences[preferencesKey] = value
        }
    }

    override suspend fun putInt(key: String, value: Int) {
        val preferencesKey = intPreferencesKey(key)
        context.dataStore.edit { preferences ->
            preferences[preferencesKey] = value
        }
    }

    override suspend fun putBoolean(key: String, value: Boolean) {
        val preferencesKey = booleanPreferencesKey(key)
        context.dataStore.edit { preferences ->
            preferences[preferencesKey] = value
        }
    }

    override  fun getString(key: String, default: String): Flow<String> {
        val preferencesKey = stringPreferencesKey(key)
        val preferences = context.dataStore.data.map { it[preferencesKey]?: default }
        return preferences
    }

    override  fun getInt(key: String, default: Int): Flow<Int> {
        val preferencesKey = intPreferencesKey(key)
        val preferences = context.dataStore.data.map { it[preferencesKey]?: default }
        return preferences
    }

    override  fun getBoolean(key: String, default: Boolean): Flow<Boolean> {
        val preferencesKey = booleanPreferencesKey(key)
        val preferences = context.dataStore.data.map { it[preferencesKey]?: default }
        return preferences
    }
}