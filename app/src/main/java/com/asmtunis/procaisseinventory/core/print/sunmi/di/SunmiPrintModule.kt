package com.asmtunis.procaisseinventory.core.print.sunmi.di

import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object SunmiPrintModule {
    
    @Provides
    @Singleton
    fun provideSunmiPrintManager(
        proCaisseLocalDb: ProCaisseLocalDb,
        proInventoryLocalDb: ProInventoryLocalDb
    ): SunmiPrintManager {
        return SunmiPrintManager(proCaisseLocalDb, proInventoryLocalDb)
    }
}
