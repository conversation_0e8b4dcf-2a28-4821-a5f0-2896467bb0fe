package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult

class ValidateStringNotEmpty {

    fun execute(value: String): ValidationResult {
        if (value.isBlank()) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.cant_be_empty)
            )
        }
        return ValidationResult(
            successful = true
        )
    }
}
