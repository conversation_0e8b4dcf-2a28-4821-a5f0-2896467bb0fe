package com.asmtunis.procaisseinventory.core

import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import com.simapps.ui_kit.utils.getCurrentLocalDateTime
import java.time.LocalDate

object Globals {
// "x8020x0x446xx11x"


   var DEVICE_ID:  String = "" //"c8020d0c446aa99f" "e019f1db30209e52"

   var devise: Devise = Devise()
   var currentLocalDateTime = getCurrentLocalDateTime()
   var currentYear = currentLocalDateTime.year
   var currentMonth = currentLocalDateTime.monthNumber.toString().padStart(2, '0')


    var USER_ID = ""
    const val PAGE_SIZE = "5000"
    const val PRO_CAISSE_MOBILITY = "ProCaisse Mobility"
    const val PRO_CAISSE = "ProCaisse"

    const val PRO_CAISSE_AUTHORIZATION_TYPE_MENU = "ProCaisseMobility"
    const val PRO_INVENTORY = "ProInventory Mobile"
    const val PRO_INVENTORY_AUTHORIZATION_TYPE_MENU = "ProInventory"
    const val APP_NAME = "ProCaisse Inventory"


    const val ADMIN = "admin"
    const val DEMO_BASE_CONFIG = "Demo Base Config"

    const val BCC_CLIENT =  "BCC_client"
    const val BCC =  "BCC"

    const val NO_OPERATION = "No Operation"
    const val ADD = "Add"
    const val MINUS = "minus"



   const val OPERATEUR_PATRIMOINE = "Operateur patrimoine"
    const val PRIX_PUBLIQUE= "Prix publique"

    const val REQUEST_CODE_UPDATE = 600

    const val PROMOBILE= "promobile"

    const val FACTURE= "FACTURE"
    const val BL= "BL"
    const val BE= "BE"
    const val BT= "BT"

    const val SOURCE= "MOBILE"
    const val NOT_FOUND_LOCALLY= "N'est pas affecté"


    const val AUTRE= "Autre"
    const val THIS_MONTH= "Le Mois courant"


    const val FROM_NOTIFICATION = "isFromNotification"

    const val PASSAGER = "Passager"
    const val PROSPECT = "Prospect"


    const val VALIDE = "Validée"
    const val ACTIVE = "Active"




    //CODE M PREFIX
    const val DEP_OUT = "Dep_Out"
    const val DEP_IN = "Dep_In"
    const val INV = "Inv"
    const val AFF = "Aff"


    const val VENTE_DU_JOUR = "vente du jour"
    const val REGLER_ACPT = "Regler Acpt"

    const val TICKET_FACTURE_DEJA = "TICKET FACTURE DEJA"







      val DEFAULT_RANGE_START_DATE = LocalDate.now()
    const val DEFAULT_RANGE_END_YEAR_OFFSET = 20L
    val DEFAULT_RANGE_END_DATE = LocalDate.now().plusYears(DEFAULT_RANGE_END_YEAR_OFFSET)
        .withMonth(1)
        .withDayOfMonth(15)

        val DATE_RANGE_FROM_NOW = DEFAULT_RANGE_START_DATE..DEFAULT_RANGE_END_DATE
}
