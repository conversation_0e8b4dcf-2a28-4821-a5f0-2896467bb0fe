package com.asmtunis.procaisseinventory.core.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import com.asmtunis.procaisseinventory.core.ktor.Urls.BASE_URL
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi


object ImageUtils {
    fun createImageFile(context: Context): File {
        // Create an image file name
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US).format(Date())
        val imageFileName = "JPEG_" + /*timeStamp + */"_"
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        val image =
            File.createTempFile(
                imageFileName, // prefix
                ".jpg", // suffix
                storageDir, // directory
            )

        // Save a file: path for use with ACTION_VIEW intents
        //  mCurrentPhotoPath = image.absolutePath
        return image
    }


        fun getImageModel(image: ImagePieceJoint): Any =
            if(image.image.isNullOrEmpty()) {
                if(image.imgUrl?.contains("http") == true)
                    image.imgUrl?:""
                else BASE_URL + "/" + (image.imgUrl?:"")
            }
            else  if(!image.image.isNullOrEmpty()) {
                base64ToBmp(image.image?:"")
    }


            else {
                base64ToBmp(image.image?:"")
            }


        // /var/www/storage/app/public/VC_IMG_001_22_30792_1qEokZJyQ_1/1aiCurZK2C.png


    //val imgData =   if (item.imageList.first().image?.isNotEmpty() == true) {
//    BASE_URL + "/" + item.imageList.first().imgUrl
//}
//else if (item.imageList.first().cheminImg?.isNotEmpty() == true) {
//    BASE_URL  + item.imageList.first().cheminImg
//}
//else {
//    ImageUtils.base64ToBmp(
//        item.imageList.first().image ?: ""
//    )
//}


    @ExperimentalEncodingApi
    fun bmpToBase64(bitmap: Bitmap): String? {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 50, outputStream)
        return Base64.encode(outputStream.toByteArray()/*, Base64.DEFAULT*/)
    }


 @OptIn(ExperimentalEncodingApi::class)
 fun base64ToBmp(logoBase64 : String) : Bitmap{

     //val imageBytes = Base64.decode(logoBase64, Base64.DEFAULT)
     val imageBytes =  Base64.decode(logoBase64/*, Base64.DEFAULT*/)
     return BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
    }

    @OptIn(ExperimentalEncodingApi::class)
     fun uriToBase64(context: Context, uri: Uri): String {



      //  val inputStream =   context.contentResolver.openInputStream(uri)
      // val bytes = inputStream?.readBytes()
        val bytes =  compressImageToByteArray(context, uri, quality = 50)

       // inputStream?.close()
        return if(bytes!=null) Base64.encode(bytes/*, Base64.DEFAULT*/) else ""
    }

    private fun compressImageToByteArray(context: Context, imageUri: Uri, quality: Int): ByteArray? {
        var inputStream: InputStream? = null
        var byteArrayOutputStream: ByteArrayOutputStream? = null
        return try {
            inputStream = context.contentResolver.openInputStream(imageUri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            byteArrayOutputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, byteArrayOutputStream)
            byteArrayOutputStream.toByteArray()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        } finally {
            inputStream?.close()
            byteArrayOutputStream?.close()
        }
    }

    private fun bitmapToFile(context: Context, bitmap: Bitmap, fileNameToSave: String): File? {
        var file: File? = null
        return try {
            file = File(context.getExternalFilesDir(Environment.DIRECTORY_PICTURES).toString() + File.separator + fileNameToSave + ".jpeg")
            file.createNewFile()

            //Convert bitmap to byte array
            val bos = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos)
            val bitmapdata = bos.toByteArray()

            //write the bytes in file
            val fos = FileOutputStream(file)
            fos.write(bitmapdata)
            fos.flush()
            fos.close()
            file
        } catch (e: Exception) {
            e.printStackTrace()
            file // it will return null
        }
    }

}