package com.asmtunis.procaisseinventory.core.print.sunmi

import android.content.Context
import android.graphics.BitmapFactory
import android.util.Base64
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.utils.mobilecode.MobileCodeGeneration
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.sunmi.printerx.PrinterSdk
import com.sunmi.printerx.enums.Align
import com.sunmi.printerx.style.BaseStyle
import com.sunmi.printerx.style.TextStyle
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SunmiPrintManager @Inject constructor(
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb
) : ViewModel() {

    private val _printerStatus = MutableStateFlow<String>("")
    val printerStatus: StateFlow<String> = _printerStatus.asStateFlow()

    private val _isPrinterReady = MutableStateFlow<Boolean>(false)
    val isPrinterReady: StateFlow<Boolean> = _isPrinterReady.asStateFlow()

    // Properties for establishment and logo information
    private var etablisement: Etablisement = Etablisement()
    private var parametrage: Parametrages = Parametrages()
    private var logo: String = ""

    private var printer: PrinterSdk.Printer? = null

    init {
        // Load establishment and parametrage data
        loadEtablisementData()
        loadParametrageData()
    }

    private fun loadEtablisementData() {
        viewModelScope.launch {
            try {
                val etablisements = proCaisseLocalDb.etablisement.getAll().firstOrNull()
                if (!etablisements.isNullOrEmpty()) {
                    etablisement = etablisements.first()
                    Log.d("SunmiPrintManager", "Loaded establishment data: ${etablisement.desgEt}")
                }
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error loading establishment data: ${e.message}")
            }
        }
    }

    private fun loadParametrageData() {
        viewModelScope.launch {
            try {
                val params = proInventoryLocalDb.parametrage.getOne().firstOrNull()
                if (params != null) {
                    parametrage = params
                    logo = parametrage.pARAMLogo ?: ""
                    Log.d("SunmiPrintManager", "Loaded parametrage data, logo size: ${logo.length}")
                }
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error loading parametrage data: ${e.message}")
            }
        }
    }

    // Global variable to store the printer instance
    companion object {
        var selectPrinter: PrinterSdk.Printer? = null
        const val PAPER_WIDTH_80MM = "80mm"
        const val PAPER_WIDTH_58MM = "58mm"
        var defaultPaperWidth = PAPER_WIDTH_80MM // Default to 80mm
    }

    fun initPrinter(context: Context, onReady: (() -> Unit)? = null) {
        Log.d("SunmiPrintManager", "Initializing Sunmi printer with context: $context")

        try {
            // Enable SDK logging for debugging
            PrinterSdk.getInstance().log(true, null)
            Log.d("SunmiPrintManager", "SDK logging enabled")

            // Initialize the Sunmi Printer SDK
            PrinterSdk.getInstance().getPrinter(context, object : PrinterSdk.PrinterListen {
                override fun onDefPrinter(printer: PrinterSdk.Printer?) {
                    Log.d("SunmiPrintManager", "onDefPrinter called with printer: $printer")
                    <EMAIL> = printer
                    selectPrinter = printer  // Set the global printer instance
                    _isPrinterReady.value = printer != null

                    if (printer != null) {
                        Log.d("SunmiPrintManager", "Printer connected successfully")
                        viewModelScope.launch(Dispatchers.IO) {
                            try {
                                val status = printer.queryApi().status.name
                                Log.d("SunmiPrintManager", "Printer status: $status")
                                _printerStatus.value = "Status: $status"
                                val name = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.NAME)
                                val type = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.TYPE)
                                val paper = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.PAPER)

                                Log.d("SunmiPrintManager", "Printer status: $status")
                                Log.d("SunmiPrintManager", "Printer name: $name")
                                Log.d("SunmiPrintManager", "Printer type: $type")
                                Log.d("SunmiPrintManager", "Printer paper: $paper")

                                // Set the paper width to 80mm
                                setPaperWidth(printer, PAPER_WIDTH_80MM)

                                _printerStatus.value = "Connected to $name ($type)"
                            } catch (e: Exception) {
                                Log.e("SunmiPrintManager", "Error getting printer info: ${e.message}")
                                _printerStatus.value = "Error: ${e.message}"
                            }
                        }
                        Log.d("SunmiPrintManager", "Calling onReady callback")
                        onReady?.invoke()
                    } else {
                        Log.e("SunmiPrintManager", "No printer found")
                        _printerStatus.value = "No printer found"
                        // Still call onReady even if printer is null
                        Log.d("SunmiPrintManager", "Calling onReady callback even though printer is null")
                        onReady?.invoke()
                    }
                }

                override fun onPrinters(printers: MutableList<PrinterSdk.Printer>?) {
                    Log.d("SunmiPrintManager", "onPrinters called with printers: ${printers?.size ?: 0}")
                    printers?.forEach { p ->
                        viewModelScope.launch(Dispatchers.IO) {
                            try {
                                val name = p.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.NAME)
                                Log.d("SunmiPrintManager", "Printer: $name")
                            } catch (e: Exception) {
                                Log.e("SunmiPrintManager", "Error getting printer name: ${e.message}")
                            }
                        }
                    }
                }
            })
            Log.d("SunmiPrintManager", "getPrinter called successfully")
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error initializing printer: ${e.message}")
            e.printStackTrace()
            // Still call onReady even if there's an error
            Log.d("SunmiPrintManager", "Calling onReady callback after error")
            onReady?.invoke()
        }
    }

    suspend fun ensurePrinterReady(context: Context): Boolean {
        if (_isPrinterReady.value) return true
        var ready = false
        val lock = java.lang.Object()
        initPrinter(context) {
            synchronized(lock) {
                ready = true
                lock.notify()
            }
        }
        synchronized(lock) {
            if (!ready) lock.wait(3000) // wait up to 3 seconds
        }
        return _isPrinterReady.value
    }

    fun printTicket(
        context: Context,
        ticketWithFactureAndPayments: TicketWithFactureAndPayments,
        listLigneTicket: List<LigneTicketWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData,
        prefixList: List<Prefixe>
    ) {
        Log.d("SunmiPrintManager", "Printing ticket using Sunmi printer with context: $context")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing ticket")
                Log.d("SunmiPrintManager", "Printer initialization callback, retrying print")
                // Retry print after printer is ready
                printTicket(context, ticketWithFactureAndPayments, listLigneTicket, utilisateur, printParams, prefixList)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = selectPrinter ?: printer
                Log.d("SunmiPrintManager", "Using printer: $printerToUse")

                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available after initialization")
                    return@launch
                }

                // Check printer status before printing
                try {
                    val status = printerToUse.queryApi().status.name
                    Log.d("SunmiPrintManager", "Printer status before printing: $status")
                } catch (e: Exception) {
                    Log.e("SunmiPrintManager", "Error getting printer status: ${e.message}")
                }

                // Enable transaction mode for monitoring print results
                try {
                    printerToUse.lineApi()?.enableTransMode(true)
                    Log.d("SunmiPrintManager", "Transaction mode enabled")
                } catch (e: Exception) {
                    Log.e("SunmiPrintManager", "Error enabling transaction mode: ${e.message}")
                }

                // Get the printer's line API
                val lineApi = printerToUse.lineApi()
                if (lineApi == null) {
                    Log.e("SunmiPrintManager", "Line API not available")
                    return@launch
                }
                Log.d("SunmiPrintManager", "Line API obtained successfully")

                val ticket = ticketWithFactureAndPayments.ticket
                val client = ticketWithFactureAndPayments.client

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("BON LIVRAISON", TextStyle.getStyle().setTextSize(24).enableBold(true))

                // Print ticket number
                val prefixeBl = prefixList.firstOrNull { it.pREIdTable == "Bon_livraison" }?.pREPrefixe ?: "BL_M_"
                var numFact = ""
                if (!ticket?.tIKNumeroBL.isNullOrBlank() || !ticket?.tIKNumFact.isNullOrEmpty()) {
                    if (!ticket?.tIKNumeroBL.isNullOrBlank() &&
                        !ticket.tIKNumeroBL.contains("BL_M") &&
                        !ticket.tIKNumeroBL.contains(prefixeBl)) {
                        numFact = ticket.tIKNumeroBL
                    }
                }

                if (numFact.isNotEmpty()) {
                    lineApi.printText("Num: $numFact", TextStyle.getStyle().setTextSize(16))
                } else {
                    lineApi.printText("Num: ${ticket?.tIKNumTicket ?: ""}", TextStyle.getStyle().setTextSize(16))
                }

                // Print date
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Date: ${ticket?.tIKDateHeureTicket?.substringBefore(".") ?: ""}", TextStyle.getStyle().setTextSize(16))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                printClientInfo(lineApi, client, ticket?.tIKNomClient, ticket?.tIKCodClt)

                // Print operator info
                printOperatorInfo(lineApi, ticket?.tIKIdSCaisse, utilisateur)

                // Print items with larger text and more details for 80mm paper
                for (item in listLigneTicket) {
                    // Print item with more details in a tabular format
                    lineApi.printText("${item.article?.aRTDesignation ?: ""}",
                                     TextStyle.getStyle().setTextSize(16).enableBold(true))
                    lineApi.printText("Quantité: ${item.ligneTicket?.lTQte ?: ""} | Prix: ${item.ligneTicket?.lTPrixVente ?: ""}",
                                     TextStyle.getStyle().setTextSize(16))
                    // Add a small separator between items
                    lineApi.printText("-", TextStyle.getStyle().setTextSize(12))
                }

                lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))

                // Calculate tax and total amounts
                val facture = ticketWithFactureAndPayments.facture
                val timbValue = if(facture?.factTimbre != null && facture.factTimbre != "0") {
                    facture.factTimbre
                } else {
                    if (ticketWithFactureAndPayments.timbre == null)
                        "0"
                    else ticketWithFactureAndPayments.timbre?.tIMBValue ?: "0"
                }

                // Calculate total HT (excluding tax)
                val ttHt = stringToDouble(ticket?.tIKMtTTC) + stringToDouble(ticket?.tIKMtRemise) - stringToDouble(timbValue) - stringToDouble(ticket?.tIKMtTVA)

                // Calculate net TTC (including tax)
                val netTTC = if(facture != null) stringToDouble(facture.factMntTTC)
                else stringToDouble(ticket?.tIKMtTTC) + stringToDouble(timbValue)

                // Print totals section
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

                // Print total articles count
                lineApi.printText("Total Articles: ${listLigneTicket.size}",
                                 TextStyle.getStyle().setTextSize(16).enableBold(true))

                // Print total HT
                lineApi.printText("Montant HT: ${formatPrice(ttHt.toString())}",
                                 TextStyle.getStyle().setTextSize(16))

                // Print TVA amount
                lineApi.printText("Montant TVA: ${formatPrice(ticket?.tIKMtTVA)}",
                                 TextStyle.getStyle().setTextSize(16))

                // Print fiscal stamp
                lineApi.printText("Timbre Fiscal: ${formatPrice(timbValue)}",
                                 TextStyle.getStyle().setTextSize(16))

                // Print total TTC
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Net à Payer: ${formatPrice(ticket?.tIKMtTTC)}",
                                 TextStyle.getStyle().setTextSize(20).enableBold(true))

                // Print payment details if available
                val mntEspece = stringToDouble(ticket?.tIKMtEspece ?: "0")
                val mntCheque = stringToDouble(ticket?.tIKMtCheque ?: "0")
                val mntTraite = stringToDouble(ticket?.tIKMtrecue ?: "0")

                if (mntEspece > 0 || mntCheque > 0 || mntTraite > 0) {
                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                    lineApi.printText("- - - -", TextStyle.getStyle().setTextSize(16))

                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                    if (mntEspece > 0) {
                        lineApi.printText("Espece: ${formatPrice(mntEspece.toString())}", TextStyle.getStyle().setTextSize(16))
                    }

                    if (mntCheque > 0) {
                        lineApi.printText("Cheque: ${formatPrice(mntCheque.toString())}", TextStyle.getStyle().setTextSize(16))
                    }

                    if (mntTraite > 0) {
                        lineApi.printText("Tickets Restaurant: ${formatPrice(mntTraite.toString())}", TextStyle.getStyle().setTextSize(16))
                    }
                }

                // Print footer with client balance and signature boxes
                printFooter(lineApi, client, printParams)

                // Cut paper and output
                lineApi.autoOut()

                // Monitor print results
                printerToUse.lineApi()?.printTrans(object : com.sunmi.printerx.api.PrintResult() {
                    override fun onResult(resultCode: Int, message: String?) {
                        if (resultCode == 0) {
                            Log.d("SunmiPrintManager", "Ticket printed successfully")
                            _printerStatus.value = "Print Success"
                        } else {
                            Log.e("SunmiPrintManager", "Error printing ticket: $message")
                            _printerStatus.value = "Print Error: $message"
                        }
                    }
                })

                // Disable transaction mode
                printerToUse.lineApi()?.enableTransMode(false)

            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing ticket: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    fun printBonCommande(
        context: Context,
        bonCommandeWithClient: BonCommandeWithClient,
        lgBonCommandeWithArticle: List<LigneBonCommandeWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        Log.d("SunmiPrintManager", "Printing bon commande using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing bon commande")
                // Retry print after printer is ready
                printBonCommande(context, bonCommandeWithClient, lgBonCommandeWithArticle, utilisateur, printParams)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = selectPrinter ?: printer
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    return@launch
                }

                // Get the printer's line API
                val lineApi = printerToUse.lineApi()
                if (lineApi == null) {
                    Log.e("SunmiPrintManager", "Line API not available")
                    return@launch
                }

                val bonCommande = bonCommandeWithClient.bonCommande
                val client = bonCommandeWithClient.client

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("BON COMMANDE", TextStyle.getStyle().setTextSize(24).enableBold(true))

                // Print bon commande number
                var num = bonCommande?.dEVNum ?: ""
                if (num.contains("_")) {
                    val numTicket: List<String> = num.split("_")
                    if (numTicket.size >= 6) {
                        num = "DEV_M_" + numTicket[2] + "_" + numTicket[3] + "_" + numTicket[5]
                    }
                }
                lineApi.printText("Num: $num", TextStyle.getStyle().setTextSize(16))

                // Print date
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Date: ${bonCommande?.dEVDate?.substringBefore(".") ?: ""}", TextStyle.getStyle().setTextSize(16))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                printClientInfo(lineApi, client, bonCommande?.dEVClientName, bonCommande?.dEVCodeClient)

                // Print operator info
                printOperatorInfo(lineApi, "", utilisateur)

                // Print items with larger text and more details for 80mm paper
                for (item in lgBonCommandeWithArticle) {
                    // Print item with more details in a tabular format
                    lineApi.printText("${item.article?.aRTDesignation ?: ""}",
                                     TextStyle.getStyle().setTextSize(16).enableBold(true))
                    lineApi.printText("Quantité: ${item.ligneBonCommande?.lGDEVQte ?: ""} | Prix: ${item.ligneBonCommande?.lGDEVPUTTC ?: ""}",
                                     TextStyle.getStyle().setTextSize(16))
                    // Add a small separator between items
                    lineApi.printText("-", TextStyle.getStyle().setTextSize(12))
                }

                lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))

                // Calculate tax and total amounts
                val totalHT = stringToDouble(bonCommande?.dEVMntht ?: "0")
                val totalTVA = stringToDouble(bonCommande?.dEVMntTva ?: "0")
                val totalTTC = stringToDouble(bonCommande?.dEVMntTTC ?: "0")

                // Print totals section
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

                // Print total articles count
                lineApi.printText("Total Articles: ${lgBonCommandeWithArticle.size}",
                                 TextStyle.getStyle().setTextSize(16).enableBold(true))

                // Print total HT
                lineApi.printText("Montant HT: ${formatPrice(totalHT.toString())}",
                                 TextStyle.getStyle().setTextSize(16))

                // Print TVA amount
                lineApi.printText("Montant TVA: ${formatPrice(totalTVA.toString())}",
                                 TextStyle.getStyle().setTextSize(16))

                // Check if there's a remise (discount)
                val remise = stringToDouble(bonCommande?.dEVRemise ?: "0")
                if (remise > 0) {
                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                    lineApi.printText("Total Remise: ${formatPrice(bonCommande?.dEVRemise)}",
                                     TextStyle.getStyle().setTextSize(16))
                }

                // Print total TTC
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Net à Payer: ${formatPrice((totalTTC - remise).toString())}",
                                 TextStyle.getStyle().setTextSize(20).enableBold(true))

                // Print footer with client balance and signature boxes
                printFooter(lineApi, client, printParams)

                // Cut paper and output
                lineApi.autoOut()

                Log.d("SunmiPrintManager", "Bon commande printed successfully")
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing bon commande: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    fun printBonRetour(
        context: Context,
        bonRetourWithClient: BonRetourWithClient,
        lgBonRetourWithArticle: List<LigneBonRetourWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        Log.d("SunmiPrintManager", "Printing bon retour using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing bon retour")
                // Retry print after printer is ready
                printBonRetour(context, bonRetourWithClient, lgBonRetourWithArticle, utilisateur, printParams)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = selectPrinter ?: printer
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    return@launch
                }

                // Get the printer's line API
                val lineApi = printerToUse.lineApi()
                if (lineApi == null) {
                    Log.e("SunmiPrintManager", "Line API not available")
                    return@launch
                }

                val bonRetour = bonRetourWithClient.bonRetour
                val client = bonRetourWithClient.client

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("BON RETOUR", TextStyle.getStyle().setTextSize(24).enableBold(true))

                // Print bon retour number
                lineApi.printText("Num: ${bonRetour?.bORNumero ?: ""}", TextStyle.getStyle().setTextSize(16))

                // Print date
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Date: ${bonRetour?.bORDate?.substringBefore(".") ?: ""}", TextStyle.getStyle().setTextSize(16))

                // Print logo
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                printClientInfo(lineApi, client, bonRetour?.bORNomfrs, bonRetour?.bORCodefrs)

                // Print operator info
                printOperatorInfo(lineApi, "", utilisateur)

                // Print items with larger text and more details for 80mm paper
                for (item in lgBonRetourWithArticle) {
                    // Print item with more details in a tabular format
                    lineApi.printText("${item.article?.aRTDesignation ?: ""}",
                                     TextStyle.getStyle().setTextSize(16).enableBold(true))
                    lineApi.printText("Quantité: ${item.ligneBonRetour?.lIGQteRetour ?: ""} | Prix: ${item.ligneBonRetour?.lIGPrixVentePub ?: ""}",
                                     TextStyle.getStyle().setTextSize(16))
                    // Add a small separator between items
                    lineApi.printText("-", TextStyle.getStyle().setTextSize(12))
                }

                lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))

                // Calculate tax and total amounts
                val totalHT = stringToDouble(bonRetour?.bORMntHT ?: "0")
                val totalTVA = stringToDouble(bonRetour?.bORMntTva ?: "0")
                val totalTTC = stringToDouble(bonRetour?.bORMntTTC ?: "0")

                // Print totals section
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

                // Print total articles count
                lineApi.printText("Total Articles: ${lgBonRetourWithArticle.size}",
                                 TextStyle.getStyle().setTextSize(16).enableBold(true))

                // Print total HT
                lineApi.printText("Montant HT: ${formatPrice(totalHT.toString())}",
                                 TextStyle.getStyle().setTextSize(16))

                // Print TVA amount
                lineApi.printText("Montant TVA: ${formatPrice(totalTVA.toString())}",
                                 TextStyle.getStyle().setTextSize(16))

                // Check if there's a remise (discount)
                val remise = stringToDouble(bonRetour?.bORMntRemise ?: "0")
                if (remise > 0) {
                    lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                    lineApi.printText("Total Remise: ${formatPrice(bonRetour?.bORMntRemise)}",
                                     TextStyle.getStyle().setTextSize(16))
                }

                // Calculate total amount
                val mntTotal = Math.abs(totalTTC) - Math.abs(remise)

                // Print total TTC
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("Net à Payer: ${formatPrice(mntTotal.toString())}",
                                 TextStyle.getStyle().setTextSize(20).enableBold(true))

                // Print footer with client balance and signature boxes
                printFooter(lineApi, client, printParams)

                // Cut paper and output
                lineApi.autoOut()

                Log.d("SunmiPrintManager", "Bon retour printed successfully")
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing bon retour: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    fun printReglement(
        context: Context,
        reglementCaisse: ReglementCaisseWithTicketAndClient,
        utilisateur: Utilisateur,
        printParams: PrintingData,
        prefixList: List<Prefixe>
    ) {
        Log.d("SunmiPrintManager", "Printing reglement using Sunmi printer")
        Log.d("SunmiPrintManager", "Current printer: $printer, selectPrinter: $selectPrinter")

        // If printer is not ready, initialize and retry after ready
        if (printer == null && selectPrinter == null) {
            Log.e("SunmiPrintManager", "Printer not initialized, initializing now...")
            Toast.makeText(context, "Initializing Sunmi printer...", Toast.LENGTH_SHORT).show()
            initPrinter(context) {
                Log.d("SunmiPrintManager", "Printer initialized in callback, now printing reglement")
                // Retry print after printer is ready
                printReglement(context, reglementCaisse, utilisateur, printParams, prefixList)
            }
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            try {
                // Use the global printer instance if available
                val printerToUse = selectPrinter ?: printer
                if (printerToUse == null) {
                    Log.e("SunmiPrintManager", "No printer available")
                    return@launch
                }

                // Get the printer's line API
                val lineApi = printerToUse.lineApi()
                if (lineApi == null) {
                    Log.e("SunmiPrintManager", "Line API not available")
                    return@launch
                }

                val reglement = reglementCaisse.reglementCaisse
                val client = reglementCaisse.client
                val ticket = reglementCaisse.ticket

                // Set paper width to 80mm
                setPaperWidth(printerToUse, PAPER_WIDTH_80MM)

                // Print header with larger text for 80mm paper
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("REGLEMENT", TextStyle.getStyle().setTextSize(24).enableBold(true))

                // Get reglement number
                val prefixeReg = prefixList.firstOrNull { it.pREIdTable == "Reglement" }?.pREPrefixe ?: "REG_M_"
                var numTicket = ""

                if (reglement?.rEGNumTicketPart != null && reglement.rEGNumTicketPart.isNotEmpty()) {
                    // This is a partial payment
                    numTicket = "${prefixeReg}${reglement.rEGNumTicketPart}"
                    lineApi.printText("(Paiement Partiel)", TextStyle.getStyle().setTextSize(16))
                } else {
                    // Regular payment
                    numTicket = reglement?.rEGCCode ?: ""
                }

                lineApi.printText("Num: $numTicket", TextStyle.getStyle().setTextSize(16))

                // Print date
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Date: ${reglement?.rEGCDateReg?.substringBefore(".") ?: ""}", TextStyle.getStyle().setTextSize(16))

                // Print logo/
                printLogo(lineApi, printParams)

                // Print establishment info
                printEstablishmentInfo(lineApi, printParams)

                // Print client info
                printClientInfo(lineApi, client, reglement?.rEGCNomPrenom, reglement?.rEGCCodeClient)

                // Print operator info
                printOperatorInfo(lineApi, reglement?.rEGCIdCaisse, utilisateur)

                // Print reglement details with larger text
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Montant: ${formatPrice(reglement?.rEGCMontant?.toString())}", TextStyle.getStyle().setTextSize(18).enableBold(true))
                lineApi.printText("Mode: ${reglement?.rEGCModeReg ?: ""}", TextStyle.getStyle().setTextSize(16))
                lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))

                // Print payment details
                val mntEspece = reglement?.rEGCMntEspece ?: 0.0
                val mntCheque = reglement?.rEGCMntCheque ?: 0.0
                val mntTraite = reglement?.rEGCMntTraite ?: 0.0
                val total = mntEspece + mntCheque + mntTraite

                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText("- - - -", TextStyle.getStyle().setTextSize(16))

                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Total: ${formatPrice(total.toString())}", TextStyle.getStyle().setTextSize(16))

                // Print footer with client balance and signature boxes
                printFooter(lineApi, client, printParams)

                // Cut paper and output
                lineApi.autoOut()

                Log.d("SunmiPrintManager", "Reglement printed successfully")
            } catch (e: Exception) {
                Log.e("SunmiPrintManager", "Error printing reglement: ${e.message}")
                _printerStatus.value = "Print Error: ${e.message}"
            }
        }
    }

    fun releasePrinter() {
        try {
            PrinterSdk.getInstance().destroy()
            printer = null
            selectPrinter = null
            _isPrinterReady.value = false
            Log.d("SunmiPrintManager", "Printer released")
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error releasing printer: ${e.message}")
        }
    }

    // Helper method to format price values
    private fun formatPrice(value: String?): String {
        if (value == null) return "0.000"
        return try {
            convertStringToPriceFormat(value)
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error formatting price: ${e.message}")
            value
        }
    }

    // Print the company logo if available and enabled in settings
    private fun printLogo(lineApi: com.sunmi.printerx.api.LineApi, printParams: PrintingData) {
        try {
            if (!printParams.printIcon || logo.isEmpty()) return

            // Decode the Base64 logo
            val decodedString: ByteArray = Base64.decode(logo, Base64.DEFAULT)
            val bmp = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.size)

            if (bmp != null) {
                // Print the logo
                lineApi.printBitmap(bmp, com.sunmi.printerx.style.BitmapStyle.getStyle().setAlign(Align.CENTER))
                lineApi.printText(" ", TextStyle.getStyle())
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing logo: ${e.message}")
        }
    }

    // Print establishment information if enabled in settings
    private fun printEstablishmentInfo(lineApi: com.sunmi.printerx.api.LineApi, printParams: PrintingData) {
        try {
            if (!printParams.printCompanyName) return

            // Print establishment name
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.CENTER))
            lineApi.printText(etablisement.desgEt ?: "N/A", TextStyle.getStyle().setTextSize(16).enableBold(true))

            // Print establishment address
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
            lineApi.printText("Adresse: ${etablisement.etAdresse ?: "N/A"}", TextStyle.getStyle().setTextSize(14))

            // Print establishment phone
            lineApi.printText("Tel: ${etablisement.etTel1 ?: etablisement.etTel2 ?: "N/A"}", TextStyle.getStyle().setTextSize(14))

            // Print establishment fiscal ID
            lineApi.printText("MF: ${etablisement.etMAtriculeF ?: "N/A"}", TextStyle.getStyle().setTextSize(14))

            // Print separator
            lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing establishment info: ${e.message}")
        }
    }

    // Print client information
    private fun printClientInfo(lineApi: com.sunmi.printerx.api.LineApi, client: Client?, clientName: String?, clientCode: String?) {
        try {
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

            // Print client name
            if (!clientName.isNullOrEmpty()) {
                lineApi.printText("Client: $clientName", TextStyle.getStyle().setTextSize(14))
            }

            // Print client code
            if (!clientCode.isNullOrEmpty()) {
                lineApi.printText("Code Client: $clientCode", TextStyle.getStyle().setTextSize(14))
            }

            // Print client fiscal ID if available
            if (client != null && !client.cLIMatFisc.isNullOrEmpty()) {
                lineApi.printText("MF Client: ${client.cLIMatFisc}", TextStyle.getStyle().setTextSize(14))
            }

            // Print separator
            lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing client info: ${e.message}")
        }
    }

    // Print operator information
    private fun printOperatorInfo(lineApi: com.sunmi.printerx.api.LineApi, idCaisse: String?, utilisateur: Utilisateur) {
        try {
            lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))

            // Print cash register ID if available
            if (!idCaisse.isNullOrEmpty()) {
                lineApi.printText("Caisse: $idCaisse", TextStyle.getStyle().setTextSize(14))
            }

            // Print operator name
            lineApi.printText("Operateur: ${utilisateur.Nom ?: ""} ${utilisateur.Prenom ?: ""}", TextStyle.getStyle().setTextSize(14))

            // Print separator
            lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing operator info: ${e.message}")
        }
    }

    // Print footer with app version if enabled
    private fun printFooter(lineApi: com.sunmi.printerx.api.LineApi, client: Client?, printParams: PrintingData) {
        try {
            // Print client balance if enabled
            if (printParams.printClientSold && client != null) {
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.LEFT))
                lineApi.printText("Solde Client: ${formatPrice(client.cLISolde.toString())}", TextStyle.getStyle().setTextSize(14))
            }

            // Print app version if enabled
            if (printParams.printAppVersion) {
                lineApi.initLine(BaseStyle.getStyle().setAlign(Align.RIGHT))
                lineApi.printText(MobileCodeGeneration.versionCode, TextStyle.getStyle().setTextSize(12))
            }

            // Print signature boxes if enabled
            if (printParams.printCachet) {
                lineApi.printText("----------------------------------------------------------------", TextStyle.getStyle().setTextSize(16))

                if (defaultPaperWidth == PAPER_WIDTH_80MM) {
                    // 80mm paper signature boxes
                    lineApi.printText("+--------------------+  +-------------------+ ", TextStyle.getStyle().setTextSize(16))
                    lineApi.printText("|                    |  |                   | ", TextStyle.getStyle().setTextSize(16))
                    lineApi.printText("|                    |  |                   | ", TextStyle.getStyle().setTextSize(16))
                    lineApi.printText("|                    |  |                   | ", TextStyle.getStyle().setTextSize(16))
                    lineApi.printText("+--------------------+  +-------------------+ ", TextStyle.getStyle().setTextSize(16))
                    lineApi.printText("  Cachet et signature     Cachet et signature ", TextStyle.getStyle().setTextSize(16))
                    lineApi.printText("      fournisseur               client ", TextStyle.getStyle().setTextSize(16))
                } else {
                    // 58mm paper signature boxes
                    lineApi.printText("+--------------+ +-------------+", TextStyle.getStyle().setTextSize(12))
                    lineApi.printText("|              | |             |", TextStyle.getStyle().setTextSize(12))
                    lineApi.printText("|              | |             |", TextStyle.getStyle().setTextSize(12))
                    lineApi.printText("+--------------+ +-------------+", TextStyle.getStyle().setTextSize(12))
                    lineApi.printText("   Fournisseur       Client", TextStyle.getStyle().setTextSize(12))
                }
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error printing footer: ${e.message}")
        }
    }

    // Check printer paper type and adjust layout accordingly
    fun checkPrinterPaper(printer: PrinterSdk.Printer): String {
        return try {
            val paper = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.PAPER)
            val printerType = printer.queryApi().getInfo(com.sunmi.printerx.enums.PrinterInfo.TYPE)

            when(paper) {
                PAPER_WIDTH_58MM -> {
                    Log.d("SunmiPrintManager", "Printer paper: 58mm")
                    PAPER_WIDTH_58MM
                }
                PAPER_WIDTH_80MM -> {
                    Log.d("SunmiPrintManager", "Printer paper: 80mm")
                    PAPER_WIDTH_80MM
                }
                else -> {
                    if(printerType == com.sunmi.printerx.enums.PrinterType.THERMAL.toString()) {
                        Log.d("SunmiPrintManager", "Printer paper: Default to ${defaultPaperWidth} for thermal printer")
                        defaultPaperWidth
                    } else {
                        Log.d("SunmiPrintManager", "Non-thermal printer")
                        "Unknown"
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error checking printer paper: ${e.message}")
            defaultPaperWidth // Default to our preferred paper width
        }
    }

    // Set the paper width for the printer
    fun setPaperWidth(printer: PrinterSdk.Printer?, paperWidth: String = PAPER_WIDTH_80MM) {
        try {
            if (printer == null) {
                Log.e("SunmiPrintManager", "Cannot set paper width: printer is null")
                return
            }

            // Update the default paper width
            defaultPaperWidth = paperWidth

            // Try to set the paper width on the printer
            // Note: Not all printers support changing paper width programmatically
            Log.d("SunmiPrintManager", "Setting paper width to $paperWidth")

            // For Sunmi printers, we can adjust the print width in the line API
            val lineApi = printer.lineApi()
            if (lineApi != null) {
                val printWidth = when(paperWidth) {
                    PAPER_WIDTH_80MM -> 576 // 72mm print width (80mm paper)
                    PAPER_WIDTH_58MM -> 384 // 48mm print width (58mm paper)
                    else -> 576 // Default to 80mm
                }

                // For Sunmi printers, we can't directly set the print width
                // but we can adjust the text and formatting based on paper width
                try {
                    // Instead of trying to set the width directly, we'll just log the intended width
                    // and adjust our formatting in the print methods
                    Log.d("SunmiPrintManager", "Using paper width: $paperWidth (print width: $printWidth)")

                    // Store the paper width in a variable that can be accessed by the print methods
                    defaultPaperWidth = paperWidth

                    // We'll adjust the text size and formatting in each print method based on the paper width
                    // For 80mm paper, we'll use larger text sizes and wider separators
                } catch (e: Exception) {
                    Log.e("SunmiPrintManager", "Error configuring printer: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e("SunmiPrintManager", "Error setting paper width: ${e.message}")
        }
    }
}
