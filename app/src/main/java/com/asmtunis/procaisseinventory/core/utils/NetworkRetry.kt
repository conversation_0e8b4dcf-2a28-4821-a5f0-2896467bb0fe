package com.asmtunis.procaisseinventory.core.utils

import android.util.Log
import kotlinx.coroutines.delay
import kotlin.math.min
import kotlin.math.pow
import kotlin.random.Random

/**
 * Utility class for handling network retries with exponential backoff.
 */
object NetworkRetry {
    private const val TAG = "NetworkRetry"
    
    /**
     * Executes a network operation with exponential backoff retry logic.
     *
     * @param maxRetries Maximum number of retry attempts
     * @param initialDelayMs Initial delay in milliseconds before the first retry
     * @param maxDelayMs Maximum delay in milliseconds between retries
     * @param factor Exponential factor for backoff calculation
     * @param jitter Whether to add random jitter to the delay
     * @param retryOnException Function that determines if a retry should be attempted for a given exception
     * @param operation The suspend function to execute
     *
     * @return The result of the operation if successful
     * @throws Exception The last exception encountered if all retries fail
     */
    suspend fun <T> withRetry(
        maxRetries: Int = 3,
        initialDelayMs: Long = 1000,
        maxDelayMs: Long = 20000,
        factor: Double = 2.0,
        jitter: Boolean = true,
        retryOnException: (Exception) -> Boolean = { true },
        operation: suspend () -> T
    ): T {
        var currentDelay = initialDelayMs
        var retryCount = 0
        
        while (true) {
            try {
                return operation()
            } catch (e: Exception) {
                retryCount++
                
                if (retryCount > maxRetries || !retryOnException(e)) {
                    Log.e(TAG, "Operation failed after $retryCount retries", e)
                    throw e
                }
                
                // Calculate the next delay with exponential backoff
                val nextDelay = if (jitter) {
                    calculateJitteredDelay(currentDelay, factor, maxDelayMs)
                } else {
                    calculateDelay(currentDelay, factor, maxDelayMs)
                }
                
                Log.d(TAG, "Retry $retryCount after $nextDelay ms")
                delay(nextDelay)
                currentDelay = nextDelay
            }
        }
    }
    
    /**
     * Calculate the next delay using exponential backoff without jitter.
     */
    private fun calculateDelay(currentDelay: Long, factor: Double, maxDelayMs: Long): Long {
        return min((currentDelay * factor).toLong(), maxDelayMs)
    }
    
    /**
     * Calculate the next delay using exponential backoff with jitter.
     * Jitter helps to avoid thundering herd problems in distributed systems.
     */
    private fun calculateJitteredDelay(currentDelay: Long, factor: Double, maxDelayMs: Long): Long {
        val baseDelay = min((currentDelay * factor).toLong(), maxDelayMs)
        val jitterFactor = 1.0 + Random.nextDouble(-0.1, 0.1) // Add ±10% jitter
        return (baseDelay * jitterFactor).toLong()
    }
}
