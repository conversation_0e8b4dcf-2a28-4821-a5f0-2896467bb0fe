package com.asmtunis.procaisseinventory.core.utils

import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentCheck
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels

object Sync {
    fun isSyncInProCaisse(
        syncProcaisseViewModels: SyncProcaisseViewModels,
        syncSharedViewModels: SyncSharedViewModels,
    ): Boolean {
        return  syncProcaisseViewModels.isLoading
    }

    fun syncAllProCaisse(
        syncProcaisseViewModels: SyncProcaisseViewModels,
    ) {

        val syncInvBatimentViewModel = syncProcaisseViewModels.syncInvBatimentViewModel
        val syncInvPatrimoineViewModel = syncProcaisseViewModels.syncInvPatrimoineViewModel
        val syncBonRetourViewModel = syncProcaisseViewModels.syncBonRetourViewModel
        val syncBonCommandeViewModel = syncProcaisseViewModels.syncBonCommandeViewModel
        val syncBLVM = syncProcaisseViewModels.syncBonLivraisonVM
        val syncDistNumViewModel = syncProcaisseViewModels.syncDistNumViewModel
        val syncClientViewModel = syncProcaisseViewModels.syncClientViewModel
        val syncReglementViewModel = syncProcaisseViewModels.syncReglementViewModel
        val syncVcViewModel = syncProcaisseViewModels.syncVcViewModel

        /**
         * *********************
         *    INV PAT CODE BARE BATIMENT
         * *********************
         */
        if (syncInvBatimentViewModel.batimentCBNotSync.isNotEmpty()) {
            syncInvBatimentViewModel.syncAffectCodeBareBatiment(batimentCheck = BatimentCheck(
                cLICode = syncInvBatimentViewModel.batimentCBNotSync.first().cLICode,
                cltImoCB = syncInvBatimentViewModel.batimentCBNotSync.first().cliImoCB!!
            )
            )
        }
        /**
         * *********************
         *    INV PAT CODE BARE BATIMENT
         * *********************
         */


        /**
         * *********************
         *    INV PAT INVENTAIRE
         * *********************
         */
        if (syncInvPatrimoineViewModel.inventaireNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncInventaire()
        }

        if (syncInvPatrimoineViewModel.inventaireBatimentNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncInventaireBatiment()
        }

        if (syncInvPatrimoineViewModel.inventaireBatimentImageNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncImageInventaireBatiment()
        }

        /**
         * *********************
         *    INV PAT AFFECTATION
         * *********************
         */
        if (syncInvPatrimoineViewModel.affectationNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncAffectation()
        }

        if (syncInvPatrimoineViewModel.affectationBatimentNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncAffectationBatiment()
        }

        if (syncInvPatrimoineViewModel.affectationBatimentImageNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncImageAffectationBatiment()
        }

        /**
         * *********************
         *    INV PAT DEP OUT
         * *********************
         */
        if (syncInvPatrimoineViewModel.depOutNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncDepOut()
        }

        if (syncInvPatrimoineViewModel.depOutBatimentNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncDepOutBatiment()
        }

        if (syncInvPatrimoineViewModel.depOutBatimentImageNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncImageDepOutBatiment()
        }

        /**
         * *********************
         *    INV PAT DEP IN
         * *********************
         */
        if (syncInvPatrimoineViewModel.depInNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncDepIn()
        }

        if (syncInvPatrimoineViewModel.depInBatimentNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncDepInBatiment()
        }

        if (syncInvPatrimoineViewModel.depInBatimentImageNotSync.isNotEmpty()) {
            syncInvPatrimoineViewModel.syncImageDepInBatiment()
        }

        /**
         * *********************
         *    Bon Retour
         * *********************
         */
        if (syncBonRetourViewModel.bonRetourNotSync.isNotEmpty()) {
            syncBonRetourViewModel.syncBonRetour()
        }

        /**
         * *********************
         *    Bon Commande
         * *********************
         */
        if (syncBonCommandeViewModel.bonCommandeNotSync.isNotEmpty()) {
            syncBonCommandeViewModel.syncBonCommande()
        }

        /**
         * *********************
         *    Bon Livraison
         * *********************
         */
        if (syncBLVM.ticketsWithLinesAndPaymentsNotSync.isNotEmpty()) {
            syncBLVM.syncBonLivraison()
        }

        /**
         * *********************
         *    CLIENT
         * *********************
         */
        if (syncClientViewModel.clientsNotSync.isNotEmpty()) {
            syncClientViewModel.syncClients(syncClientViewModel.clientsNotSync)
        }

        /**
         * *********************
         *    Reglement Libre
         * *********************
         */
        if (syncReglementViewModel.reglementLibreNotSync.isNotEmpty()) {
            syncReglementViewModel.syncReglementLibre()
        }

        /**
         * *********************
         *    DISTRIBUTION NUM
         * *********************
         */
        if (syncDistNumViewModel.visiteDnNotSyncToDelete.isNotEmpty()) {
            syncDistNumViewModel.syncVisitesDnToDelete(syncDistNumViewModel.visiteDnNotSyncToDelete)
        }
        if (syncDistNumViewModel.visiteDnNotSync.isNotEmpty()) {
            syncDistNumViewModel.syncVisitesDn(syncDistNumViewModel.visiteDnNotSync)
        }

        /**
         * *********************
         *    VC
         * *********************
         */

        if (syncVcViewModel.imagesNotSync.isNotEmpty()) {
            syncVcViewModel.syncImages(syncVcViewModel.imagesNotSync)
        }

        if (syncVcViewModel.promoVcNotSync.isNotEmpty()) {
            syncVcViewModel.syncPromoVc(syncVcViewModel.promoVcNotSync)
        }

        if (syncVcViewModel.prixVcNotSync.isNotEmpty()) {
            syncVcViewModel.syncPrixVc(syncVcViewModel.prixVcNotSync)
        }

        if (syncVcViewModel.autreVcNotSync.isNotEmpty()) {
            syncVcViewModel.syncAutreVc(syncVcViewModel.autreVcNotSync)
        }

        if (syncVcViewModel.newProductVcNotSync.isNotEmpty()) {
            syncVcViewModel.syncNewProductsVc(syncVcViewModel.newProductVcNotSync)
        }

        if (syncVcViewModel.newProductDeletedNotSync.isNotEmpty()) {
            syncVcViewModel.syncNewProductToDelete(syncVcViewModel.newProductDeletedNotSync)
        }
        if (syncVcViewModel.autreDeletedNotSync.isNotEmpty()) {
            syncVcViewModel.syncAutreToDelete(syncVcViewModel.autreDeletedNotSync)
        }

        if (syncVcViewModel.prixDeletedNotSync.isNotEmpty()) {
            syncVcViewModel.syncPrixToDelete(syncVcViewModel.prixDeletedNotSync)
        }
        if (syncVcViewModel.promoDeletedNotSync.isNotEmpty()) {
            syncVcViewModel.syncPromoToDelete(syncVcViewModel.promoDeletedNotSync)
        }
    }

    fun getProCaisseTotalNoSyncCount(
        syncProcaisseViewModels: SyncProcaisseViewModels,
    ): Int =
                syncProcaisseViewModels.syncClientViewModel.clientsNotSync.size +
                syncProcaisseViewModels.syncDistNumViewModel.visiteDnNotSyncToDelete.size +
                syncProcaisseViewModels.syncDistNumViewModel.visiteDnNotSyncToDelete.size +
                syncProcaisseViewModels.syncDistNumViewModel.visiteDnNotSync.size +

                syncProcaisseViewModels.syncVcViewModel.imagesNotSync.size +
                syncProcaisseViewModels.syncVcViewModel.promoVcNotSync.size +
                syncProcaisseViewModels.syncVcViewModel.prixVcNotSync.size +
                syncProcaisseViewModels.syncVcViewModel.autreVcNotSync.size +
                syncProcaisseViewModels.syncVcViewModel.newProductVcNotSync.size +

                syncProcaisseViewModels.syncVcViewModel.newProductDeletedNotSync.size +
                syncProcaisseViewModels.syncVcViewModel.autreDeletedNotSync.size +
                syncProcaisseViewModels.syncVcViewModel.promoDeletedNotSync.size +
                syncProcaisseViewModels.syncVcViewModel.prixDeletedNotSync.size +

                syncProcaisseViewModels.syncReglementViewModel.reglementLibreNotSync.size +

                syncProcaisseViewModels.syncBonRetourViewModel.bonRetourNotSync.size +

                syncProcaisseViewModels.syncInvPatrimoineViewModel.affectationNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.depInNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.depOutNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.inventaireNotSync.size +

                syncProcaisseViewModels.syncInvPatrimoineViewModel.affectationBatimentNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.depInBatimentNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.depOutBatimentNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.inventaireBatimentNotSync.size +

                syncProcaisseViewModels.syncInvPatrimoineViewModel.affectationBatimentImageNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.depOutBatimentImageNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.depInBatimentImageNotSync.size +
                syncProcaisseViewModels.syncInvPatrimoineViewModel.inventaireBatimentImageNotSync.size +


                syncProcaisseViewModels.syncInvBatimentViewModel.batimentCBNotSync.size +

                syncProcaisseViewModels.syncBonCommandeViewModel.bonCommandeNotSync.size +
                syncProcaisseViewModels.syncBonLivraisonVM.ticketsWithLinesAndPaymentsNotSync.size +
                syncProcaisseViewModels.syncTourneeViewModel.lgOrdMissionNotSync.size



    fun getProInventoryTotalNoSyncCount(
        syncInventoryViewModel: SyncInventoryViewModel,
    ): Int =
                syncInventoryViewModel.bonEntreeNotSync.size +
                syncInventoryViewModel.bonTransfertNotSync.size +
                syncInventoryViewModel.inventaireNotSync.size +
                syncInventoryViewModel.ticketRayonNotSync.size

    fun getSharedTotalNoSyncCount(
        syncSharedViewModels: SyncSharedViewModels,
    ): Int =
        syncSharedViewModels.syncArticlesViewModel.familleNotSync.size +
                syncSharedViewModels.syncArticlesViewModel.marqueNotSync.size +
                syncSharedViewModels.syncArticlesViewModel.articleSync.size




    fun isSyncInProInventory(
        syncSharedViewModels: SyncSharedViewModels,
        syncInventoryViewModel: SyncInventoryViewModel,
    ): Boolean {
        return syncSharedViewModels.syncArticlesViewModel.addFamilleState.loading ||
                syncSharedViewModels.syncArticlesViewModel.marqueState.loading ||
                syncSharedViewModels.syncArticlesViewModel.articleState.loading ||
                syncInventoryViewModel.responseAddAchatState.loading ||
                syncInventoryViewModel.responseAddBonTransfertState.loading ||
                syncInventoryViewModel.responseAddInventaireState.loading ||
                syncInventoryViewModel.responseAddTicketRayonState.loading
    }

    fun syncAllProInventory(
        syncSharedViewModels: SyncSharedViewModels,
        syncInventoryViewModel: SyncInventoryViewModel,
    ) {
        val syncArticlesViewModel = syncSharedViewModels.syncArticlesViewModel
        /**
         * *********************
         *    FAMILLE
         * *********************
         */
        if (syncArticlesViewModel.familleNotSync.isNotEmpty()) {
            syncArticlesViewModel.syncFamille(syncArticlesViewModel.familleNotSync)
        }

        /**
         * *********************
         *    MARQUE
         * *********************
         */
        if (syncArticlesViewModel.marqueNotSync.isNotEmpty()) {
            syncArticlesViewModel.syncMarque(syncArticlesViewModel.marqueNotSync)
        }

        /**
         * *********************
         *    ARTICLE
         * *********************
         */
        if (syncArticlesViewModel.articleSync.isNotEmpty()) {
            syncArticlesViewModel.syncArticle()
        }

        /**
         * *********************
         *    A C H A T
         * *********************
         */
        if (syncInventoryViewModel.bonEntreeNotSync.isNotEmpty()) {
            syncInventoryViewModel.syncAchat()
        }

        /**
         * *********************
         *   B O N  ***** T R A N S F E R T
         * *********************
         */
        if (syncInventoryViewModel.bonTransfertNotSync.isNotEmpty()) {
            syncInventoryViewModel.syncBnTransfert()
        }

        /**
         * *********************
         *   I N V E N T A I R E
         * *********************
         */
        if (syncInventoryViewModel.inventaireNotSync.isNotEmpty()) {
            syncInventoryViewModel.syncInventaire()
        }

        /**
         * *********************
         *   T I C K E T **** R A Y O N
         * *********************
         */
        if (syncInventoryViewModel.ticketRayonNotSync.isNotEmpty()) {
            syncInventoryViewModel.syncTicketRayon()
        }
    }

}