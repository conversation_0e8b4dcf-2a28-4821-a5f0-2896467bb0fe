package com.asmtunis.procaisseinventory.core.local_storage.localdb.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ClientArticlePrix
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ArticleCodeBarDAO
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ArticleDAO
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ClientArticlePrixDAO
import com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine.PricePerStation
import com.asmtunis.procaisseinventory.articles.data.priceperstation.local.dao.PricePerStationDAO
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.articles.data.unite_article.local.dao.UniteArticleDAO
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.base_config.data.local.dao.BaseConfigDAO
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.auth.login.data.local.dao.AuthorizationDAO
import com.asmtunis.procaisseinventory.auth.login.data.local.dao.UtilisateurDAO
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.dao.LicenceDAO
import com.asmtunis.procaisseinventory.core.utils.AuthorizationTypeConverter
import com.asmtunis.procaisseinventory.core.utils.LicenceTypeConverter
import com.asmtunis.procaisseinventory.core.utils.StatisticsTypeConverter
import com.asmtunis.procaisseinventory.core.utils.StringTypeConverter
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.banques.local.dao.BanqueDAO
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.data.carte_resto.local.dao.CarteRestoDAO
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.asmtunis.procaisseinventory.data.cheque_caisse.local.dao.ChequeCaisseDAO
import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import com.asmtunis.procaisseinventory.data.devise.local.dao.DeviseDAO
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import com.asmtunis.procaisseinventory.data.etablisement.local.dao.EtablisementDAO
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.exercice.local.dao.ExerciceDAO
import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import com.asmtunis.procaisseinventory.data.facture.local.dao.FactureDAO
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.famille.local.dao.FamilleDAO
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.image_piece_joint.local.dao.InventairePieceJointDAO
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.data.marque.local.dao.MarqueDAO
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.parametrages.local.dao.ParametrageDAO
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.data.prefixe.local.dao.PrefixeDAO
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.dao.SessionCaisseDAO
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.station.local.station.dao.StationDAO
import com.asmtunis.procaisseinventory.data.station.local.station_stock_article.dao.StationStockArticleDAO
import com.asmtunis.procaisseinventory.data.statistiques.domaine.Statistics
import com.asmtunis.procaisseinventory.data.statistiques.local.dao.StatisticsDAO
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.data.ticket_resto.local.dao.TraiteCaisseDAO
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.timbre.local.dao.TimbreDAO
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.tva.local.dao.TvaDAO
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.data.unite.local.dao.UniteDAO
import com.asmtunis.procaisseinventory.data.ville.domaine.Ville
import com.asmtunis.procaisseinventory.data.ville.local.dao.VilleDAO
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.network_errors.local.dao.NetworkErrorsDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.bon_commande.dao.BonCommandeDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.local.ligne_bon_commande.dao.LigneBonCommandeDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ligne_ticket.dao.LigneTicketDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.local.ticket.dao.TicketDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.bon_retour.dao.BonRetourDAO
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.local.ligne_bon_retour.dao.LigneBonRetourDAO
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.client.data.local.dao.ClientDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.FamilleDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.SuperficieDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypePointVenteDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.TypeServicesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.VisitesDn
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.famille.dao.FamilleDnDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.superficie.dao.SuperficieDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typepointvente.dao.TypePointVenteDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.typeservice.dao.TypeServicesDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao.LigneVisiteDAO
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.local.visite.dao.VisiteDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.local.dao.DeplacementOutByUserDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.BatimentByUser
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.Immobilisation
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.domaine.TypeMouvement
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.BatimentsByUserDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.ImmobilisationDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.TypeMouvementDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.dao.InventaireDAO
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.dao.ReglementCaisseDAO
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.dao.EtatOrdreMissionDAO
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.dao.LigneOrdreMissionDAO
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.dao.OrdreMissionDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.domain.AutreVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.autre.data.local.dao.AutreVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.ConcurrentVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.domaine.TypeCommunicationVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.image.dao.ImageVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.liste_concurrent.dao.ListeConcurrentVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.data.local.type_communication.dao.TypeCommunicationVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.domaine.NewProductVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.new_product.data.local.dao.NewProductVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.domaine.PrixVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.prix.data.local.dao.PrixVCDAO
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.domaine.PromoVC
import com.asmtunis.procaisseinventory.pro_caisse.veille_concurentiel.promotion.data.local.dao.PromoVCDAO
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.dao.BonEntreeDAO
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.dao.LigneBonEntreeDAO
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.bn_livraison.dao.BonLivraisonDAO
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.dao.LigneBonLivraisonDAO
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.dao.FournisseurDAO
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.dao.TypePrixUnitaireHTDAO
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.dao.LigneInventaireDAO
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.dao.TicketRayonDAO


@Database(
    entities = [
        BaseConfig::class,
        Client::class,
        Utilisateur::class,
        Authorization::class,
        Article::class,
        ArticleCodeBar::class,
        ClientArticlePrix::class,
        Etablisement::class,
        Statistics::class,
        Prefixe::class,
        PricePerStation::class,
        SessionCaisse::class,
        TypeServicesDn::class,
        SuperficieDn::class,
        TypePointVenteDn::class,
        VisitesDn::class,
        LigneVisitesDn::class,
        FamilleDn::class,
        ConcurrentVC::class,
        NewProductVC::class,
        PromoVC::class,
        PrixVC::class,
        AutreVC::class,
        TypeCommunicationVC::class,
        Licence::class,
        Exercice::class,
        ReglementCaisse::class,
        Ticket::class,
        LigneTicket::class,
        BonCommande::class,
        LigneBonCommande::class,
        Banque::class,
        CarteResto::class,
        ChequeCaisse::class,
        Devise::class,
        Timbre::class,
        BonRetour::class,
        LigneBonRetour::class,
        TraiteCaisse::class,
        Ville::class,
        OrdreMission::class,
        LigneOrdreMission::class,
        EtatOrdreMission::class,
        Immobilisation::class,
        ImagePieceJoint::class,
        BatimentByUser::class,
        TypeMouvement::class,
        DeplacementOutByUser::class,
        Facture::class,
        NetworkError::class,
        /**
         * Inventory
         */

        Station::class,
        StationStockArticle::class,
        TypePrixUnitaireHT::class,
        Tva::class,
        Famille::class,
        Marque::class,
        BonEntree::class,
        LigneBonEntree::class,
        BonLivraison::class,
        LigneBonLivraison::class,
        Inventaire::class,
        LigneInventaire::class,
        Fournisseur::class,
        TicketRayon::class,
        Parametrages::class,
        Unite::class,
        UniteArticle::class
    ],
    version = 4,
    exportSchema = false
)
@TypeConverters(
    AuthorizationTypeConverter::class,
    LicenceTypeConverter::class,
    StringTypeConverter::class,
    StatisticsTypeConverter::class
)

abstract class ProCaisseDataBase : RoomDatabase() {
//    override fun clearAllTables() {
//        // Clears all tables in the database
//        this.clearAllTables()
//    }

    abstract fun uniteDAO(): UniteDAO
    abstract fun networkErrorDAO(): NetworkErrorsDAO
    abstract fun factureDAO(): FactureDAO
    abstract fun deplacementOutByUserDAO(): DeplacementOutByUserDAO
    abstract fun typeMouvementDao(): TypeMouvementDAO
    abstract fun immobilisationDao(): ImmobilisationDAO
    abstract fun batimentsByUserDao(): BatimentsByUserDAO
    abstract fun baseConfigDao(): BaseConfigDAO
    abstract fun licenceDao(): LicenceDAO
    abstract fun clientDAO(): ClientDAO
    abstract fun utilisateurDAO(): UtilisateurDAO
    abstract fun authorizationDAO(): AuthorizationDAO
    abstract fun articleDAO(): ArticleDAO
    abstract fun uniteArticleDAO(): UniteArticleDAO
    abstract fun articleCodeBarDAO(): ArticleCodeBarDAO
    abstract fun clientArticlePrixDAO(): ClientArticlePrixDAO
    abstract fun etablisementDAO(): EtablisementDAO
    abstract fun banqueDAO(): BanqueDAO
    abstract fun deviseDAO(): DeviseDAO
    abstract fun carteRestoDAO(): CarteRestoDAO
    abstract fun chequeCaisseDAO(): ChequeCaisseDAO
    abstract fun statisticsDAO(): StatisticsDAO
    abstract fun timbreDAO(): TimbreDAO
    abstract fun villeDAO(): VilleDAO
    abstract fun prefixeDAO(): PrefixeDAO
    abstract fun pricePerStationDAO(): PricePerStationDAO
    abstract fun sessionCaisseDAO(): SessionCaisseDAO
    abstract fun exerciceDAO(): ExerciceDAO
    abstract fun reglementCaisseDAO(): ReglementCaisseDAO
    abstract fun traiteCaisseDAO(): TraiteCaisseDAO

    abstract fun ticketDAO(): TicketDAO
    abstract fun ligneTicketDAO(): LigneTicketDAO


    abstract fun bonRetourDAO(): BonRetourDAO
    abstract fun ligneBonRetourDAO(): LigneBonRetourDAO

    abstract fun bonCommandeDAO(): BonCommandeDAO
    abstract fun inventairePatrimoineDAO(): InventaireDAO
    abstract fun inventairePieceJointDAO(): InventairePieceJointDAO
    abstract fun ligneBonCommandeDAO(): LigneBonCommandeDAO


    abstract fun ordreMissionDAO(): OrdreMissionDAO
    abstract fun ligneOrdreMissionDAO(): LigneOrdreMissionDAO
    abstract fun etatOrdreMissionDAO(): EtatOrdreMissionDAO
    // DISTRIBUTION NUMERIQUE
    abstract fun typeServicesDAO(): TypeServicesDAO
    abstract fun familleDnDAO(): FamilleDnDAO
    abstract fun superficieDAO(): SuperficieDAO
    abstract fun TypePointVenteDAO(): TypePointVenteDAO
    abstract fun VisiteDAO(): VisiteDAO
    abstract fun LigneVisiteDAO(): LigneVisiteDAO

    // VEILLE CONCURENTIEL
    abstract fun ListeConcurrentVCDAO(): ListeConcurrentVCDAO
    abstract fun newProductVCDAO(): NewProductVCDAO
    abstract fun PromoVCDAO(): PromoVCDAO
    abstract fun PrixVCDAO(): PrixVCDAO
    abstract fun AutreVCDAO(): AutreVCDAO
    abstract fun TypeCommunicationVCDAO(): TypeCommunicationVCDAO
    abstract fun ImageVCDAO(): ImageVCDAO


    /**
     *  INVENTORY
     */


    abstract fun stationArticleDAO(): StationStockArticleDAO

    abstract fun stationDAO(): StationDAO
    abstract fun parametragesDAO(): ParametrageDAO
    abstract fun typePrixDAO(): TypePrixUnitaireHTDAO
    abstract fun tvaDAO(): TvaDAO
     abstract fun familleDAO(): FamilleDAO
    abstract fun fournisseurDAO(): FournisseurDAO
    abstract fun marqueDAO(): MarqueDAO
    abstract fun bonEntreeDAO(): BonEntreeDAO
    abstract fun ligneBonEntreeDAO(): LigneBonEntreeDAO
    abstract fun bonLivraisonDAO(): BonLivraisonDAO
    abstract fun ligneBonLivraisonDAO(): LigneBonLivraisonDAO
    abstract fun inventaireDAO(): com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.inv.dao.InventaireDAO
    abstract fun ligneInventaireDAO(): LigneInventaireDAO
    abstract fun ticketRayonDAO(): TicketRayonDAO


}
