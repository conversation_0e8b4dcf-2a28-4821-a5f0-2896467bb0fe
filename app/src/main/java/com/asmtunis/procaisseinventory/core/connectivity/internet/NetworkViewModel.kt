package com.asmtunis.procaisseinventory.core.connectivity.internet

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NetworkViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val listenNetwork: ListenNetwork
) : ViewModel() {

    // val isConnected : Flow<Boolean> = listenNetwork.isConnected
    var isConnected by mutableStateOf(true)
        private set

  //  private val _isConnected = mutableStateOf(false)
  //  val isConnected: State<Boolean> = _isConnected
    init {
        getNetworkState()
    }
    private fun getNetworkState() = viewModelScope.launch {
        listenNetwork.isConnected.distinctUntilChanged().collectLatest {
            isConnected = it
        }
    }
}
