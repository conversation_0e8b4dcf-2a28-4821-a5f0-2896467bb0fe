package com.asmtunis.procaisseinventory.core.ktor

object Urls {
    const val CHECK_LICENCE_BASE_URL = "http://*************:2222/checkLicense/"

    const val LICENCE_BASE_URL = "https://as.asmhost.net"
    var BASE_URL = ""



    const val SELECTION_BASE_CONFIG = "/licence/reclamation/WS/selection_base_config.php"
    const val ADD_DEMANDE_LICENCE = "/proresto/WS/Add_demande_licence.php"

    const val AUTHENTIFICATION = "/Utilisateur/Authentification"
    const val GET_UTILISATEURS = "/Utilisateur/getUtilisateurs"
    
    const val CHECK_LICENCE = "/licence/public/api/checkLicenseV2"

    const val INSERT_USER_TOKEN = "/Utilisateur/insertUserToken?token="

    const val GET_BANQUES = "/Banque/getBanques"

    const val GET_ARTICLES = "/Article/getArticles"
    const val GET_ARTICLES_PAGINATION = "/Article/getArticleWithPagination"
    const val GET_ARTICLES_CODE_BARE_PAGINATION = "/Article/getCodeBarPagination"
    const val GET_COUNT_ARTICLE = "/Article/getCountArticle"
    const val ADD_ARTICLE_V2 = "/Article/addarticlev2"
    const val GET_ARTICLES_BY_STATION = "/Article/getArticlesByStation"
    const val GET_ARTICLES_CLIENT_PRIX = "/Article/getArticleClientPrix"

    const val GET_CODE_TEST = "/Article/getCodeTest"

//todo implemnt this SEE PROiNVENTORY APP
    const val GET_UNITE_ARTICLE = "/UniteArticle/getUniteArticles"
    const val GET_UNITE_ARTICLE_PAGINATION = "/UniteArticle/getUniteArticlesPagination"
    const val ADD_UNITE_ARTICLE = "/UniteArticle/addUniteArticleMobile"

    const val GET_UNITE = "/Unite/getUnites"
    const val ADD_UNITE_MOBILE = "/Unite/addUniteMobile"

    const val GET_PRICES_BY_STATION = "/Price/getPricesByStation"

    const val GET_CARTES_RESTO = "/CarteResto/getCartesResto"
    const val GET_CARTES_RESTO_BY_CODE = "/CarteResto/getCarteRestoByCode"


    const val GET_CHEQUE_BY_REGLEMENTS = "/ChequeCaisse/getChequeCaisseByReglements"
    const val GET_CHEQUE_BY_REGLEMENT = "/ChequeCaisse/getChequeCaisseByReglement"


    const val GET_DEVISES = "/Devise/getDevises"

    const val GET_ETABLISSEMENTS = "/Etablisement/getEtablisements"

    const val GET_EXERCICE = "/Exercice/getExercice"

    const val GET_FACTURE = "/facture/getFacture"


    const val GET_FAMILLES = "/Famille/getFamilles"
    const val ADD_FAMILLE_MOBILE = "/Famille/addFamilleMobile"

    const val GET_MARQUES = "/Marque/getMarques"
    const val ADD_MARQUE_MOBILE = "/Marque/addMarqueMobile"


    const val GET_PARAMETRAGE = "/Parametrage/getParametrage"

    const val GET_PREFIXES = "/Prefixe/getPrefixes"

    const val GET_SESSION_CAISSES = "/SessionCaisse/getSessionCaisses"
    const val ADD_SESSION_VENDEUR = "/SessionCaisse/addSessionVendeur"
    const val ADD_SESSION = "/SessionCaisse/addSession"
    const val CLOSE_SESSION_VENDEUR = "/SessionCaisse/closeSessionVendeur"
    const val GET_SESSION_CAISSE_BY_USER = "/SessionCaisse/getSessionCaisseByUser"

    const val GET_STATISTICS = "/Miscs/Statistics/getStatistics"

    const val GET_TRAITE_CAISSE_BY_REGLEMENTS = "/TraiteCaisse/getTraiteCaisseByReglements"

    const val GET_ALL_TIMBRE = "/Timbre/getAll"

    const val GET_TVA = "/TVA/getTVA"

    const val GET_VILLE = "/Ville/getville"

    const val GET_COMMANDE = "/Commande/getCommande"
    const val CONTROLE_INVENTAIRE = "/Commande/controleInventaire"
    const val ADD_BATCH_BON_COMMANDE = "/Commande/addBatchBonCommande"
    const val GET_LIGNE_COMMANDE = "/LigneCommande/getLigneCommande"
    const val ADD_BATCH_LIGNE_COMMANDE = "/LigneCommande/addBatchLigneCommande"
    const val UPLOAD_IMAGES = "/LigneCommande/upload"
    const val GET_IMAGES = "/LigneCommande/getImages"

    const val GET_LIGNE_TICKET_BY_TICKETS = "/LigneTicket/getLigneTicketByTickets"
    const val GET_TICKETS_BY_CAISSE_ID = "/Ticket/getTicketsByCaisseId"
    const val GET_MAX_NUM_TICKET = "/Ticket/getMaxNumTicket"
    const val ADD_BATCH_TICKETS_WITH_LIGNES_TICKET_AND_PAYMENT = "/Ticket/addBatchTicketWithLignesTicketAndPayment"


    const val ADD_BATCH_FACTURE_WITH_LINES = "/facture/addBatchFactureWithLines"


    const val GET_BON_RETOUR = "/Retour/getRetour"
    const val ADD_BATCH_BON_RETOUR = "/Retour/addBatchBonRetour"
    const val GET_LIGNE_BON_RETOUR = "/LigneRetour/getLigneRetour"
    const val ADD_BATCH_LIGNE_BON_RETOUR = "/LigneRetour/addBatchLigneRetour"


    const val GET_CLIENTS = "/Client/getClients"
    const val ADD_BATCH_CLIENT = "/Client/addBatchClient"

    const val GET_ALL_TYPE_SERVICE = "/Visite/getAllTypeService"
    const val GET_ALL_FAMILLE = "/Visite/getAllFamille"
    const val GET_ALL_SUPERFICIE = "/Visite/getAllSuperficie"
    const val GET_ALL_TYPE_P_VENTE = "/Visite/getAllTypePVente"
    const val GET_ALL_VISITE = "/Visite/getAllVisite"
    const val GET_ALL_LIGNE_VISITE = "/Visite/getAllLigneVisite"
    const val GET_VISITE_BY_CODE = "/Visite/getVisiteByCode"
    const val ADD_BATCH_VISITE = "/Visite/addBatchVisite"
    const val DELETE_VISITE = "/Visite/deleteVisite"



    const val GET_ALL_DEPLACEMENT_OUT_BY_USER = "/getAllDeplacememntOutByUser"
    const val GET_IMMOBILISATION = "/getImmobilisation"
    const val GET_BATIMENT_BY_USER = "/getBatimentByUser"
    const val GET_ALL_TYPE_MOUVEMENT = "/getAllTypeMouvement"
    const val AFFECT_CODE_BARE_BATIMENT = "/AffectCodeBareBatiment"

    const val GET_REGLEMENT_CAISSE_BY_TICKET = "/ReglementCaisse/getReglementCaisseByTicket"
    const val GET_REGLEMENT_CAISSE_BY_TICKETS = "/ReglementCaisse/getReglementCaisseByTickets"
    const val GET_REGLEMENT_CAISSE_BY_SESSION = "/ReglementCaisse/getReglementCaisseBySession"
    const val ADD_BATCH_PAYMENTS = "/ReglementCaisse/addBatchPayments"


    const val GET_ORDRE_MISSION_WITH_LINES = "/OrdreMission/displayordre"
    const val GET_ETAT_ORDRE_MISSION = "/OrdreMission/displayetatordremission"
    const val UPDATE_LIGNE_ORDRE_MISSION = "/OrdreMission/updateetatordremission"
    const val ADD_ORDRE_MISSION = "/OrdreMission/AddOrdreMission"
    const val BATCH_UPDATE_ORDRE_MISSION = "/OrdreMission/BatchUpdateOrdreMission"



    const val GET_VC_LISTE_CONCURRENT = "/VConcu/getVCListeConcurrent"
    const val GET_VC_NEW_PRODUCT = "/VConcu/getDataVConcu/VCLancementNP"
    const val GET_VC_PROMO = "/VConcu/getDataVConcu/VCPromo"
    const val GET_VC_PRIX = "/VConcu/getDataVConcu/VCPrix"
    const val GET_VC_AUTRE = "/VConcu/getDataVConcu/VCAutre"
    const val GET_VC_TYPE_COMMUNICATION = "/VConcu/getVCTypeCommunication"
    const val GET_VC_IMAGE = "/VConcu/getVCImage"
    const val ADD_BATCH_VC_IMAGE = "/VConcu/upload"
    const val ADD_BATCH_DATA_VC_CONCU = "/VConcu/addBatchDataVConcu"
    const val DELETE_DATA_VC_CONCU = "/VConcu/deleteDataVConcu"


    const val GET_BON_ENTREES = "/BonEntree/getBonEntrees"
    const val ADD_BATCH_BON_ENTREES_WITH_LINES = "/BonEntree/addBatchBonEntreesWithLines"
    const val GET_LIGNE_BON_ENTREES = "/LigneBonEntree/getLigneBonEntrees"


    const val GET_BON_LIVRAISON = "/BonLivraison/getBonLivraison"
    const val ADD_BATCH_BON_LIVRAISON_WITH_LINES = "/BonLivraison/addBatchBonLivraisonWithLines"
    const val GET_LIGNE_BON_LIVRAISON = "/LigneBonLivraison/getLigneBonLivraison"


    const val GET_FOURNISSEURS = "/Fournisseur/getFournisseurs"


    const val GET_STATIONS = "/Station/getStations"
    const val GET_STOCK_ARTICLE_BY_STATIONS = "/Station/getstockArticle"
    const val GET_STOCK_ARTICLE_BY_STATIONS_PAGINATION = "/Station/getstockArticlePagination"


    const val GET_TYPE_PRIX_UNITAIRE_HT = "/TypePrixUnitaire/getTypePrixUnitaireHT"


    const val GET_INVENTAIRES = "/Inventaire/getInventaires"
    const val ADD_BATCH_INVENTAIRES_WITH_LINES = "/Inventaire/addBatchInventairesWithLines"
    const val GET_LIGNE_INVENTAIRES = "/LigneInventaire/getLigneInventaires"
    const val GET_LIGNE_INVENTAIRES_BY_CODE = "/LigneInventaire/getLigneInventaires"


    const val ADD_TICKET_RAYON = "/TicketRayon/addTicketRayon"





}