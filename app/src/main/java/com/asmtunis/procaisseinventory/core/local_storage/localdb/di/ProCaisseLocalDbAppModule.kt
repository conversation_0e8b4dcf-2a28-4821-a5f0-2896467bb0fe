package com.asmtunis.procaisseinventory.core.local_storage.localdb.di

import android.content.Context
import androidx.room.Room
import androidx.room.RoomDatabase
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.PRO_CAISSE_DB_NAME
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class ProCaisseLocalDbAppModule {

    @Provides
    @Singleton
    @Named("ProCaisseRoomDatabase")
    fun provideProCaisseLocalDbCallback(): RoomDatabase.Callback {
        return object : RoomDatabase.Callback() {/*
          override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                db.execSQL("PRAGMA journal_mode=MEMORY")
                db.execSQL("PRAGMA cache_size=202400")
            }
        */
        }
    }

    @Provides
    @Singleton
    fun provideProCaisseAppDatabase(
        @ApplicationContext
        context: Context,
        @Named("ProCaisseRoomDatabase")  roomCallback: RoomDatabase.Callback
    ) = Room.databaseBuilder(
        context,
        ProCaisseDataBase::class.java,
        PRO_CAISSE_DB_NAME
    )
        .fallbackToDestructiveMigration(false)
        .addCallback(roomCallback)
        .setJournalMode(RoomDatabase.JournalMode.WRITE_AHEAD_LOGGING)
        .build()
}
