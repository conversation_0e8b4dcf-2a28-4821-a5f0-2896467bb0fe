package com.asmtunis.procaisseinventory.core.utils.mobilecode

import java.util.regex.Pattern
import kotlin.math.ceil

class Hashids @JvmOverloads constructor(
    salt: String? = DEFAULT_SALT,
    minHashLength: Int = 0,
    alphabet: String = DEFAULT_ALPHABET
) {
    private val salt: String
    private val minHashLength: Int
    private val alphabet: String
    private val seps: String
    private val guards: String

    init {
        var alphabt = alphabet
        this.salt = salt ?: DEFAULT_SALT
        this.minHashLength = if (minHashLength > 0) minHashLength else DEFAULT_MIN_HASH_LENGTH
        val uniqueAlphabet = StringBuilder()
        for (i in alphabt.indices) {
            if (uniqueAlphabet.indexOf(alphabt[i].toString()) == -1) {
                uniqueAlphabet.append(alphabt[i])
            }
        }
        alphabt = uniqueAlphabet.toString()
        require(alphabt.length >= MIN_ALPHABET_LENGTH) { "alphabet must contain at least $MIN_ALPHABET_LENGTH unique characters" }
        require(!alphabt.contains(" ")) { "alphabet cannot contains spaces" }

        // seps should contain only characters present in alphabet;
        // alphabet should not contains seps
        var seps = DEFAULT_SEPS
        for (i in seps.indices) {
            val j = alphabt.indexOf(seps[i])
            if (j == -1) {
                seps = seps.substring(0, i) + " " + seps.substring(i + 1)
            } else {
                alphabt = alphabt.substring(0, j) + " " + alphabt.substring(j + 1)
            }
        }
        alphabt = alphabt.replace("\\s+".toRegex(), "")
        seps = seps.replace("\\s+".toRegex(), "")
        seps = consistentShuffle(seps, this.salt)
        if (seps.isEmpty() || alphabt.length.toFloat() / seps.length > SEP_DIV) {
            var sepsLen = ceil(alphabt.length / SEP_DIV).toInt()
            if (sepsLen == 1) {
                sepsLen++
            }
            if (sepsLen > seps.length) {
                val diff = sepsLen - seps.length
                seps += alphabt.substring(0, diff)
                alphabt = alphabt.substring(diff)
            } else {
                seps = seps.substring(0, sepsLen)
            }
        }
        alphabt = consistentShuffle(alphabt, this.salt)
        // use double to round up
        val guardCount = ceil(alphabt.length.toDouble() / GUARD_DIV).toInt()
        val guards: String
        if (alphabt.length < 3) {
            guards = seps.substring(0, guardCount)
            seps = seps.substring(guardCount)
        } else {
            guards = alphabt.substring(0, guardCount)
            alphabt = alphabt.substring(guardCount)
        }
        this.guards = guards
        this.alphabet = alphabt
        this.seps = seps
    }

    /**
     * Encode numbers to string
     *
     * @param numbers the numbers to encode
     * @return the encoded string
     */
    fun encode(vararg numbers: Long): String {
        if (numbers.isEmpty()) {
            return ""
        }
        for (number in numbers) {
            if (number < 0) {
                return ""
            }
            require(number <= MAX_NUMBER) { "number can not be greater than " + MAX_NUMBER + "L" }
        }
        return encod(*numbers)
    }

    /**
     * Decode string to numbers
     *
     * @param hash the encoded string
     * @return decoded numbers
     */
    fun decode(hash: String): LongArray {
        if (hash.isEmpty()) {
            return LongArray(0)
        }
        val validChars = alphabet + guards + seps
        for (element in hash) {
            if (validChars.indexOf(element) == -1) {
                return LongArray(0)
            }
        }
        return decod(hash, alphabet)
    }

    /**
     * Encode hexa to string
     *
     * @param hexa the hexa to encode
     * @return the encoded string
     */
    fun encodeHex(hexa: String): String {
        if (!hexa.matches("^[0-9a-fA-F]+$".toRegex())) {
            return ""
        }
        val matched: MutableList<Long> = ArrayList()
        val matcher = Pattern.compile("[\\w\\W]{1,12}").matcher(hexa)
        while (matcher.find()) {
            matched.add(("1" + matcher.group()).toLong(16))
        }

        // conversion
        val result = LongArray(matched.size)
        for (i in matched.indices) {
            result[i] = matched[i]
        }
        return encode(*result)
    }

    /**
     * Decode string to numbers
     *
     * @param hash the encoded string
     * @return decoded numbers
     */
    fun decodeHex(hash: String): String {
        val result = StringBuilder()
        val numbers = decode(hash)
        for (number in numbers) {
            result.append(java.lang.Long.toHexString(number).substring(1))
        }
        return result.toString()
    }

    /* Private methods */
    private fun encod(vararg numbers: Long): String {
        var numberHashInt: Long = 0
        for (i in numbers.indices) {
            numberHashInt += numbers[i] % (i + 100)
        }
        var alphabet = alphabet
        val ret = alphabet[(numberHashInt % alphabet.length).toInt()]
        var num: Long
        var sepsIndex: Long
        var guardIndex: Long
        var buffer: String
        val ret_strB = StringBuilder(minHashLength)
        ret_strB.append(ret)
        var guard: Char
        for (i in numbers.indices) {
            num = numbers[i]
            buffer = ret.toString() + salt + alphabet
            alphabet = consistentShuffle(alphabet, buffer.substring(0, alphabet.length))
            val last = hash(num, alphabet)
            ret_strB.append(last)
            if (i + 1 < numbers.size) {
                if (last.isNotEmpty()) {
                    num %= (last[0].code + i).toLong()
                    sepsIndex = (num % seps.length).toInt().toLong()
                } else {
                    sepsIndex = 0
                }
                ret_strB.append(seps[sepsIndex.toInt()])
            }
        }
        var ret_str = ret_strB.toString()
        if (ret_str.length < minHashLength) {
            guardIndex = (numberHashInt + ret_str[0].code.toLong()) % guards.length
            guard = guards[guardIndex.toInt()]
            ret_str = guard.toString() + ret_str
            if (ret_str.length < minHashLength) {
                guardIndex = (numberHashInt + ret_str[2].code.toLong()) % guards.length
                guard = guards[guardIndex.toInt()]
                ret_str += guard
            }
        }
        val halfLen = alphabet.length / 2
        while (ret_str.length < minHashLength) {
            alphabet = consistentShuffle(alphabet, alphabet)
            ret_str = alphabet.substring(halfLen) + ret_str + alphabet.substring(0, halfLen)
            val excess = ret_str.length - minHashLength
            if (excess > 0) {
                val start_pos = excess / 2
                ret_str = ret_str.substring(start_pos, start_pos + minHashLength)
            }
        }
        return ret_str
    }

    private fun decod(hash: String, alphabet: String): LongArray {
        var alphaBet = alphabet
        val ret = ArrayList<Long>()
        var i = 0
        val regexp = "[$guards]"
        var hashBreakdown = hash.replace(regexp.toRegex(), " ")
        var hashArray =
            hashBreakdown.split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        if (hashArray.size == 3 || hashArray.size == 2) {
            i = 1
        }
        if (hashArray.isNotEmpty()) {
            hashBreakdown = hashArray[i]
            if (hashBreakdown.isNotEmpty()) {
                val lottery = hashBreakdown[0]
                hashBreakdown = hashBreakdown.substring(1)
                hashBreakdown = hashBreakdown.replace(("[$seps]").toRegex(), " ")
                hashArray =
                    hashBreakdown.split(" ".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                var subHash: String
                var buffer: String
                for (aHashArray in hashArray) {
                    subHash = aHashArray
                    buffer = lottery.toString() + salt + alphaBet
                    alphaBet = consistentShuffle(alphaBet, buffer.substring(0, alphaBet.length))
                    ret.add(unhash(subHash, alphaBet))
                }
            }
        }

        // transform from List<Long> to long[]
        var arr = LongArray(ret.size)
        for (k in arr.indices) {
            arr[k] = ret[k]
        }
        if (encode(*arr) != hash) {
            arr = LongArray(0)
        }
        return arr
    }

    /**
     * Get Hashid algorithm version.
     *
     * @return Hashids algorithm version implemented.
     */
    val version: String
        get() = "1.0.0"

    companion object {
        /**
         * Max number that can be encoded with Hashids.
         */
        const val MAX_NUMBER = 9007199254740992L
        private const val DEFAULT_ALPHABET =
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
        private const val DEFAULT_SEPS = "cfhistuCFHISTU"
        private const val DEFAULT_SALT = ""
        private const val DEFAULT_MIN_HASH_LENGTH = 0
        private const val MIN_ALPHABET_LENGTH = 16
        private const val SEP_DIV = 3.5
        private const val GUARD_DIV = 12
        fun checkedCast(value: Long): Int {
            val result = value.toInt()
            require(result.toLong() == value) {
                // don't use checkArgument here, to avoid boxing
                "Out of range: $value"
            }
            return result
        }

        private fun consistentShuffle(alphabet: String, salt: String): String {
            if (salt.isEmpty()) {
                return alphabet
            }
            var asc_val: Int
            var j: Int
            val tmpArr = alphabet.toCharArray()
            var i = tmpArr.size - 1
            var v = 0
            var p = 0
            while (i > 0) {
                v %= salt.length
                asc_val = salt[v].code
                p += asc_val
                j = (asc_val + v + p) % i
                val tmp = tmpArr[j]
                tmpArr[j] = tmpArr[i]
                tmpArr[i] = tmp
                i--
                v++
            }
            return String(tmpArr)
        }

        private fun hash(input: Long, alphabet: String): String {
            var inpt = input
            var hash = ""
            val alphabetLen = alphabet.length
            do {
                val index = (inpt % alphabetLen).toInt()
                if (index >= 0 && index < alphabet.length) {
                    hash = alphabet[index].toString() + hash
                }
                inpt /= alphabetLen.toLong()
            } while (inpt > 0)
            return hash
        }

        private fun unhash(input: String, alphabet: String): Long {
            var number: Long = 0
            var pos: Long
            for (element in input) {
                pos = alphabet.indexOf(element).toLong()
                number = number * alphabet.length + pos
            }
            return number
        }
    }
}