package com.asmtunis.procaisseinventory.core.utils

import android.graphics.Color
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

object ColorsUtils {
    fun convertColorToHue(color: Int): Float {
        return colorToHSV(abs(color))[0]
    }

    private fun colorToHSV(color: Int): FloatArray {
        val hsv = FloatArray(3)

        // Get the red, green, and blue components of the color.
        val r = Color.red(color)
        val g = Color.green(color)
        val b = Color.blue(color)


        // Calculate the hue value.
        val max = max(r, max(g, b))
        val min = min(r, min(g, b))
        val diff = max - min

        if (diff == 0) {
            hsv[0] = 0f
        } else if (max == r) {
            hsv[0] = (60f * ((g - b) / diff) + 360f) % 360f
        } else if (max == g) {
            hsv[0] = (60f * ((b - r) / diff) + 120f) % 360f
        } else if (max == b) {
            hsv[0] = (60f * ((r - g) / diff) + 240f) % 360f
        }

        // Calculate the saturation value.
        hsv[1] = if (max == 0) 0f else (diff / max) * 100f

        // Calculate the value value.
        hsv[2] = max * 100f




        return hsv
    }
}