package com.asmtunis.procaisseinventory.core.sync_workmanager

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.concurrent.TimeUnit
import javax.inject.Inject


    @HiltViewModel
    class SyncWorkerViewModel @Inject constructor(
        private val workManager: WorkManager,
        private val repository: DataStoreRepository,
    ) : ViewModel() {

   /*     init {
            viewModelScope.launch {
             if(repository.getBoolean(PROCAISSE_AUTO_SYNC_AUTHORISATION))
                 setProCaisseAutoSyncWork("1")
                else cancelProCaisseAutoSyncWork("cancel 1")
            }

        }*/
        //WorkManager
        fun setProCaisseAutoSyncWork(from : String) {
            Log.d("WorManager", "from "+ from)
            val constraints = Constraints.Builder()
              .setRequiredNetworkType(networkType = NetworkType.CONNECTED)
                //  .setRequiresCharging(true)
                //.setRequiresBatteryNotLow(true)
                .build()


            val workRequest = PeriodicWorkRequestBuilder<SyncWorker>(15, TimeUnit.MINUTES)
                .setConstraints(constraints)
                .build()

            workManager.enqueueUniquePeriodicWork(
                ProCaisseWORKER_KEY, ExistingPeriodicWorkPolicy.UPDATE, workRequest
            )
        }

        fun cancelProCaisseAutoSyncWork(from:String) {
            Log.d("WorManager", "cancelProCaisseAutoSyncWork "+ from)
            workManager.cancelUniqueWork(ProCaisseWORKER_KEY)
        }

        fun getProCaisseSyncWorkStatus(): LiveData<List<WorkInfo>> =
            workManager.getWorkInfosForUniqueWorkLiveData(ProCaisseWORKER_KEY)

        companion object {
            private val ProCaisseWORKER_KEY = "Procaisse_sync_worker"
        }
}