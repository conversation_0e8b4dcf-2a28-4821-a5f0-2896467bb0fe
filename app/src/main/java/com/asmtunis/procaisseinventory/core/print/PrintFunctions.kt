package com.asmtunis.procaisseinventory.core.print

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.BluetoothUtil
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.navigation.BluetoothConnectRoute
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.dokar.sonner.ToasterState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object PrintFunctions {
    fun print(
        context: Context,
        navigate: (route: Any) -> Unit,
        printParams: PrintingData,
        toaster: ToasterState,
        printViewModel: PrintViewModel,
        bluetoothVM: BluetoothViewModel,
        sunmiPrintManager: SunmiPrintManager? = null,
        toPrintBT: () -> Unit,
        toPrintWifi: () -> Unit = {},
        toPrintSunmi: () -> Unit = {}
    ) {
        Log.d("PrintFunctions", "Print method called with params: useSunmiPrinter=${printParams.useSunmiPrinter}, printViaWifi=${printParams.printViaWifi}")

        // For Sunmi and WiFi printing, show the note dialog immediately
        if (printParams.enableNotes && (printParams.useSunmiPrinter || printParams.printViaWifi)) {
            showNoteDialog(context, navigate, printParams, toaster, printViewModel, bluetoothVM, sunmiPrintManager, toPrintBT, toPrintWifi, toPrintSunmi)
            return
        }

        // For Bluetooth printing, we'll show the dialog after the device is selected

        // Process the print request directly
        processPrint(context, navigate, printParams, toaster, printViewModel, bluetoothVM, sunmiPrintManager, toPrintBT, toPrintWifi, toPrintSunmi, "")
    }

    private fun showNoteDialog(
        context: Context,
        navigate: (route: Any) -> Unit,
        printParams: PrintingData,
        toaster: ToasterState,
        printViewModel: PrintViewModel,
        bluetoothVM: BluetoothViewModel,
        sunmiPrintManager: SunmiPrintManager? = null,
        toPrintBT: () -> Unit,
        toPrintWifi: () -> Unit = {},
        toPrintSunmi: () -> Unit = {}
    ) {
        // Create a layout for the dialog
        val layout = android.widget.LinearLayout(context)
        layout.orientation = android.widget.LinearLayout.VERTICAL
        layout.setPadding(50, 40, 50, 10)

        // Create the EditText
        val editText = android.widget.EditText(context).apply {
            // Configure the EditText
            setSingleLine(false)
            minLines = 2
            maxLines = 3
            hint = "Entrez votre note ici"
            // No initial value - leave it empty for the client to fill in
        }

        // Add the EditText to the layout
        layout.addView(editText)

        val dialog = android.app.AlertDialog.Builder(context)
            .setTitle("Ajouter une note")
            .setMessage("Veuillez saisir une note qui sera imprimée après l'adresse du client:")
            .setView(layout)
            .setPositiveButton("Confirmer") { _, _ ->
                // Get the note from the EditText
                val noteText = editText.text.toString()
                Log.d("PrintFunctions", "Note entered: $noteText")

                // Set the note in the SunmiPrintManager if available
                if (sunmiPrintManager != null && printParams.useSunmiPrinter) {
                    sunmiPrintManager.setTicketNote(noteText)
                    Log.d("PrintFunctions", "Note set in SunmiPrintManager")
                }

                // Make sure enableNotes is set to true and noteText is set
                val updatedPrintParams = printParams.copy(enableNotes = true, noteText = noteText)
                Log.d("PrintFunctions", "Using updated printParams with enableNotes=${updatedPrintParams.enableNotes}, noteText='${updatedPrintParams.noteText}'")

                // Log the note for debugging
                Log.d("PrintFunctions", "Note will be printed: '$noteText'")

                // For Bluetooth printing, call the toPrintBT function directly
                if (!printParams.useSunmiPrinter && !printParams.printViaWifi) {
                    Log.d("PrintFunctions", "Bluetooth printing: calling toPrintBT directly with note: '$noteText'")
                    // Call the Bluetooth printing function directly
                    printViewModel.printTicketWithNote(
                        context = context,
                        note = noteText,
                        printParams = updatedPrintParams,
                        toPrintBT = toPrintBT
                    )
                } else {
                    // For Sunmi and WiFi printing, use the processPrint method
                    processPrint(context, navigate, updatedPrintParams, toaster, printViewModel, bluetoothVM, sunmiPrintManager, toPrintBT, toPrintWifi, toPrintSunmi, noteText)
                }
            }
            .setNegativeButton("Annuler") { _, _ ->
                // Process the print request without a note
                // Make sure enableNotes is set to false so no note is printed
                val updatedPrintParams = printParams.copy(enableNotes = false, noteText = "")
                Log.d("PrintFunctions", "Cancel clicked, disabling notes: enableNotes=${updatedPrintParams.enableNotes}")

                // Clear any previously set note in the SunmiPrintManager if available
                if (sunmiPrintManager != null && printParams.useSunmiPrinter) {
                    sunmiPrintManager.setTicketNote("")
                    Log.d("PrintFunctions", "Note cleared in SunmiPrintManager")
                }

                // For Bluetooth printing, call the toPrintBT function directly
                if (!printParams.useSunmiPrinter && !printParams.printViaWifi) {
                    Log.d("PrintFunctions", "Bluetooth printing: calling toPrintBT directly without note")
                    // Call the Bluetooth printing function directly
                    printViewModel.printTicketWithNote(
                        context = context,
                        note = "",
                        printParams = updatedPrintParams,
                        toPrintBT = toPrintBT
                    )
                } else {
                    // For Sunmi and WiFi printing, use the processPrint method
                    processPrint(context, navigate, updatedPrintParams, toaster, printViewModel, bluetoothVM, sunmiPrintManager, toPrintBT, toPrintWifi, toPrintSunmi, "")
                }
            }
            .create()

        dialog.show()
    }

    private fun processPrint(
        context: Context,
        navigate: (route: Any) -> Unit,
        printParams: PrintingData,
        toaster: ToasterState,
        printViewModel: PrintViewModel,
        bluetoothVM: BluetoothViewModel,
        sunmiPrintManager: SunmiPrintManager? = null,
        toPrintBT: () -> Unit,
        toPrintWifi: () -> Unit = {},
        toPrintSunmi: () -> Unit = {},
        note: String = ""
    ) {
        Log.d("PrintFunctions", "processPrint called with note: '$note'")

        // Log the printParams for debugging
        Log.d("PrintFunctions", "processPrint called with printParams: enableNotes=${printParams.enableNotes}, noteText='${printParams.noteText}'")

        // Check if Sunmi printer is selected and available
        if(printParams.useSunmiPrinter) {
            Log.d("PrintFunctions", "Using Sunmi API printer")
            if (sunmiPrintManager != null) {
                Log.d("PrintFunctions", "SunmiPrintManager is available: $sunmiPrintManager")
                // Initialize the printer first
                try {
                    // Create a CoroutineScope to handle async operations
                    CoroutineScope(Dispatchers.Main).launch {
                        try {
                            // Initialize the printer and wait for it to be ready
                            sunmiPrintManager.initPrinter(context) {
                                Log.d("PrintFunctions", "Sunmi printer initialized, now printing")
                                try {
                                    // Call the print function
                                    Log.d("PrintFunctions", "Calling toPrintSunmi()")
                                    toPrintSunmi()
                                    Log.d("PrintFunctions", "toPrintSunmi() completed")
                                } catch (e: Exception) {
                                    Log.e("PrintFunctions", "Error in Sunmi printing: ${e.message}")
                                    e.printStackTrace()
                                    Toast.makeText(context, "Error in Sunmi printing: ${e.message}", Toast.LENGTH_SHORT).show()
                                }
                            }
                            Log.d("PrintFunctions", "initPrinter called successfully")
                        } catch (e: Exception) {
                            Log.e("PrintFunctions", "Error in Sunmi printing coroutine: ${e.message}")
                            e.printStackTrace()
                            Toast.makeText(context, "Error in Sunmi printing: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    }
                } catch (e: Exception) {
                    Log.e("PrintFunctions", "Error initializing Sunmi printer: ${e.message}")
                    e.printStackTrace()
                    Toast.makeText(context, "Error initializing Sunmi printer: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            } else {
                Log.e("PrintFunctions", "SunmiPrintManager is null")
                Toast.makeText(context, "Sunmi printer manager not available", Toast.LENGTH_SHORT).show()
            }
            return
        }

        // Check if WiFi printing is selected
        if(printParams.printViaWifi) {
            Log.d("PrintFunctions", "Using WiFi printing with note: '${printParams.noteText}'")
            toPrintWifi()
            return
        }

        Log.d("PrintFunctions", "Using Bluetooth printing")

        // Default to regular Bluetooth printing
        BluetoothUtil.checkBT(bluetoothVM = bluetoothVM, context = context)
        if(!bluetoothVM.isBluetoothEnabled){
            Toast.makeText(context, context.resources.getString(R.string.activate_bluetooth), Toast.LENGTH_SHORT).show()
            return
        }

        // Only navigate to Bluetooth connect screen if no device is selected
        if (printViewModel.deviceAddress.isEmpty()) {
            Log.d("PrintFunctions", "No Bluetooth device selected, navigating to device selection screen")
            navigate(BluetoothConnectRoute)
            return
        }

        // Now we have a Bluetooth device selected, so we can print
        Log.d("PrintFunctions", "Using Bluetooth device: ${printViewModel.deviceAddress}")

        // For Bluetooth printing with notes, we need to show the dialog after the device is selected
        if (printParams.enableNotes) {
            Log.d("PrintFunctions", "Showing note dialog for Bluetooth printing")
            // Show the note dialog
            showNoteDialog(context, navigate, printParams, toaster, printViewModel, bluetoothVM, sunmiPrintManager, toPrintBT, toPrintWifi, toPrintSunmi)
            return
        }

        // Use regular Bluetooth printing without notes
        Log.d("PrintFunctions", "Using regular Bluetooth printing with selected device")
        printViewModel.awaitPrint(
            context = context,
            toPrint = {
                // Pass the printParams to the toPrintBT function
                Log.d("PrintFunctions", "Calling toPrintBT with printParams: enableNotes=${printParams.enableNotes}, noteText='${printParams.noteText}'")
                toPrintBT()
            }
        )
    }
}