package com.asmtunis.procaisseinventory.core.connectivity.location

import android.Manifest
import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import androidx.core.content.ContextCompat
import androidx.core.net.toUri


object LocationUtils {

     const val  MAX_DISTANCE_TO_CLIENTS_IN_METERS = 200
     fun openGoogleMapNav(context : Context, lat: Double, longitude: Double) {
        //   Uri gmmIntentUri = Uri.parse("geo:37.7749,-122.4194");
        /*   Uri gmmIntentUri = Uri.parse("geo:"+gPSTracker.getLatitude()+","+gPSTracker.getLongitude());
            Intent mapIntent = new Intent(Intent.ACTION_VIEW, gmmIntentUri);
            mapIntent.setPackage("com.google.android.apps.maps");
            if (mapIntent.resolveActivity(context.getPackageManager()) != null) {
                startActivity(mapIntent);
            }*/

        //   Uri gmmIntentUri = Uri.parse("google.navigation:q="+gPSTracker.getLatitude()+","+gPSTracker.getLongitude());
        val gmmIntentUri = "google.navigation:q=$lat,$longitude".toUri()
        val mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
        mapIntent.setPackage("com.google.android.apps.maps")
        if (mapIntent.resolveActivity(context.packageManager) != null) {
            context.startActivity(mapIntent)
        }
    }

    fun getDistance(lat1: Double, lon1: Double, myLat: Double, myLong: Double): Double {
        val loc1 = Location("")
        loc1.latitude = lat1
        loc1.longitude = lon1

        val loc2 = Location("")
        loc2.latitude = myLat
        loc2.longitude = myLong


       return loc1.distanceTo(loc2).toDouble()

    }


     fun hasLocationPermissions(application: Application): Boolean {
        return ContextCompat.checkSelfPermission(
            application,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(
                    application,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) == PackageManager.PERMISSION_GRANTED
    }

    // Improvement: Extract GPS check to a separate function for better readability
     fun isGpsEnabled(application: Application): Boolean {
        val locationManager = application.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER) ||
                locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
    }



    fun isValidGPScoordinates(lat: Double?, long: Double?): Boolean =
        lat != 0.0 && long != 0.0 && lat != null && long != null
}