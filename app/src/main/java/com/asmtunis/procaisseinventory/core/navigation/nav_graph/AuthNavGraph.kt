package com.asmtunis.procaisseinventory.core.navigation.nav_graph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.NoLicenseScreen
import com.asmtunis.procaisseinventory.auth.base_config.BaseConfigScreen
import com.asmtunis.procaisseinventory.auth.login.screens.LoginScreen
import com.asmtunis.procaisseinventory.auth.spalsh_screen.screens.SplashScreen
import com.asmtunis.procaisseinventory.auth.subscribtion.screens.SubscribtionScreen
import com.asmtunis.procaisseinventory.auth.subscribtion.screens.WaitingLicenceActivationScreen
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AuthGraph
import com.asmtunis.procaisseinventory.core.navigation.BaseConfigRoute
import com.asmtunis.procaisseinventory.core.navigation.LoginRoute
import com.asmtunis.procaisseinventory.core.navigation.NoLicenseRoute
import com.asmtunis.procaisseinventory.core.navigation.SplashRoute
import com.asmtunis.procaisseinventory.core.navigation.SubscribtionRoute
import com.asmtunis.procaisseinventory.core.navigation.WaitingLicenceActivationRoute
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel


fun NavGraphBuilder.authNavGraph(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    authViewModel: AuthViewModel,
    settingViewModel: SettingViewModel,
    mainViewModel: MainViewModel,
    networkErrorsVM: NetworkErrorsViewModel,
) {
    navigation<AuthGraph>(
        startDestination = SplashRoute,
    ) {
        composable<SplashRoute> {
            SplashScreen(
                navigate = { navigate(it)},
                navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                authViewModel = authViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingViewModel,
            )
        }

        composable<SubscribtionRoute> {
            SubscribtionScreen(
                navigate = { navigate(it)},
                navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                authViewModel = authViewModel,
                settingViewModel = settingViewModel
            )
        }

        composable<NoLicenseRoute> {
            NoLicenseScreen(
                navigate = { navigate(it)},
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                authViewModel = authViewModel,
            )
        }

        composable<WaitingLicenceActivationRoute> {
            WaitingLicenceActivationScreen(
                navigate = { navigate(it)},
                dataViewModel = dataViewModel,
                authViewModel = authViewModel,
                networkViewModel = networkViewModel,
            )
        }

        composable<LoginRoute> {
            LoginScreen(
                navigate = { navigate(it)},
                navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                dataViewModel = dataViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                authViewModel = authViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                settingViewModel = settingViewModel,
                networkErrorsVM = networkErrorsVM
            )
        }

        composable<BaseConfigRoute> {
            dataViewModel.resetDataStore()
            BaseConfigScreen(
                navigate = { navigate(it)},
                navigationDrawerViewModel = navigationDrawerViewModel,
                dataViewModel = dataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                networkViewModel = networkViewModel,
                authViewModel = authViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingViewModel
            )
        }
    }
}
