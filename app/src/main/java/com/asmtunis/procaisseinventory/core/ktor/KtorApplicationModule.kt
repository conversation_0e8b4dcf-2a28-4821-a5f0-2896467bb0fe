package com.asmtunis.procaisseinventory.core.ktor

import com.asmtunis.procaisseinventory.BuildConfig
import com.asmtunis.procaisseinventory.core.Globals.APP_NAME
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import io.ktor.client.engine.android.Android
import io.ktor.client.plugins.DefaultRequest
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.observer.ResponseObserver
import io.ktor.client.request.header
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.serialization.kotlinx.json.json
import io.ktor.util.appendIfNameAbsent
import kotlinx.serialization.json.Json
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object KtorApplicationModule {
    @Provides
    @Singleton
    fun provideHttpClient(): HttpClient = HttpClient(Android) {

        // set expectSuccess to true to access catch (e: Exception) in executePostApiCall and  executeDeleteApiCall functions in NetworkUtils
        expectSuccess = true


        install(DefaultRequest) {
            headers.appendIfNameAbsent("Language", "fr")

            header(HttpHeaders.ContentType, ContentType.Application.Json)

          // headers.appendIfNameAbsent("Application-name", AppUtils.getAppName())
           headers.appendIfNameAbsent("Application-name", APP_NAME)

            headers.appendIfNameAbsent("Version-code", BuildConfig.VERSION_CODE.toString())
            headers.appendIfNameAbsent("Version-name", BuildConfig.VERSION_NAME)
            headers.appendIfNameAbsent("User-Agent", "android-api-client")
          //  headers.appendIfNameAbsent("user", userId)


        }

        install(Logging) {


            logger = object : Logger {
                override fun log(message: String) {
                    veryLongLog(tag = "ktor_logger", message = message)
                }
            }
            level = LogLevel.ALL


        }

        install(ResponseObserver) {
            onResponse { response ->
                //Log.d("http_status:", "${response.status.value}")
                veryLongLog("http_status:", "${response.status.value}")
            }
        }

        install(ContentNegotiation) {
            json(
                Json {
                    prettyPrint = true
                    isLenient = true
                    ignoreUnknownKeys = true
                    explicitNulls = true
                    coerceInputValues = true
                    encodeDefaults = true
                    allowSpecialFloatingPointValues = true
                    allowStructuredMapKeys = true
                    useArrayPolymorphism = true
                    useAlternativeNames = true // Disabling this flag when one does not use JsonNames a
                },

                contentType = ContentType.Any // body response type
            )


//            engine {
//                connectTimeout = 60_000
//                socketTimeout = 60_000
//            }
        }


    }





}


