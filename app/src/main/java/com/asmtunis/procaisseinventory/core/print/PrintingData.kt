package com.asmtunis.procaisseinventory.core.print

import kotlinx.serialization.Serializable

@Serializable
data class PrintingData(
    val printIcon: Boolean = true,
    val printCompanyName: Boolean = true,
    val taxeArticle: Boolean = true,
    val printClientSold: <PERSON>olean = true,
    val printAppVersion: <PERSON>olean = true,
    val printCachet: <PERSON>olean = true,
    val printViaWifi: Boolean = false,
    val useSunmiPrinter: Boolean = false,
    val paperSize: Int = 80, // Default to 80mm paper
)
