package com.asmtunis.procaisseinventory.core.navigation

import kotlinx.serialization.Serializable


//sealed class Screens(val Route: String) {


// Route for nested graph
@Serializable
object AuthGraph


@Serializable
object SharedGraph


@Serializable
object InventoryGraph

@Serializable
object ProcaisseGraph


// Routes inside nested graph
@Serializable
object NetworkErrorRoute

@Serializable
object SplashRoute

@Serializable
object SubscribtionRoute

@Serializable
object NoLicenseRoute

@Serializable
object WaitingLicenceActivationRoute

@Serializable
object LoginRoute

@Serializable
object HomePageRoute

@Serializable
object BluetoothConnectRoute

@Serializable
object StatGraphRoute

@Serializable
data class PaymentListRoute(
    val clientId: String = "",
)

@Serializable
data class BonLivraisonRoute(
    val clientId: String = "",
)

@Serializable
object BonLivraisonDetailRoute

@Serializable
object RecapBonLivraisonRoute



@Serializable
data class AddBonLivraisonRoute(
    val clientId: String = "",
)


@Serializable
data class BonCommandeRoute(
    val clientId: String = "",
)

@Serializable
data class AddBonCommandeRoute(
    val clientId: String = "",
)

@Serializable
data class BonCommandeDetailRoute(
    val clientId: String = "",
)

@Serializable
data class BonRetourRoute(
    val clientId: String = "",
)

@Serializable
data class AddBonRetourRoute(
    val clientId: String = "",
)

@Serializable
object BonRetourDetailRoute

@Serializable
object ClientListRoute

@Serializable
data class ClientsInfoRoute(
    val clientId: String = "",
)

@Serializable
data class AddClientRoute(
    val clientId: String = "",
)

@Serializable
object ProductListRoute

@Serializable
object TourneRoute

@Serializable
object ArchiveRoute

@Serializable
data class DigitalDistributionRoute(
    val clientId: String = "",
)

@Serializable
data class AddModifyDigitalDistributionRoute(
    val clientId: String = "",
)

@Serializable
object FamilleProduitDetailRoute

@Serializable
object DepenceRoute

@Serializable
data class ReglementRoute(
    val clientId: String = "",
)

@Serializable
data class AddReglementRoute(
    val clientId: String = "",
)

@Serializable
object VeilleConcurentielleRoute

@Serializable
object NewProductDetailRoute

@Serializable
object AutreDetailRoute

@Serializable
object PromotionDetailRoute

@Serializable
object PrixDetailRoute

@Serializable
object BaseConfigRoute

@Serializable
object SettingRoute

@Serializable
object ProCaisseSyncRoute

@Serializable
object ProCaisseUpdateRoute

@Serializable
object SelectArticlesScreensCalculRoute

@Serializable
object MainImageTiketRoute

@Serializable
object BarCodeCameraRoute

@Serializable
data class InventairePatrimoineRoute(
    val clientId: String = "",
)

@Serializable
data class AjoutAffectationPatrimoineRoute(
    val clientId: String = "",
)

@Serializable
data class AjoutDepInPatrimoineRoute(
    val clientId: String = "",
)

@Serializable
data class AjoutDepOutPatrimoineRoute(
    val clientId: String = "",
)

@Serializable
data class AjoutInventairePatrimoineRoute(
    val clientId: String = "",
)

@Serializable
object InventairePatrimoineDetailRoute

@Serializable
object SelectPatrimoineRoute

@Serializable
object InventaireBatimentRoute

@Serializable
data class AjoutAffectationBatimentRoute(
    val numSerie: String = "",
    val codeInventaire: String = ""
)

@Serializable
object AjoutDepInBatimentRoute

@Serializable
object AjoutDepOutBatimentRoute

@Serializable
data class AjoutInventaireBatimentRoute(
    val autoOpenDialog: Boolean = false,
    val preFilledNumSerie: String = ""
)

@Serializable
object InventaireBatimentDetailRoute

@Serializable
object SelectBatimentScreens

@Serializable
object ZoneConsomationRoute

@Serializable
object DeplacementOutByUserRoute

@Serializable
data class DeplacementOutByUserWaitingRoute(
    val title: String
)

@Serializable
object DeplacementOutByUserDetailRoute

@Serializable
object ChooseImmoRoute

@Serializable
object ChooseSiteFinancierRoute

@Serializable
object ZoneConsomationDetailRoute

// //   I N V E N T O R Y //////////
@Serializable
object InventoryHomeRoute

@Serializable
object InventoryAchatRoute

@Serializable
object InventoryAchatDetailRoute

@Serializable
object InventoryAddAchatRoute

@Serializable
object InventoryInventaireRoute

@Serializable
object InventoryInventaireDetailRoute

@Serializable
object InventoryInventaireAddRoute

@Serializable
object InventoryBonTransfertRoute

@Serializable
object InventoryBonTransfertDetailRoute

@Serializable
object InventoryBonTransfertAddRoute

@Serializable
object InventoryTicketRayonRoute

@Serializable
object InventoryConsultationArticleScreen

@Serializable
object ArticlesDetailRoute

@Serializable
object AddArticlesRoute

@Serializable
object UpdateLocalDbInventoryRoute

@Serializable
object ProInventorySyncRoute

@Serializable
data class SelectArticlesNoCalculRoute(
    val stationOrigineCode: String = "",
)




/**
 *   S H A R E D
 */
//}
