package com.asmtunis.procaisseinventory.core.connectivity.bluetooth

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothClass
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.result.ActivityResult
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.AudioFile
import androidx.compose.material.icons.twotone.DeviceUnknown
import androidx.compose.material.icons.twotone.HealthAndSafety
import androidx.compose.material.icons.twotone.Laptop
import androidx.compose.material.icons.twotone.PermDeviceInformation
import androidx.compose.material.icons.twotone.PhoneAndroid
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material.icons.twotone.Toys
import androidx.compose.material.icons.twotone.WebAsset
import androidx.compose.ui.graphics.vector.ImageVector
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel

object BluetoothUtil {

    fun enableBluetooth(
        enableBluetoothLauncher:
        ManagedActivityResultLauncher<Intent, ActivityResult>
    ) {
        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        enableBluetoothLauncher.launch(enableBtIntent)
    }
    fun checkBluetoothAvailability(bluetoothVM: BluetoothViewModel, context: Context): Boolean {
        val packageManager = context.packageManager
        val bluetoothAdapter: BluetoothAdapter? = bluetoothVM.bluetoothManager?.adapter

        if (packageManager?.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH) != true) {
            return false
        }

        if (bluetoothAdapter == null) {
            return false
        }

        return true
    }

    fun checkBT(bluetoothVM: BluetoothViewModel, context: Context){
        bluetoothVM.onIsBluetoothEnabledChange(bluetoothVM.bluetoothAdapter?.isEnabled == true)
        bluetoothVM.onBluetoothManagerChange(context.getSystemService(BluetoothManager::class.java))
        bluetoothVM.onBluetoothAdapterChange(bluetoothVM.bluetoothManager?.adapter)

    }


    fun getBluetoothDeviceIcon(deviceCl: Int): ImageVector {
        return when (deviceCl) {
            BluetoothClass.Device.Major.COMPUTER -> Icons.TwoTone.Laptop
            BluetoothClass.Device.Major.PHONE -> Icons.TwoTone.PhoneAndroid
            BluetoothClass.Device.Major.AUDIO_VIDEO -> Icons.TwoTone.AudioFile
            BluetoothClass.Device.Major.IMAGING -> Icons.TwoTone.Print
            BluetoothClass.Device.Major.PERIPHERAL -> Icons.TwoTone.PermDeviceInformation  // Consider adding specific icons for peripherals
            BluetoothClass.Device.Major.WEARABLE -> Icons.TwoTone.WebAsset
            BluetoothClass.Device.Major.TOY -> Icons.TwoTone.Toys
            BluetoothClass.Device.Major.HEALTH -> Icons.TwoTone.HealthAndSafety
            else -> Icons.TwoTone.DeviceUnknown
        }
    }
}