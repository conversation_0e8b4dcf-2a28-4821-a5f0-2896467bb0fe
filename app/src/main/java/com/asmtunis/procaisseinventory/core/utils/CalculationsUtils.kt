package com.asmtunis.procaisseinventory.core.utils

import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva


object CalculationsUtils {
    fun timbersValueSum(listActifTimber: List<Timbre>): Double =
        listActifTimber.sumOf { if(it.tIMBEtat == "Actif") stringToDouble(it.tIMBValue) else 0.0 }
    fun totalPriceArticle(
        price: String,
        quantity: String
    ): String = (stringToDouble(price) * (if(quantity.isNotEmpty()) stringToDouble(quantity) else 1.0)).toString()

    fun totalPriceWithoutDiscountTTC(
        listArt : List<SelectedArticle>,
        withTva: Boolean = false
    ) : Double{
        var result = 0.0
        for (article in listArt) {
            result+= if(withTva){
                if(article.tva != Tva())
                stringToDouble(article.lTMtTTC)

                else
                    stringToDouble(
                        totalPriceArticle(
                        price = article.lTPuHT,
                        quantity = article.quantity
                    )
                    )
            }
            else
                stringToDouble(
                    totalPriceArticle(
                price = /* if(withTva) article.lTMtHT else  */article.prixVente,
                quantity = article.quantity
            )
                )
        }

        return result
    }

    fun totalPriceCaissseTTC(
        listArt : List<SelectedArticle>,
        withTva: Boolean = false
    ) : Double{
        var result = 0.0
        for (article in listArt) {

            result +=

                if(withTva){
                    if(article.tva != Tva())
                        stringToDouble(article.lTMtTTC)

                    else
                        stringToDouble(
                            totalPriceArticle(
                            price = article.lTPuHT,
                            quantity = article.quantity
                        )
                        )
                }


                else stringToDouble(
                    totalPriceArticle(
                price = article.prixCaisse,
                quantity = article.quantity
            )
                )

        }

        return result
    }

    fun totalPriceHT(
        listArt : List<SelectedArticle>
    ) : Double {
        var result = 0.0
        for (article in listArt) {
            //  result+= calculateAmountHT(amount = StringFormatting.stringToDouble(article.article.aRTPrixUnitaireHT), tva = article.article.aRTTVA)
            result+= stringToDouble(article.lTMtBrutHT)  //* stringToDouble(article.quantity)
        }

        return result
    }

    fun totalPriceTTC(listArt : List<SelectedArticle>) : Double =
        listArt.sumOf { stringToDouble(it.lTMtTTC) }



fun calculateAfterDiscount(amount: Double, discount: Double): Double {
return  amount - (amount* (discount/100))
}

    fun calculateDiscountAmount(amount: Double, discount: Double): Double {
        return  amount * (discount/100)
    }




    fun calculateAmountTTC(price: Double, quantity: Double): Double {
        return price * quantity
    }

    fun calculateAmountTTCNet(amount: Double, discount: Double): Double {
        return amount - (discount * amount) / 100.0
    }

    fun calculateAmountTTCNet(price: Double, quantity: Double, discount: Double): Double {
        return price * quantity
    }

    fun calculateAmountHT(amount: Double, tva: Double): Double {
        return amount / (1 + (tva / 100))
    }

    fun calculateAmountHT(price: Double, quantity: Double, tva: Double): Double {
        return price * quantity / (1 + tva / 100)
    }


    fun calculateAmountExcludingTax(price: Double, vat: Double): Double {
        return price / (1 + vat / 100)
    }





    fun calculateAmountExcludingTax(price: Double, quantity: Double, vat: Double): Double {
        return price / (1 + vat / 100) * quantity
    }

    fun calculateAmountHTNet(amount: Double, discount: Double, tva: Double): Double {
        return (amount - discount * amount / 100.0) / (1 + tva / 100)
    }

    fun calculateAmountHTNet(
        price: Double,
        quantity: Double,
        discount: Double,
        tva: Double
    ): Double {
        return (price * quantity - discount * (price * quantity) / 100.0) / (1 + tva / 100)
    }

    fun calculateDiscountRate(price: Double, unitPrice: Double): Double = (1 - price / unitPrice) * 100.0

   fun Double.returnZeroIfNegative(): Double = if(this <0) 0.0 else this

    fun Double.returnOneIfZero(): Double = if(this ==0.0) 1.0 else this



    fun calculateReceived(received: Double, chequeCaisses: Double, traiteCaisses: Double): Double {
        return received + chequeCaisses + traiteCaisses
    }

    fun calculateCheckAndCartResto(chequeCaisses: Double, traiteCaisses: Double): Double {
        return chequeCaisses + traiteCaisses
    }



    fun calculateTickets(traiteCaisses: ArrayList<TraiteCaisse>?): Double {
        var traiteCaisseAmount = 0.0
        if (traiteCaisses != null) {
            for (traiteCaisse in traiteCaisses) {
                traiteCaisseAmount += traiteCaisse.tRAITMontant
            }
        }
        return traiteCaisseAmount
    }


   /* fun calculateTicketAmount(ligneTickets: List<LigneTicket>, function: Int): Double {
        var result = 0.0
        when (function) {
            R.id.calculateAmountTTC -> for (ligneTicket in ligneTickets) {
                result += calculateAmountTTC(
                    ligneTicket.getArticle().getPvttc(),
                    ligneTicket.getlTQte()
                )
                //  result += calculateAmountTTC(ligneTicket.getlTMtTTC()/ligneTicket.getlTQte(), ligneTicket.getlTQte());
            }

            R.id.calculateAmountTTCNet -> for (ligneTicket in ligneTickets) {
                result += ligneTicket.getlTMtTTC()
            }

            R.id.calculateAmountHT -> for (ligneTicket in ligneTickets) {
                // result += calculateAmountHT(ligneTicket.getlTMtTTC(), ligneTicket.getlTTVA());
                result += ligneTicket.getlTMtHT()
            }

            R.id.calculateAmountHTNet -> for (ligneTicket in ligneTickets) {
                result += calculateAmountHTNet(
                    ligneTicket.getlTMtTTC(),
                    ligneTicket.getlTTauxRemise(),
                    ligneTicket.getlTTVA()
                )
            }

            R.id.calculateAmountVAT -> for (ligneTicket in ligneTickets) {
                result += ligneTicket.getlTMtTTC() - ligneTicket.getlTMtHT()
            }
        }
        return Utils.round(decimalFormat(result), 3)
    }*/
}