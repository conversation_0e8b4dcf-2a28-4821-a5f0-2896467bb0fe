package com.asmtunis.procaisseinventory.core.utils

import android.net.InetAddresses
import android.os.Build
import android.util.Patterns
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.ktor.Urls.BASE_URL
import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import java.text.DecimalFormatSymbols
import java.text.NumberFormat
import java.util.Locale

object StringUtils {

    fun getClientName(cltName : String?, cltCode:String?):String = if(!cltName.isNullOrEmpty())cltName else cltCode?:"N/A"


    //  fun nbrTitleTabs(allSize: Int, filteredSize: Int): String = if(allSize == filteredSize || allSize< filteredSize) allSize.toString() else "$filteredSize/$allSize"
    fun nbrTitleTabs(allSize: Int, filteredSize: Int): String =   " ($filteredSize)"
    fun stringPlural(
        nbr: Int,
        single: String,
        plural: String,
    ): String =
        if (nbr < 2) {
            "$nbr $single"
        } else {
            "$nbr $plural"
        }

    fun validateBaseUrl(baseConfig: BaseConfig): String {
        BASE_URL = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (InetAddresses.isNumericAddress(baseConfig.adresse_ip)) {
                "http://%s:%s"
            } else {
                "https://%s:%s"
            }
        } else {
            if (Patterns.IP_ADDRESS.matcher(baseConfig.adresse_ip).matches()) {
                "http://%s:%s"
            } else {
                "https://%s:%s"
            }
        }

        return BASE_URL
    }





    fun returnNAifEmpty(string: String?): String = if (!string.isNullOrBlank()) string else "N/A"

    fun formatZeroDouble(double: Double): String = if (double <= 0) "" else double.toString()//if(double > 0.0) double.toString() else ""
    fun checkIfNull(string: String): String = string.ifBlank { "" }

    fun isNumber(s: String?): Boolean = if (s.isNullOrEmpty()) false else s.all { Character.isDigit(it) }



     


    fun convertStringToPriceFormat(input: String?): String {

        if(Globals.devise == Devise()) {
            val format = NumberFormat.getCurrencyInstance(Locale.getDefault())
            format.maximumFractionDigits = 3

            return if (input.isNullOrEmpty()) {
                format.format(0)
            } else {
                try {
                    val value = stringToDouble(input)
                    format.format(value)
                } catch (e: NumberFormatException) {
                    format.format(0)
                }
            }
        }
        else
            return  convertStringToDoubleFormat(input = input) + " "+ Globals.devise.symbole
    }

    fun convertDoubleToDoubleFormat(value: Double): String {
        val format = NumberFormat.getNumberInstance(Locale.getDefault())
        format.minimumFractionDigits = 3
        format.maximumFractionDigits = 3
       return format.format(value)

     }




    fun convertStringToDoubleFormat(input: String?): String {
        val format = NumberFormat.getNumberInstance(Locale.getDefault())
        format.minimumFractionDigits = 3
        format.maximumFractionDigits = 3
        return format.format(stringToDouble(input))
//        val formattedNumber = input?.ifEmpty { "" }?.let { format.parse(it) } ?: ""
     //  return  formattedNumber.toString()
    }


    fun usingKotlinStringFormat(
        input: Double,
        scale: Int,
    ) = "%.${scale}f".format(input)


//    fun removeTrailingZeroInDouble(number: String): String {
//        val symbols = DecimalFormatSymbols.getInstance(Locale.getDefault())
//        val decimalSeparator = symbols.decimalSeparator
//        return number.replace(Regex("""[$decimalSeparator](\d*?)0*$""")) { if (it.groupValues[1].isEmpty()) "" else "${decimalSeparator}${it.groupValues[1]}" }.replace(Regex("""[$decimalSeparator]$"""), "")
//    }

    fun removeTrailingZeroInDouble(number: String): String {
        val symbols = DecimalFormatSymbols.getInstance(Locale.getDefault())
        val decimalSeparator = symbols.decimalSeparator
        val result = number.replace(Regex("""[$decimalSeparator](\d*?)0*$""")) {
            if (it.groupValues[1].isEmpty()) "" else "${decimalSeparator}${it.groupValues[1]}"
        }.replace(Regex("""[$decimalSeparator]$"""), "")

        return if (result.startsWith("-$decimalSeparator")) "-0${result.substring(1)}"
        else if (result.startsWith(decimalSeparator)) "0$result"
        else result
    }

    fun keepNumbers(str: String): String {
        return str.filter { it.isDigit() }
    }
    fun stringToDouble(text: String?): Double = text?.toDoubleOrNull() ?: 0.0

   /* fun stringToDouble(text: String?): Double {

        if (text == "null" || text.isNullOrEmpty()) {
            return 0.0 // Handle empty or null string
        }

        return try {
          //  text.replace(" ","").replace(",",".").toDouble()

           text.replace(" ","").replace(",","").toDouble()


        } catch (e: NumberFormatException) {
            Log.d("dgssffsfc", e.message.toString())
            0.0 // Handle invalid number format
        }
    }*/


    fun metersToKilometers(meters: Double): String =
        if (meters > 500) {
            "${convertDoubleToDoubleFormat(meters / 1000)} km"
        } else {
            "${convertDoubleToDoubleFormat(meters)} m"
        }
}
