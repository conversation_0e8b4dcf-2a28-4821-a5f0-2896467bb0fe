package com.asmtunis.procaisseinventory.core.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.widget.Toast

object IntentUtils {

    fun openPhoneDialer(
        context: Context,
        phoneNbr:String
    ) {
        val u = Uri.parse("tel:$phoneNbr")
        val i = Intent(Intent.ACTION_DIAL, u)
        try {

            // Launch the Phone app's dialer with a phone
            // number to dial a call.
            context.startActivity(i)
        } catch (s: SecurityException) {

            // show() method display the toast with
            // exception message.
            Toast.makeText(context, "An error occurred: "+ s.message, Toast.LENGTH_LONG).show()
        }
    }


    fun openEmailApp(
        context: Context,
        email: String,
        body: String = "",
        subject: String = "",
        onComplete: (String?) -> Unit
    ) {
        try {
            val emailIntent = Intent(Intent.ACTION_SENDTO).apply {
                data = Uri.parse("mailto:$email?subject=$subject&body=$body")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            if (emailIntent.resolveActivity(context.packageManager) != null) {
                context.startActivity(emailIntent)
                onComplete(null)
            } else {
                onComplete("No Email App installed in this device!")
            }
        } catch (e: Exception) {
            onComplete(e.message.toString())
        }
    }
}