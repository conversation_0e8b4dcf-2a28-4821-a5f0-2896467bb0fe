package com.asmtunis.procaisseinventory.core.utils

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.model.NestedItem
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.AssetOperation
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement

/**
 * Helper class for batching network requests.
 * This reduces network overhead by grouping similar operations.
 */
object BatchRequestHelper {
    
    /**
     * Default batch size for network requests
     */
    private const val DEFAULT_BATCH_SIZE = 10
    
    /**
     * Creates batched requests for asset operations.
     *
     * @param items List of items to batch
     * @param baseConfig Base configuration for the request
     * @param batchSize Maximum number of items per batch
     * @return List of batched request objects
     */
    fun <T : AssetOperation> createBatchedRequests(
        items: List<T>,
        baseConfig: BaseConfig,
        batchSize: Int = DEFAULT_BATCH_SIZE
    ): List<GenericObject> {
        return items.chunked(batchSize).map { batch ->
            GenericObject(
                connexion = baseConfig,
                data = Json.encodeToJsonElement(batch)
            )
        }
    }
    
    /**
     * Creates batched requests for nested items.
     *
     * @param items List of nested items to batch
     * @param baseConfig Base configuration for the request
     * @param batchSize Maximum number of items per batch
     * @return List of batched request objects
     */
    fun createBatchedNestedRequests(
        items: List<NestedItem<BonCommande, List<LigneBonCommande>>>,
        baseConfig: BaseConfig,
        batchSize: Int = DEFAULT_BATCH_SIZE
    ): List<GenericObject> {
        return items.chunked(batchSize).map { batch ->
            GenericObject(
                connexion = baseConfig,
                data = Json.encodeToJsonElement(batch)
            )
        }
    }
    
    /**
     * Groups similar operations to reduce the number of network requests.
     *
     * @param operations List of operations to group
     * @return Map of operation type to list of operations
     */
    fun <T : AssetOperation> groupSimilarOperations(
        operations: List<T>
    ): Map<String, List<T>> {
        return operations.groupBy { "${it.typePat}-${it.devEtat}" }
    }
}
