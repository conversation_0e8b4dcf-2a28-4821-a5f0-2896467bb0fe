package com.asmtunis.procaisseinventory.auth.subscribtion.data.remote.di

import com.asmtunis.procaisseinventory.auth.subscribtion.data.remote.api.SubscribtionApi
import com.asmtunis.procaisseinventory.auth.subscribtion.data.remote.api.SubscribtionApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object SubscribtionRemoteModule {
    @Provides
    @Singleton
    fun provideSubscribtionApi(client: HttpClient): SubscribtionApi = SubscribtionApiImpl(client)


}