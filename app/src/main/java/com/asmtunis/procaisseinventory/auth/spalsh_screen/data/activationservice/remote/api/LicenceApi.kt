package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.remote.api

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.core.model.DataResult
import kotlinx.coroutines.flow.Flow

interface LicenceApi {


    suspend fun getLicenceInventory(deviceId: String, product: String): Flow<DataResult<Licence>>
}
