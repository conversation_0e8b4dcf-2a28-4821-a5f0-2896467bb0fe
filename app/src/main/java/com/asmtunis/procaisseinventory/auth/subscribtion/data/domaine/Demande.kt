package com.asmtunis.procaisseinventory.auth.subscribtion.data.domaine

import kotlinx.serialization.Serializable

@Serializable
data class Demande(
    val idDevice: String? = null,
    var nomPrenom: String? = null,
    var etablissement: String? = null,
    var numTel: String? = null,
    var email: String? = null,
    var duree: Long = 0,
    var produit: String? = null,
    var pays: String = "Tunisie",
  //  @SerialName("device")
    var device: String //= "promobile"
) /*{
    fun toDemande(): Demande {
        return Demande(
            idDevice,
            nomPrenom,
            etablissement,
            numTel,
            email,
            duree,
            produit,
            pays,
            device
        )
    }
}*/