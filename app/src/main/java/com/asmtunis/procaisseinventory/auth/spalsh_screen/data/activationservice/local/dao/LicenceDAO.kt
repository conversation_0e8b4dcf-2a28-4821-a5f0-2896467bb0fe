package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.coroutines.flow.Flow

 

@Dao
interface LicenceDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun upsert(value: Licence) // : Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun upsertAll(listValue: List<Licence>) // : Long

    // @Query("UPDATE $Licence_TABLE SET selected = :selected WHERE id_base_config = :idbaseconfig")
    // fun updateISSelected(selected: <PERSON>olean,idbaseconfig:String)

    @Query("SELECT * FROM ${ProCaisseConstants.LICENCE_TABLE}")
    fun getAll(): Flow<List<Licence>>

    @Query("SELECT * FROM ${ProCaisseConstants.LICENCE_TABLE}  WHERE produit Like :product")
    fun getByProduct(product: String): Flow<Licence>


    @Query("DELETE FROM ${ProCaisseConstants.LICENCE_TABLE} WHERE produit Like :product ")
    fun deleteByProduct(product : String)

    @Query("DELETE FROM ${ProCaisseConstants.LICENCE_TABLE}")
    fun deleteAll()

}