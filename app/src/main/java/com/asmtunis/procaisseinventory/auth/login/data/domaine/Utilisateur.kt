package com.asmtunis.procaisseinventory.auth.login.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.UTILISATEUR_TABLE)
@Serializable
data class Utilisateur(
    @ColumnInfo(name = "Code_Ut")
    @SerialName("Code_Ut")
    @PrimaryKey(autoGenerate = false)
    val codeUt: String = "",

    @ColumnInfo(name = "AutBonA")
    @SerialName("AutBonA")
    val autBonA: String? = "",

    @SerialName("AutoriseCrCt")
    @ColumnInfo(name = "AutoriseCrCt")
    val autoriseCrCt: String? = "",

    @SerialName("AutoriseTourne")
    @ColumnInfo(name = "AutoriseTourne")
    val autoriseTourne: String? = "",

    @SerialName("Autorise_Bc")
    @ColumnInfo(name = "Autorise_Bc")
    val autoriseBc: String? = "",

    @SerialName("Autorise_Clt")
    @ColumnInfo(name = "Autorise_Clt")
    val autoriseClt: String? = "",

    @SerialName("Autorise_R")
    @ColumnInfo(name = "Autorise_R")
    val autoriseR: String? = "",

    @SerialName("Autorise_Rt")
    @ColumnInfo(name = "Autorise_Rt")
    val autoriseRt: String? = "",

    @SerialName("CHEF_DEP")
    @ColumnInfo(name = "CHEF_DEP")
    val chefDep: String? = "",

    @SerialName("CHEF_EQUIPE")
    @ColumnInfo(name = "CHEF_EQUIPE")
    val chefEquipe: String? = "",

    @SerialName("CHEF_ZONE")
    @ColumnInfo(name = "CHEF_ZONE")
    val chefZone: String? = "",

    @SerialName("ClotSessAuto")
    @ColumnInfo(name = "ClotSessAuto")
    val ClotSessAuto: String? = "",

    @SerialName("CltEquivalent")
    @ColumnInfo(name = "CltEquivalent")
    val CltEquivalent: String? = "",

    @SerialName("CodeCle")
    @ColumnInfo(name = "CodeCle")
    val CodeCle: String? = "",

    @SerialName("CodeCle1")
    @ColumnInfo(name = "CodeCle1")
    val CodeCle1: String? = "",

    @SerialName("Code_Caisse")
    @ColumnInfo(name = "Code_Caisse")
    val Code_Caisse: String? = "",

    @SerialName("Code_Carnet")
    @ColumnInfo(name = "Code_Carnet")
    val Code_Carnet: String? = "",

    @SerialName("CrtourneAuto")
    @ColumnInfo(name = "CrtourneAuto")
    val CrtourneAuto: String? = "",

    @SerialName("DDm")
    @ColumnInfo(name = "DDm")
    val DDm: String? = "",

    @SerialName("DDmM")
    @ColumnInfo(name = "DDmM")
    val DDmM: String? = "",

    @SerialName("Etat")
    @ColumnInfo(name = "Etat")
    val Etat: String? = "",

    @SerialName("FiltreClt")
    @ColumnInfo(name = "FiltreClt")
    val FiltreClt: String? = "",

    @SerialName("Groupe")
    @ColumnInfo(name = "Groupe")
    val Groupe: String? = "",

    @SerialName("HeurFintourne")
    @ColumnInfo(name = "HeurFintourne")
    val HeurFintourne: String? = "",

    @SerialName("Login")
    @ColumnInfo(name = "Login")
    val Login: String? = "",

    @SerialName("Mail")
    @ColumnInfo(name = "Mail")
    val Mail: String? = "",

    @SerialName("MajPvBL")
    @ColumnInfo(name = "MajPvBL")
    val MajPvBL: String? = "",

    @SerialName("MajRegTik")
    @ColumnInfo(name = "MajRegTik")
    val MajRegTik: String? = "",

    @SerialName("MajTik")
    @ColumnInfo(name = "MajTik")
    val MajTik: String? = "",

    @SerialName("Nom")
    @ColumnInfo(name = "Nom")
    val Nom: String = "",

    @SerialName("Passe")
    @ColumnInfo(name = "Passe")
    val Passe: String? = "",

    @SerialName("Prenom")
    @ColumnInfo(name = "Prenom")
    val Prenom: String? = "",

    @SerialName("Station")
    @ColumnInfo(name = "Station")
    val Station: String = "",

    @SerialName("Tel")
    @ColumnInfo(name = "Tel")
    val Tel: String? = "",

    @SerialName("Type_user")
    @ColumnInfo(name = "Type_user")
    val typeUser: String = "",

    @SerialName("UPgros")
    @ColumnInfo(name = "UPgros")
    val UPgros: String? = "",

    @SerialName("UPointT")
    @ColumnInfo(name = "UPointT")
    val UPointT: String? = "",

     //@Transient
    @SerialName("autorisationUser")
  //  @Ignore
    @ColumnInfo(name = "autorisationUser")
  //  val autorisationUser: List<List<Authorization>> = listOf(listOf()),
    val autorisationUser: List<Authorization> = emptyList(),

    @SerialName("export")
    @ColumnInfo(name = "export")
    val export: String? = "",

    @SerialName("exportM")
    @ColumnInfo(name = "exportM")
    val exportM: String? = "",

    @SerialName("zones")
    @ColumnInfo(name = "zones")
   val zones: List<String> = ArrayList(),

    //@ColumnInfo(name = "product")
   // var product: String = "",

)



