package com.asmtunis.procaisseinventory.auth.login.text_validation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePassword
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import kotlinx.coroutines.launch


class LoginTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
    private val validatePassword: ValidatePassword = ValidatePassword(),
) : ViewModel() {



    /**
     * Login input edit text validation
     */

    var stateLogin by mutableStateOf(LogInFormState())

    var validationLoginEvent by mutableStateOf(ValidationLoginEvent())

    fun resetValidationLoginEvent() {
        validationLoginEvent = ValidationLoginEvent()
    }
  //  private val validationLoginEventChannel = Channel<ValidationLoginEvent>()
 //   val validationLoginEvents = validationLoginEventChannel.receiveAsFlow()

    fun onLoginEvent(event: LogInFormEvent) {
        when (event) {
            is LogInFormEvent.IdentifiantChanged -> {
                stateLogin = stateLogin.copy(
                    identifiant = event.identifiant,
                    identifiantError = validateIsNotEmptyString.execute(event.identifiant).errorMessage
                )

            }

            is LogInFormEvent.PasswordChanged -> {
                stateLogin = stateLogin.copy(
                    password = event.password,
                    passwordError = validatePassword.execute(event.password).errorMessage
                )

            }

            LogInFormEvent.SubmitLogIn -> {
                submitLoginData()
            }
        }
    }

    private fun submitLoginData() {
        val identifiantResult = validateIsNotEmptyString.execute(stateLogin.identifiant)

        val passwordResult = validatePassword.execute(stateLogin.password)

        val hasError = listOf(
            identifiantResult,
            passwordResult
        ).any { !it.successful }

        if (hasError) {
            stateLogin = stateLogin.copy(
                passwordError = passwordResult.errorMessage,
                identifiantError = identifiantResult.errorMessage

            )
            return
        }
        viewModelScope.launch {
            validationLoginEvent = ValidationLoginEvent.LoginSubscribtion(stateLogin)
        }
    }






    fun restVariables(){
        stateLogin = stateLogin.copy(

             identifiant = "",
         identifiantError = null,
         password = "",
         passwordError = null
        )
    }
fun restErrorVariables(){
    stateLogin = stateLogin.copy(

     identifiantError = null,
     passwordError = null
    )
}
}