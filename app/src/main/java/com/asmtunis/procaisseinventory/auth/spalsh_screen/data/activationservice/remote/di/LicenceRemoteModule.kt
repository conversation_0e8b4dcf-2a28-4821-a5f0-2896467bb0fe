package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.remote.di

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.remote.api.LicenceApi
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.remote.api.LicenceApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object LicenceRemoteModule {
    @Provides
    @Singleton
    fun provideLicenceApi(client: HttpClient): LicenceApi = LicenceApiImpl(client)


}