package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.repository

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import kotlinx.coroutines.flow.Flow



interface LicenceLocalRepository {

    fun upsert(value: Licence)

    fun upsertAll(value: List<Licence>)
    fun deletebyProduct(value: String)
    fun deleteAll()

    fun getAll(): Flow<List<Licence>>

    fun getByProduct(product: String): Flow<Licence?>
}