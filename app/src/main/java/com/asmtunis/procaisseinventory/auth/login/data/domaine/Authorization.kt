package com.asmtunis.procaisseinventory.auth.login.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.AUTHORIZATION_TABLE)
@Serializable
data class Authorization(
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "AutoCode")
    val AutoCode: String = "",

    @ColumnInfo(name = "AutEtat")
    val AutEtat: String? = "",
    @ColumnInfo(name = "AutValues")
    val AutValues: String? = "",

    @ColumnInfo(name = "AutoCodeAu")
    val AutoCodeAu: String = "",
    @ColumnInfo(name = "AutoCodeUt")
    val AutoCodeUt: String = "",
    @ColumnInfo(name = "AutoDescription")
    val AutoDescription: String? = "",
    @ColumnInfo(name = "AutoNom")
    val AutoNom: String? = "",
    @ColumnInfo(name = "AutoTypeMenu")
    val AutoTypeMenu: String? = ""
)