package com.asmtunis.procaisseinventory.auth.base_config.data.local.di


import com.asmtunis.procaisseinventory.auth.base_config.data.local.dao.BaseConfigDAO
import com.asmtunis.procaisseinventory.auth.base_config.data.local.repository.BaseConfigLocalRepository
import com.asmtunis.procaisseinventory.auth.base_config.data.local.repository.BaseConfigRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class BaseConfigLocalModule {

    @Provides
    @Singleton
    fun provideBaseConfigDao(
        baseConfigProCaisseDataBase: ProCaisseDataBase
    ) = baseConfigProCaisseDataBase.baseConfigDao()

    @Provides
    @Singleton
    @Named("BaseConfig")
    fun provideBaseConfigRepository(
        baseConfigDAO: BaseConfigDAO
    ): BaseConfigLocalRepository = BaseConfigRepositoryImpl(
        baseConfigDAO = baseConfigDAO

    )

}