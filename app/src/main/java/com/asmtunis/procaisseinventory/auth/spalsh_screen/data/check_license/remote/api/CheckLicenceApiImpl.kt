package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.remote.api

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.domaine.BaseConfigLicenseCheck
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class CheckLicenceApiImpl(private val client: HttpClient) : CheckLicenceApi {


    override suspend fun checkLicenses(idDevice: String): Flow<DataResult<BaseConfigLicenseCheck>> = flow {

        emitAll(
            executePostApiCall<BaseConfigLicenseCheck>(
                client = client,
                baseUrl = Urls.CHECK_LICENCE_BASE_URL,
                endpoint = idDevice,
            )
        )
    }
}