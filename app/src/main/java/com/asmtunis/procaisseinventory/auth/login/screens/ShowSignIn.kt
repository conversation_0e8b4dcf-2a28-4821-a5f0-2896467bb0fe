@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.auth.login.screens

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MultiChoiceSegmentedButtonRow
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.text_validation.LogInFormEvent
import com.asmtunis.procaisseinventory.auth.login.text_validation.LogInFormState
import com.asmtunis.procaisseinventory.auth.login.text_validation.LoginTextValidationViewModel
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.simapps.ui_kit.edit_text.EditTextField
import com.simapps.ui_kit.edit_text.EditTextPasswordField

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShowSignIn(
    state: LogInFormState,
    isConnected: Boolean,
    loginTextValidationViewModel: LoginTextValidationViewModel,
    dataViewModel: DataViewModel,
    authViewModel: AuthViewModel,
    networkViewModel: NetworkViewModel,
    selectedBaseconfig: BaseConfig,
) {


    //   val loginCheckedLicenceList = authViewModel.loginCheckedLicenceList
    val selectedProInventoryLicense = authViewModel.selectedProInventoryLicense
    val selectedProCaisseLicense = authViewModel.selectedProCaisseLicense
    val options = listOf("ProInventory", "ProCaisse")
    val icons = listOf(painterResource(id = R.drawable.inventory_logo),  painterResource(id = R.drawable.procaisse_logo))
    val proLoginState = authViewModel.proLoginState
    val keyboardController = LocalSoftwareKeyboardController.current
    if (isConnected) {
        if (authViewModel.showIdentifiantList) {
            ListIdentifiantBottomSheet(
                onDismissRequest = {
                    authViewModel.onShowIdentifiantListChange(false)
                }
            )
        }



        EditTextField(
            text = state.identifiant,
            errorValue = state.identifiantError?.asString(),
            requestFocus = true,
            label = stringResource(id = R.string.username_title),
            onValueChange = {
                loginTextValidationViewModel.onLoginEvent(LogInFormEvent.IdentifiantChanged(it))
            },
            readOnly = false,
            enabled = true,
            showTrailingIcon = true,
            leadingIcon = Icons.Default.Home,
            keyboardType = KeyboardType.Password,
            imeAction = ImeAction.Next,
        )
        Spacer(Modifier.height(14.dp))

        EditTextPasswordField(
            value = state.password,
            errorValue = state.passwordError?.asString(),
            label = stringResource(id = R.string.password_title),
            imageVector = Icons.Default.Lock,
            imeAction = ImeAction.Done,
            onValueChanged = {
                loginTextValidationViewModel.onLoginEvent(LogInFormEvent.PasswordChanged(it))
            },
            keyboardAction = {
                loginTextValidationViewModel.onLoginEvent(LogInFormEvent.SubmitLogIn)
            }
        )
    } else {
        LottieAnim(lotti = R.raw.no_connection, size = 100.dp)
    }

    Spacer(Modifier.height(14.dp))
    if (dataViewModel.getInventoryActivationState() && dataViewModel.getProcaisseActivationState()) {

        MultiChoiceSegmentedButtonRow(
            modifier = Modifier.fillMaxWidth().padding(start = 9.dp, end = 9.dp)
        ) {
            options.forEachIndexed { index, label ->
                SegmentedButton(
                    shape = SegmentedButtonDefaults.itemShape(index = index, count = options.size),
                    icon = {
                        SegmentedButtonDefaults.Icon(active = (selectedProInventoryLicense && index==0) || (selectedProCaisseLicense && index == 1) ) {
                            Icon(
                                painter = icons[index],
                                contentDescription = null,
                                modifier = Modifier.size(SegmentedButtonDefaults.IconSize)
                            )
                        }
                    },
                    onCheckedChange = {


                        val isSelected = (selectedProInventoryLicense && index==0) || (selectedProCaisseLicense && index == 1)

                        if(index == 0) {
                            dataViewModel.saveIsProInventoryLicenseSelected(!isSelected)
                            authViewModel.onSelectedProInventoryLicense(!isSelected)
                        }
                        else if(index == 1) {
                            dataViewModel.saveIsProcaisseLicenseSelected(!isSelected)
                            authViewModel.onSelectedProCaisseLicense(!isSelected, from = "5")
                        }

                    },
                    checked = (selectedProInventoryLicense && index==0) || (selectedProCaisseLicense && index == 1)
                ) {
                    Text(label)
                }
            }
        }


    }
    Spacer(Modifier.height(14.dp))

    OutlinedButton (
        modifier = Modifier.fillMaxWidth().padding(start = 16.dp, end = 16.dp),
        enabled = networkViewModel.isConnected && !proLoginState.loading,
        onClick = {
            keyboardController?.hide()
            loginTextValidationViewModel.onLoginEvent(LogInFormEvent.SubmitLogIn)
        },
    ) {
        if (proLoginState.loading) {
            LottieAnim(lotti = R.raw.loading, size = 30.dp)
        } else {
            Text(text = stringResource(id = R.string.login_title))
        }
    }

    Spacer(Modifier.height(14.dp))
    if (selectedBaseconfig.designation_base == Globals.DEMO_BASE_CONFIG) {
        Spacer(Modifier.height(14.dp))
       // Spacer(Modifier.weight(1F))
        OutlinedButton(
            onClick = {
                authViewModel.onShowIdentifiantListChange(!authViewModel.showIdentifiantList)
            },
            shape = MaterialTheme.shapes.medium,
        ) {
            Text(text = stringResource(id = R.string.list_identifiant))
        }
        Spacer(Modifier.height(16.dp))
    }
}







