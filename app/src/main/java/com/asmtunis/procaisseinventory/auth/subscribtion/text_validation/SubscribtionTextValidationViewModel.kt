package com.asmtunis.procaisseinventory.auth.subscribtion.text_validation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.countrycodepicker.data.CountryData
import com.asmtunis.countrycodepicker.data.utils.getDefaultLangCode
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateBooleanIsTrue
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateDoubleNotZero
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateEmail
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateList
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePassword
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePhoneNumber
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import java.util.Locale

class SubscribtionTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
    private val validateList: ValidateList = ValidateList(),
    private val validateEmail: ValidateEmail = ValidateEmail(),
    private val validatePassword: ValidatePassword = ValidatePassword(),
    private val validatePhoneNumber: ValidatePhoneNumber = ValidatePhoneNumber(),
    private val validateDoubleNotZero: ValidateDoubleNotZero = ValidateDoubleNotZero(),
    private val validateBooleanIsTrue: ValidateBooleanIsTrue = ValidateBooleanIsTrue()
) : ViewModel() {


    var requestProcaisseMobilityLicence by mutableStateOf(false)

    fun onrequestProcaisseMobilityLicence(visibility: Boolean) {
        requestProcaisseMobilityLicence = visibility
    }

    var requestProInventoryLicence by mutableStateOf(false)

    fun onrequestProInventoryLicence(visibility: Boolean) {
        requestProInventoryLicence = visibility
    }

    /**
     * Subscribtion input edit text validation
     */
    var stateSbscribtion by mutableStateOf(SubscribtionFormState())
    private val validationSubscribtionEventChannel = Channel<ValidatSubscribtionionEvent>()
    val validationSubscribtionEvents = validationSubscribtionEventChannel.receiveAsFlow()

    fun onSubscribtionEvent(event: SubscribtionFormEvent) {
        when (event) {
            is SubscribtionFormEvent.NomsocieteChanged -> {
                stateSbscribtion = stateSbscribtion.copy(nomsociete = event.nomsociete)
            }

            is SubscribtionFormEvent.EtablissementChanged -> {
                stateSbscribtion = stateSbscribtion.copy(etablissement = event.etablissement)
            }

            is SubscribtionFormEvent.EmailChanged -> {
                stateSbscribtion = stateSbscribtion.copy(email = event.email)
            }

            /*    is SubscribtionFormEvent.PasswordChanged -> {
                    state = state.copy(password = event.password)
                }*/

            is SubscribtionFormEvent.PhoneNumberChanged -> {
                stateSbscribtion = stateSbscribtion.copy(phone = event.phonenumber1)
            }

            is SubscribtionFormEvent.SubscriptionTimeChanged -> {
                stateSbscribtion = stateSbscribtion.copy(subscriptionTime = event.subscriptionTime)
            }

            is SubscribtionFormEvent.CountryDataChanged -> {
                stateSbscribtion = stateSbscribtion.copy(countryData = event.countryData)
            }

            is SubscribtionFormEvent.SubmitSubscribtion -> {
                submitSubscribtionData()
            }

            is SubscribtionFormEvent.ProductSelectedChanged -> {
                stateSbscribtion = stateSbscribtion.copy(onProductSelected = event.productSelected)
            }
        }
    }

    private fun submitSubscribtionData() {
        val nomsocieteResult = validateIsNotEmptyString.execute(stateSbscribtion.nomsociete)
        val etablissementResult = validateIsNotEmptyString.execute(stateSbscribtion.etablissement)
        val emailResult = validateEmail.execute(stateSbscribtion.email)
        val productSelectedResult = validateBooleanIsTrue.execute(stateSbscribtion.onProductSelected)
        //  val passwordResult = validatePassword.execute(state.password)
        val phonenumberResult = validatePhoneNumber.execute(
            stateSbscribtion.phone,
            stateSbscribtion.countryData.phoneCode, //  getDefaultPhoneCode(),
            stateSbscribtion.countryData.countryCode //  getDefaultLangCode()
        )
        val hasError = listOf(
            nomsocieteResult,
            etablissementResult,
            emailResult,
            productSelectedResult,
            //    passwordResult,
            phonenumberResult
        ).any { !it.successful }

        if (hasError) {
            stateSbscribtion = stateSbscribtion.copy(
                emailError = emailResult.errorMessage,
                //   passwordError = passwordResult.errorMessage,
                nomsocieteError = nomsocieteResult.errorMessage,
                etablissementError = etablissementResult.errorMessage,
                phoneError = phonenumberResult.errorMessage,
                        onProductSelectedError = productSelectedResult.errorMessage

            )
            return
        }
        viewModelScope.launch {
            validationSubscribtionEventChannel.send(
                ValidatSubscribtionionEvent.SuccessSubscribtion(
                    stateSbscribtion
                )
            )
        }
    }



    sealed class ValidatSubscribtionionEvent {
        data class SuccessSubscribtion(val stateLoginSubscribtion: SubscribtionFormState) :
            ValidatSubscribtionionEvent()
    }


    fun resetVariable(){
        stateSbscribtion = stateSbscribtion.copy(
          nomsociete = "",
         nomsocieteError = null,
             onProductSelected = false,
         onProductSelectedError = null,
         etablissement = "",
         etablissementError = null,

         email = "",
         emailError = null,
        //  val password: String = "",
        // val passwordError: String? = null,
         phone = "",
         phoneError = null,


         countryData = CountryData(getDefaultLangCode().lowercase(Locale.getDefault())),
         subscriptionTime = "1 mois")

    }


    fun resetErrorVariable(){
        stateSbscribtion = stateSbscribtion.copy(
            nomsocieteError = null,
            onProductSelectedError = null,
            etablissementError = null,
            emailError = null,
            phoneError = null
        )

    }
}
