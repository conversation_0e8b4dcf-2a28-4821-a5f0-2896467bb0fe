package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.domaine


import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BaseConfigLicenseCheck(
    @SerialName("BaseConfig")
    val baseConfig: List<BaseConfig>,
    @SerialName("demandes")
    val demandes: List<DemandeHistory>
)