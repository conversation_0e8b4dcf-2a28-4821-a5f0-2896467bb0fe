package com.asmtunis.procaisseinventory.auth.subscribtion.view_model

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.countrycodepicker.data.utils.getCountryName
import com.asmtunis.procaisseinventory.auth.subscribtion.data.domaine.Demande
import com.asmtunis.procaisseinventory.auth.subscribtion.data.domaine.ServerResponse
import com.asmtunis.procaisseinventory.auth.subscribtion.data.remote.api.SubscribtionApi
import com.asmtunis.procaisseinventory.auth.subscribtion.text_validation.SubscribtionTextValidationViewModel
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.Globals.PROMOBILE
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_MOBILITY
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.INVENTORY_ACTIVATION_SENT
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_ACTIVATION_SENT
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject

@HiltViewModel
class SubscribtionViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val repository: SubscribtionApi,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {

   
    var state: RemoteResponseState<ServerResponse> by mutableStateOf(RemoteResponseState())
        private set
    fun postSubscribtion(demande: Demande, sendMobility: Boolean, sendInventory: Boolean) {
        viewModelScope.launch {
            val getObject1Task = async {
                if (sendMobility) {
                    demande.produit = PRO_CAISSE_MOBILITY
                    requestProCaisseSubscribtion(Json.encodeToString(demande))
                }
            }

            val getObject2Task = async {
                if (sendInventory) {
                    demande.produit = PRO_INVENTORY
                    requestInventorySubscribtion(Json.encodeToString(demande))
                }
            }

            getObject1Task.await()
            getObject2Task.await()
        }
    }

    suspend fun requestProCaisseSubscribtion(demande: String) {
        repository.postSubscribtion(demande).onEach { result ->
            when (result) {
                is DataResult.Success -> {
                    proCaisseLocalDb.dataStore.putBoolean(PROCAISSE_ACTIVATION_SENT, true)
                    state = RemoteResponseState(data = result.data!!, loading = false, error = null)
                }

                is DataResult.Loading -> {
                    state = RemoteResponseState(data = null, loading = true, error = null)
                }

                is DataResult.Error -> {
                    state = RemoteResponseState(data = null, loading = false, error = result.message)
                }
            }
        }.flowOn(dispatcher).launchIn(viewModelScope)
    }

    suspend fun requestInventorySubscribtion(demande: String) {
        repository.postSubscribtion(demande).onEach { result ->
            when (result) {
                is DataResult.Success -> {
                    proCaisseLocalDb.dataStore.putBoolean(INVENTORY_ACTIVATION_SENT, true)
                    state = RemoteResponseState(data = result.data!!, loading = false, error = null)
                }

                is DataResult.Loading -> {
                    state = RemoteResponseState(data = null, loading = true, error = null)
                }

                is DataResult.Error -> {
                    state = RemoteResponseState(data = null, loading = false, error = result.message)
                }
            }
        }.flowOn(dispatcher).launchIn(viewModelScope)
    }

    fun resetSubscribtionState() {
        state = RemoteResponseState(data = null, loading = false, error = null)
    }





    fun handleSubscribtionEvents(
        subscribtionTextValidationViewModel: SubscribtionTextValidationViewModel,
        validationSubscribtionEvents:  Flow<SubscribtionTextValidationViewModel. ValidatSubscribtionionEvent>,
        context: Context,
    ) {
        viewModelScope.launch {
           validationSubscribtionEvents.collect { event ->
                when (event) {
                    is SubscribtionTextValidationViewModel.ValidatSubscribtionionEvent.SuccessSubscribtion -> {
                        subscribtionTextValidationViewModel.resetErrorVariable()
                        val demande = Demande(
                            idDevice = DEVICE_ID, // DEVICE_ID
                            nomPrenom = event.stateLoginSubscribtion.nomsociete,
                            etablissement = event.stateLoginSubscribtion.etablissement,
                            numTel = event.stateLoginSubscribtion.countryData.phoneCode + event.stateLoginSubscribtion.phone,
                            email = event.stateLoginSubscribtion.email,
                            duree = when (event.stateLoginSubscribtion.subscriptionTime) {
                                "1 mois" -> 1
                                "3 mois" -> 3
                                "6 mois" -> 6
                                "1 an" -> 12
                                else -> 1
                            },
                            //   produit = "",
                            pays = String.format(
                                "%s",
                                context.getString(getCountryName(event.stateLoginSubscribtion.countryData.countryCode.lowercase()))
                            ),
                            device = PROMOBILE

                        )


                       postSubscribtion(
                            demande = demande,
                            sendMobility = subscribtionTextValidationViewModel.requestProcaisseMobilityLicence,
                            sendInventory = subscribtionTextValidationViewModel.requestProInventoryLicence
                        )

                    }
                }
            }
        }
    }
}
