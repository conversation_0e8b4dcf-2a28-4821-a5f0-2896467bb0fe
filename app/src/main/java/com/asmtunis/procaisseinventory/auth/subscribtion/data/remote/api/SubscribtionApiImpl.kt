package com.asmtunis.procaisseinventory.auth.subscribtion.data.remote.api

import com.asmtunis.procaisseinventory.auth.subscribtion.data.domaine.ServerResponse
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow

class SubscribtionApiImpl(private val client: HttpClient) : SubscribtionApi {
    override suspend fun postSubscribtion(demande: String): Flow<DataResult<ServerResponse>> {

        var headers = emptyMap<String, String>()

        //todo maybe pass Application-name as a parameter !
        if (demande.contains(Globals.PRO_INVENTORY))
            headers = mapOf("Application-name" to PRO_INVENTORY_AUTHORIZATION_TYPE_MENU)


        val formsData = mapOf("demande" to demande)

        return executePostApiCall<ServerResponse>(
            client = client,
            baseUrl = Urls.LICENCE_BASE_URL,
            formsData = formsData,
            headers = headers,
            endpoint = Urls.ADD_DEMANDE_LICENCE,

            )
    }
}
