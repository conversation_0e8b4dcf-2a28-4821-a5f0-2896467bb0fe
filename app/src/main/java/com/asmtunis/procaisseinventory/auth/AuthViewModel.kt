package com.asmtunis.procaisseinventory.auth

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.HeaderStatus
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.auth.login.text_validation.ValidationLoginEvent
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.domaine.BaseConfigLicenseCheck
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.remote.api.CheckLicenceApi
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.DEMO_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_MOBILITY
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.ktor.insertNetworkError
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.FIREBASE_TOKEN
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROCAISSE_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.IS_PROINVENTORY_LICENSE_SELECTED
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.navigation.BaseConfigRoute
import com.asmtunis.procaisseinventory.core.navigation.InventoryGraph
import com.asmtunis.procaisseinventory.core.navigation.ProcaisseGraph
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject

@HiltViewModel
class AuthViewModel@Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val repository: CheckLicenceApi,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork
) : ViewModel() {



    var subscribtionCheckedLicenceList= mutableStateListOf(-1)
        private set
    fun onAddSubscribtionCheckedLicenceListChange(value: Int) {
        subscribtionCheckedLicenceList.add(value)
    }

    fun onRemoveSubscribtionCheckedLicenceListChange(value: Int) {
        subscribtionCheckedLicenceList.remove(value)
    }


   /* var loginCheckedLicenceList= mutableStateListOf(-1)
        private set
    fun onAddLoginCheckedLicenceListChange(value: Int) {
        loginCheckedLicenceList.add(value)
    }

    fun onRemoveLoginCheckedLicenceListChange(value: Int) {
        loginCheckedLicenceList.remove(value)
    }
*/
    var showSubscribtionPeriod by mutableStateOf(false)
        private set
    fun onShowSubscribtionPeriodChange(value: Boolean) {
        showSubscribtionPeriod = value
    }


    var checkLicensestate: RemoteResponseState<BaseConfigLicenseCheck> by mutableStateOf(RemoteResponseState())
        private set

    var selectedIndexBaseConfig by mutableStateOf(BaseConfig())
        private set
    fun onSelectedIndexBaseConfigValue(value: BaseConfig) {
        selectedIndexBaseConfig = value
    }

    var selectedProCaisseLicense by mutableStateOf(false)
        private set
    fun onSelectedProCaisseLicense(value: Boolean, from: String) {
        selectedProCaisseLicense = value
    }


    var showIdentifiantList by mutableStateOf(false)
        private set
    fun onShowIdentifiantListChange(value: Boolean) {
        showIdentifiantList = value
    }

    var selectedProInventoryLicense by mutableStateOf(false)
        private set
    fun onSelectedProInventoryLicense(value: Boolean) {
        selectedProInventoryLicense = value
    }
    var baseConfigList by mutableStateOf(emptyList<BaseConfig>())
        private set
    init{
            checkSelectedBaseConfig()
            requestCheckLicense()


    }

    private fun checkSelectedBaseConfig() {
        viewModelScope.launch {
            val selsectedBase = proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
            if (!selsectedBase.isNullOrEmpty()) {
                onSelectedIndexBaseConfigValue(Json.decodeFromString(selsectedBase))
            } else   onSelectedIndexBaseConfigValue(BaseConfig())

            onSelectedProInventoryLicense(proCaisseLocalDb.dataStore.getBoolean(IS_PROINVENTORY_LICENSE_SELECTED).first())
            onSelectedProCaisseLicense(proCaisseLocalDb.dataStore.getBoolean(IS_PROCAISSE_LICENSE_SELECTED).first(), from = "1")
        }
    }


     fun getBaseConfigList(){
        viewModelScope.launch {
            proCaisseLocalDb.baseConfig.getAll().collect {

                    baseConfigList = it

            }
        }

    }


     fun requestCheckLicense() {//TODO ADD requestCheckLicense WHEN NAV TO A CERTAIN SCREEN OR  A TIMER MAYBE
         resetCheckLicense()
        viewModelScope.launch {
            repository.checkLicenses(DEVICE_ID).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                        viewModelScope.launch(dispatcher) {
                            proCaisseLocalDb.baseConfig.deleteAll()
                            proCaisseLocalDb.licence.deleteAll()
                            proCaisseLocalDb.baseConfig.upsertAll(result.data!!.baseConfig)
                            proCaisseLocalDb.licence.upsertAll(result.data.baseConfig.flatMap { it.licences })
                        }


                        checkLicensestate = RemoteResponseState(data = result.data!!, loading = false, error = null)

                    }

                    is DataResult.Loading -> {

                        checkLicensestate = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        checkLicensestate = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            } /*.flowOn(dispatcher)*/.launchIn(this)
        }

    }




    fun resetCheckLicense() {
        checkLicensestate = RemoteResponseState(data = null, loading = false, error = null)
    }




    var remainDayLicenceProCaisse: String by mutableStateOf("")
        private set

    var remainDayLicenceInventory: String by mutableStateOf("")
        private set




    fun deleteAllBaseConfig(){
        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.baseConfig.deleteAll()
        }
    }

    val demoBaseConfig = listOf(
        BaseConfig(
            id_base_config = "999",

            key_base = "WNA-WG5-6XP",
            dbName = "ed8ad57eb2273cae5733c088f62b4589",
            dbIpAddress = "d6e33daa4332f3e88df5d984a2047c72",
            adresse_ip = "*************",
            port = "5005",
            username = "2148ed972ff6ca8e0bf0484a9bcdc179",
            password = "dd0a76c46df2b3db8085a90680feaa99",
            //designation_base = "Pro-Caisse PROD",
            designation_base = DEMO_BASE_CONFIG,
            produit = "ProInventory Mobile;ProCaisse Mobility",
            id_entreprise = "980",
            date_creation = "10-08-2022 12:32:20",

            licences = listOf(
                Licence(
                    id = 14966,
                    activat = "true",
                    bonSortie= "demo",
                    codeActivation= "0",
                    dateActivation= "2023-04-25",
                    dateConx= "2023-04-25 12=48=46",
                    datef= "2050-01-25",
                    dater= 9771.0,
                    demo= "true",
                    device= "promobile",
                    email= "<EMAIL>",
                    etablissement= "demo",
                    etat= null,
                    heureActivation= "12=48=08",
                    idClient= null,
                    id_device= "e019f1db30209e52",
                    id_user= "945",
                    nom_prenom= "demo",
                    offre= "0",
                    pays= "Tunisia",
                    port= 900,
                    prix= "0",
                    produit= PRO_INVENTORY,
                    telephone= "+21699999999",
                    type= "Abonnement",
                    url= null,
                    user= "Youssef aouadni",
                    version= "v13"),
                Licence(
                    id= 14965,
                    activat= "true",
                    bonSortie= "demo",
                    codeActivation= "0",
                    dateActivation= "2023-04-25",
                    dateConx= "2023-04-25 12=48=47",
                    datef= "2050-01-25",
                    dater= 9771.0,
                    demo= "true",
                    device= "promobile",
                    email= "<EMAIL>",
                    etablissement= "demo",
                    etat= null,
                    heureActivation= "12=47=05",
                    idClient= null,
                    id_device= "e019f1db30209e52",
                    id_user= "945",
                    nom_prenom= "demo",
                    offre= "0",
                    pays= "Tunisia",
                    port= 900,
                    prix= "0",
                    produit= PRO_CAISSE_MOBILITY,
                    telephone= "+21699999999",
                    type= "Abonnement",
                    url= null,
                    user= "Youssef aouadni",
                    version= "v13"
                )
            )
        )/*,
                BaseConfig(
                    id_base_config = "1035",
                    key_base = "D0Q-D16-JYG",
                    dbName = "86d23e744403d37ab3f1b237595a9261",
                    dbIpAddress = "6845728aa947732909031c16701de252",
                    adresse_ip = "procaisse.asmtechtn.com",
                    port = "443",
                    username = "2148ed972ff6ca8e0bf0484a9bcdc179",
                    password = "efe0914c9807cf450762d44b2f34ce6c",
                    designation_base = "Pro-Caisse Mobile DEV",
                    produit = "",
                    id_entreprise = "",
                    date_creation = "",

                    licences = emptyList()
                )*/
    )
    fun setBaseConfigTable(){

        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.baseConfig.deleteAll()
          //  proCaisseLocalDb.dataStore.putString(SELECTED_BASE_CONFIG,Json.encodeToString(baseConfig.first()))
            proCaisseLocalDb.baseConfig.upsertAll(demoBaseConfig)
        }

    }



    var showIsLoginOut: Boolean by mutableStateOf(false)
        private set
    fun setShowisLoginOut(value: Boolean){
        showIsLoginOut = value

    }




    private fun isValidLicense(output: Licence?): Boolean {
        return output != null && (output.id != 0L && output.id_device != null /*&& !output.id_device.isEmpty()*/)
    }
    fun checkRemainingDayLicence(baseConfig: BaseConfig) {
        val licenceProCaisse = baseConfig.licences.firstOrNull { it.produit ==  PRO_CAISSE_MOBILITY}
        val licenceProInventory = baseConfig.licences.firstOrNull { it.produit ==  PRO_INVENTORY}


        if (licenceProCaisse!!.dater != null) {
            if (licenceProCaisse.dater!! < 30 &&
                licenceProCaisse.dater!! > 0 //&&
               // licenceProCaisse.demo.equals("true")
            ) {
                remainDayLicenceProCaisse = licenceProCaisse.dater!!.toString()
            }
        }


        if (licenceProInventory!!.dater != null) {
            if (licenceProInventory.dater!! < 30 &&
                licenceProInventory.dater!! > 0 //&&
            // licenceProInventory.demo.equals("true")
            ) {
                remainDayLicenceInventory = licenceProInventory.dater!!.toString()
            }
        }
    }









    /**
     *    L O G I N
     */


    var proLoginState: RemoteResponseState<Utilisateur> by mutableStateOf(RemoteResponseState())
        private set

     fun postLogin(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        getData: Boolean = true
    ) {
         viewModelScope.launch {
       //  BASE_URL = String.format(validateBaseUrl(baseConfig), baseConfig.adresse_ip, baseConfig.port)


         val baseConfigObj = GenericObject(
             baseConfig,
             Json.encodeToJsonElement(utilisateur)
         )
         proCaisseRemote.login.postLogin(Json.encodeToString(baseConfigObj), getLoginHeader())
             .onEach { result ->
                 when (result) {
                     is DataResult.Success -> {
                      Globals.USER_ID = result.data!!.codeUt
                         proLoginState = RemoteResponseState(data = result.data!!, loading = false, error = null)

                         proCaisseLocalDb.utilisateur.deleteAll()
                         proCaisseLocalDb.authorization.deleteAll()


                         proCaisseLocalDb.utilisateur.upsert(result.data)

                         val authorizationsList = result.data.autorisationUser//.flatten()

                         proCaisseLocalDb.authorization.upsertAll(authorizationsList)

                         if(baseConfig.produit.contains(PRO_CAISSE_MOBILITY)) {

                             FirebaseMessaging.getInstance().token.addOnCompleteListener(
                                 OnCompleteListener { task ->
                                     if (!task.isSuccessful) {
                                         return@OnCompleteListener
                                     }
                                     // Get new FCM registration token


                                     // viewModelScope.launch(dispatcher) {
                                     val token = task.result
                                     //   proCaisseLocalDb.dataStore.putString(FIREBASE_TOKEN, token)


                                     setUserFireBaseToken(
                                         token = token,
                                         baseConfig = baseConfig
                                     )

                                     //    }


                                 }
                             )
                         }

                     }

                     is DataResult.Loading -> {
                         proLoginState =
                             RemoteResponseState(data = null, loading = true, error = null)
                     }

                     is DataResult.Error -> {
                         proLoginState = RemoteResponseState(
                             data = null,
                             loading = false,
                             error = result.message
                         )

                         //   getUtilisateur(null)
                     }
                 }
             }.flowOn(dispatcherIO).launchIn(this)
     }
    }

    fun loginFinshed() {
        proLoginState = RemoteResponseState(data = null, loading = false, error = null)

    }



    fun handleBaseConfig():String =
        if ((!selectedIndexBaseConfig.produit.contains(PRO_CAISSE_MOBILITY) && !selectedIndexBaseConfig.produit.contains(PRO_INVENTORY)) /*&&
            (!selectedProInventoryLicense && !selectedProCaisseLicense)*/)
            HeaderStatus.EMPTY.header
        else if (selectedIndexBaseConfig.produit.contains(PRO_CAISSE_MOBILITY) && selectedIndexBaseConfig.produit.contains(PRO_INVENTORY) /*&&
            (selectedProInventoryLicense && selectedProCaisseLicense)*/)
            HeaderStatus.PROCAISSE_AND_INVENTORY.header
        else if(selectedIndexBaseConfig.produit.contains(PRO_CAISSE_MOBILITY) && !selectedIndexBaseConfig.produit.contains(PRO_INVENTORY) /*&&
            (!selectedProInventoryLicense && selectedProCaisseLicense)*/)
            HeaderStatus.PROCAISSE.header
        else if(!selectedIndexBaseConfig.produit.contains(PRO_CAISSE_MOBILITY) && selectedIndexBaseConfig.produit.contains(PRO_INVENTORY) /*&&
            (selectedProInventoryLicense && !selectedProCaisseLicense)*/)
            HeaderStatus.INVENTORY.header
        else HeaderStatus.EMPTY.header


    fun getLoginHeader():String =
         if(!selectedProInventoryLicense && !selectedProCaisseLicense) HeaderStatus.EMPTY.header
        else if(selectedProInventoryLicense && selectedProCaisseLicense) HeaderStatus.PROCAISSE_AND_INVENTORY.header
        else if(!selectedProInventoryLicense && selectedProCaisseLicense)  HeaderStatus.PROCAISSE.header
        else if(selectedProInventoryLicense && !selectedProCaisseLicense) HeaderStatus.INVENTORY.header
        else ""

    fun navToInvOrProCaisse() : Any =
        /*!dataViewModel.getProcaisseActivationState() &&*/
        if(!selectedProInventoryLicense && !selectedProCaisseLicense)  BaseConfig
     else  if(selectedProCaisseLicense) ProcaisseGraph
        else InventoryGraph

    fun isSameRemoteDB(selectedBase : BaseConfig, base : BaseConfig) : Boolean  =
        selectedBase.dbName == base.dbName &&
        selectedBase.id_base_config == base.id_base_config &&
        selectedBase.password == base.password &&
        selectedBase.dbIpAddress ==  base.dbIpAddress  &&
        selectedBase.username == base.username &&
        selectedBase.designation_base == base.designation_base











    fun getSelectedBaseConfingFromRemoteResponse(baseConfigList: List<BaseConfig>, baseConfig: BaseConfig): BaseConfig? =
        baseConfigList.firstOrNull {
            it.adresse_ip == baseConfig.adresse_ip &&
                    it.date_creation == baseConfig.date_creation &&
                    it.dbIpAddress == baseConfig.dbIpAddress &&
                    it.dbName == baseConfig.dbName &&
                    it.designation_base == baseConfig.designation_base &&
                    it.id_entreprise == baseConfig.id_entreprise &&
                    it.key_base == baseConfig.key_base &&
                    it.id_base_config == baseConfig.id_base_config &&
                    it.password == baseConfig.password &&
                    it.port == baseConfig.port &&
                    it.produit == baseConfig.produit &&
                    it.username == baseConfig.username

        }


    /**
     * *******************************
     */

    //Firebase

    var setTokenState: RemoteResponseState<Boolean> by mutableStateOf(RemoteResponseState())
        private set

    fun resetTokenState() {
        setTokenState = RemoteResponseState()
    }
    fun setUserFireBaseToken(token: String, baseConfig: BaseConfig) {
        viewModelScope.launch {
            proCaisseLocalDb.dataStore.putString(FIREBASE_TOKEN, token)

            val baseConfigObj = GenericObject(
                baseConfig,
                null
            )
            proCaisseRemote.login.insertUserToken(baseConfig = Json.encodeToString(baseConfigObj), token = token).onEach { result ->
                when (result) {
                    is DataResult.Success -> {
                       if(token.isBlank() || token.isEmpty()) proCaisseLocalDb.utilisateur.deleteAll()
                        setTokenState = RemoteResponseState(data = result.data?:false, loading = false, error = null)
                    }

                    is DataResult.Loading -> {

                        setTokenState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        insertNetworkError(
                            proCaisseLocalDb = proCaisseLocalDb,
                            url = Urls.INSERT_USER_TOKEN,
                            errorMessage = result.message
                        )
                        setTokenState = RemoteResponseState(data = null, loading = false, error = result.message)
                    }
                }
            } .flowOn(dispatcherIO).launchIn(this)

        }



    }



    fun handleLoginEvents(
        navigate: (route: Any) -> Unit,
        validationLoginEvent: ValidationLoginEvent,
        toaster: ToasterState,
        context: Context,
        restErrorVariables: () -> Unit,
        selectedBaseconfig: BaseConfig,
    ) {


                when (validationLoginEvent) {
                    is ValidationLoginEvent.LoginSubscribtion -> {
                       checkSelectedBaseConfig()
                        restErrorVariables()
                        /**
                         * here request login
                         */

                        if (!selectedProInventoryLicense && !selectedProCaisseLicense) {
                            showToast(
                                context = context,
                                toaster = toaster,
                                message = context.resources.getString(R.string.error) + "\n"+ context.resources.getString(
                                    R.string.select_licence),
                                type =  ToastType.Error,
                            )
                            return
                        }



                        setShowisLoginOut(false)
                        when (handleBaseConfig()) {
                            HeaderStatus.EMPTY.header -> navigate(BaseConfigRoute)
                            HeaderStatus.PROCAISSE_AND_INVENTORY.header,
                            HeaderStatus.PROCAISSE.header,
                            HeaderStatus.INVENTORY.header,
                            -> {
                                postLogin(
                                    baseConfig = selectedBaseconfig,
                                    utilisateur =
                                    Utilisateur(
                                        Login = validationLoginEvent.stateLogin.identifiant,
                                        Passe = validationLoginEvent.stateLogin.password,
                                    ),
                                )
                            }

                            else -> {
                                navigate(BaseConfigRoute)
                            }
                        }
                    }
                }


    }


    /**
     * get list utilisateur
     */

    var getUtilisateursState: RemoteResponseState<List<Utilisateur>> by mutableStateOf(RemoteResponseState())
        private set

    fun getUtilisateur(baseConfig: BaseConfig) {
        viewModelScope.launch {
        //    BASE_URL = String.format(validateBaseUrl(baseConfig), baseConfig.adresse_ip, baseConfig.port)


            val baseConfigObj = GenericObject(
                baseConfig,
                null
            )
            proCaisseRemote.login.getUtilisateurs(Json.encodeToString(baseConfigObj), getLoginHeader())
                .onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                            getUtilisateursState = RemoteResponseState(data = result.data, loading = false, error = null)

                           proCaisseLocalDb.utilisateur.deleteAll()
                         //   proCaisseLocalDb.authorization.deleteAll()


                            proCaisseLocalDb.utilisateur.upsertAll(result.data?: emptyList())







                        }

                        is DataResult.Loading -> {
                            getUtilisateursState =
                                RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            getUtilisateursState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )

                            //   getUtilisateur(null)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
        }
    }
}
