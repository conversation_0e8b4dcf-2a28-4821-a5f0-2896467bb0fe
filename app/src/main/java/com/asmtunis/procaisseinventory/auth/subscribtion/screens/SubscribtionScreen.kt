@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.auth.subscribtion.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CutCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Home
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.MultiChoiceSegmentedButtonRow
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.material3.windowsizeclass.WindowHeightSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.countrycodepicker.data.utils.getNumberHint
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.AuthViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.spalsh_screen.screens.ShowMsgThenNavigate
import com.asmtunis.procaisseinventory.auth.subscribtion.data.domaine.ServerResponse
import com.asmtunis.procaisseinventory.auth.subscribtion.text_validation.SubscribtionFormEvent
import com.asmtunis.procaisseinventory.auth.subscribtion.text_validation.SubscribtionTextValidationViewModel
import com.asmtunis.procaisseinventory.auth.subscribtion.view_model.SubscribtionViewModel
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_MOBILITY
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.SubscribtionRoute
import com.asmtunis.procaisseinventory.core.navigation.WaitingLicenceActivationRoute
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.PhoneCountryPicker
import com.simapps.ui_kit.edit_text.EditTextField
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SubscribtionScreen(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    authViewModel: AuthViewModel,
    settingViewModel: SettingViewModel,
) {
    val subscribtionTextValidationViewModel: SubscribtionTextValidationViewModel = hiltViewModel()
    val subscribtionViewModel = hiltViewModel<SubscribtionViewModel>()
    val subscribtionState = subscribtionViewModel.state
    val isConnected = networkViewModel.isConnected
    val checkedLicenceList = authViewModel.subscribtionCheckedLicenceList
    val scope = rememberCoroutineScope()

    val context = LocalContext.current

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val requestProInventoryLicence = subscribtionTextValidationViewModel.requestProInventoryLicence
    val requestProcaisseMobilityLicence = subscribtionTextValidationViewModel.requestProcaisseMobilityLicence

    val options = ArrayList<String>()
    val icons = ArrayList<Painter>()

    val state = subscribtionTextValidationViewModel.stateSbscribtion

    val selectedBaseConfig = dataViewModel.selectedBaseConfig

    val uiWindowState = settingViewModel.uiWindowState
    val windowSize = uiWindowState.windowSize!!

    if (selectedBaseConfig == BaseConfig() ||
        (!dataViewModel.getInventoryActivationState() ||
                (selectedBaseConfig.designation_base == Globals.DEMO_BASE_CONFIG &&
                        selectedBaseConfig.licences.any { it.produit == Globals.PRO_INVENTORY })) &&
        !dataViewModel.getProInventorySubscribtionSent()
    ) {
        options.add(PRO_INVENTORY_AUTHORIZATION_TYPE_MENU +  if(state.onProductSelectedError!=null && !requestProInventoryLicence) " * "  else "",)
        icons.add(painterResource(id = R.drawable.inventory_logo))


    }

    if (selectedBaseConfig == BaseConfig() ||
        (!dataViewModel.getProcaisseActivationState() ||
                (selectedBaseConfig.designation_base == Globals.DEMO_BASE_CONFIG &&
                        selectedBaseConfig.licences.any { it.produit == PRO_CAISSE_MOBILITY }) &&
                !dataViewModel.getProcaisseSubscribtionSent())
    ) {
        options.add(PRO_CAISSE_MOBILITY +  if(state.onProductSelectedError!=null && !requestProcaisseMobilityLicence) " * "  else "")
        icons.add(painterResource(id = R.drawable.procaisse_logo))
    }
    LaunchedEffect(key1 = Unit) {
        subscribtionViewModel.handleSubscribtionEvents(
            subscribtionTextValidationViewModel = subscribtionTextValidationViewModel,
            validationSubscribtionEvents = subscribtionTextValidationViewModel.validationSubscribtionEvents,
            context = context,
        )



    }


    if(authViewModel.showSubscribtionPeriod)
        ModalBottomSheet(
            sheetState = sheetState,
            onDismissRequest = {
                scope.launch {
                    sheetState.hide()
                }
                authViewModel.onShowSubscribtionPeriodChange(false)
            },
        ) {

            val subscriptionPeriods = listOf("1 mois", "3 mois", "6 mois", "1 an")

            Spacer(modifier = Modifier.height(20.dp))
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp, horizontal = 12.dp)
            ) {
                Text(
                    text = stringResource(R.string.subscribtion_for, state.subscriptionTime),
                    style = MaterialTheme.typography.titleLarge
                )

                Spacer(modifier = Modifier.height(20.dp))
                HorizontalDivider()


                subscriptionPeriods.forEach { period ->
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        textAlign = TextAlign.Center,
                        text = period,
                        color = if(state.subscriptionTime == period) MaterialTheme.colorScheme.error else Color.Unspecified,
                        modifier = Modifier.fillMaxWidth().clickable {
                            subscribtionTextValidationViewModel.onSubscribtionEvent(
                                SubscribtionFormEvent.SubscriptionTimeChanged(period)
                            )
                            authViewModel.onShowSubscribtionPeriodChange(false)
                        },
                        style = if(state.subscriptionTime == period) MaterialTheme.typography.titleLarge else MaterialTheme.typography.titleMedium
                        )
                    Spacer(modifier = Modifier.height(12.dp))
                    HorizontalDivider()
                }
            }
        }

    Scaffold(
        topBar = {
            AppBar(
                onNavigationClick = {},
                showNavIcon = false,
                title = stringResource(R.string.demandeLicence),
                baseConfig = selectedBaseConfig,
                isConnected = networkViewModel.isConnected
            )
        },
    ) { padding ->



            if (subscribtionState.data != null) {
                val destination =
                    if (dataViewModel.getProcaisseActivationState() || dataViewModel.getInventoryActivationState()) {
                        authViewModel.navToInvOrProCaisse()
                    } else WaitingLicenceActivationRoute

                ShowMsgThenNavigate(
                    navigate = { navigate(it)},
                    navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                        navigatePopUpTo(route, popUpTo, isInclusive)
                    },
                    destination = destination,
                    msg = subscribtionState.data.message,
                    resetState = { subscribtionViewModel.resetSubscribtionState() })
            } else {

                if (subscribtionState.error != null) {

                    ShowMsgThenNavigate(
                        navigate = { navigate(it)},
                        navigatePopUpTo = { route: Any, popUpTo: Any, isInclusive: Boolean ->
                            navigatePopUpTo(route, popUpTo, isInclusive)
                        },
                        destination = SubscribtionRoute,
                        msg = context.resources.getString(R.string.verify_connection),
                        resetState = { subscribtionViewModel.resetSubscribtionState() }
                    )


                } else {

                    when (windowSize.heightSizeClass) {
                        WindowHeightSizeClass.Compact -> {
                            MainScreenRow(
                                subscribtionTextValidationViewModel = subscribtionTextValidationViewModel,
                                authViewModel = authViewModel,
                                dataViewModel = dataViewModel,
                                padding = padding,
                                isConnected = isConnected,
                                subscribtionState = subscribtionState,
                                requestProInventoryLicence = requestProInventoryLicence,
                                requestProcaisseMobilityLicence = requestProcaisseMobilityLicence,
                                options = options,
                                icons = icons,
                                checkedLicenceList = checkedLicenceList
                            )


                        }
                        WindowHeightSizeClass.Medium,
                        WindowHeightSizeClass.Expanded -> {

                            MainScreenColumn(
                                subscribtionTextValidationViewModel = subscribtionTextValidationViewModel,
                                authViewModel = authViewModel,
                                dataViewModel = dataViewModel,
                                padding = padding,
                                isConnected = isConnected,
                                subscribtionState = subscribtionState,
                                requestProInventoryLicence = requestProInventoryLicence,
                                requestProcaisseMobilityLicence = requestProcaisseMobilityLicence,
                                options = options,
                                icons = icons,
                                checkedLicenceList = checkedLicenceList
                            )
                        }
                        else -> {
                            MainScreenColumn(
                                subscribtionTextValidationViewModel = subscribtionTextValidationViewModel,
                                authViewModel = authViewModel,
                                dataViewModel = dataViewModel,
                                padding = padding,
                                isConnected = isConnected,
                                subscribtionState = subscribtionState,
                                requestProInventoryLicence = requestProInventoryLicence,
                                requestProcaisseMobilityLicence = requestProcaisseMobilityLicence,
                                options = options,
                                icons = icons,
                                checkedLicenceList = checkedLicenceList
                            )
                        }
                    }


                }
            }










    }

}

@Composable
fun MainScreenColumn(
    subscribtionTextValidationViewModel: SubscribtionTextValidationViewModel,
    authViewModel: AuthViewModel,
    dataViewModel: DataViewModel,
    padding: PaddingValues,
    isConnected: Boolean,
    subscribtionState: RemoteResponseState<ServerResponse>,
    requestProInventoryLicence: Boolean,
    requestProcaisseMobilityLicence: Boolean,
    options: ArrayList<String>,
    icons: ArrayList<Painter>,
    checkedLicenceList: List<Int>
) {
    val context = LocalContext.current
    val state = subscribtionTextValidationViewModel.stateSbscribtion
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(padding)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
    Image(
        painter = painterResource(id = R.drawable.ic_asm),
        contentDescription = "",
        modifier = Modifier
            .padding(6.dp)
            .background(Color.DarkGray, CutCornerShape(6.dp))
            .padding(6.dp)
    )

    //  Spacer(Modifier.height(6.dp))

    Text(text = DEVICE_ID)
    Spacer(Modifier.height(12.dp))

    EditTextField(
        text = state.nomsociete,
        errorValue = state.nomsocieteError?.asString(),
        onValueChange = {
            subscribtionTextValidationViewModel.onSubscribtionEvent(
                SubscribtionFormEvent.NomsocieteChanged(it)
            )
        },
        label = stringResource(R.string.nom_prenom_societe),
        leadingIcon = Icons.Default.Home,
        keyboardType = KeyboardType.Text,
        imeAction = ImeAction.Next
    )

    Spacer(Modifier.height(12.dp))

    EditTextField(
        text = state.etablissement,
        errorValue = state.etablissementError?.asString(),
        onValueChange = {
            subscribtionTextValidationViewModel.onSubscribtionEvent(
                SubscribtionFormEvent.EtablissementChanged(
                    it
                )
            )
        },
        label = stringResource(R.string.Etablissement),
        leadingIcon = Icons.Default.Home,
        keyboardType = KeyboardType.Text,
        imeAction = ImeAction.Next
    )

    Spacer(Modifier.height(12.dp))

    EditTextField(
        text = state.email,
        errorValue = state.emailError?.asString(),
        onValueChange = {
            subscribtionTextValidationViewModel.onSubscribtionEvent(
                SubscribtionFormEvent.EmailChanged(it)
            )
        },
        label = stringResource(R.string.email_field_title),
        leadingIcon = Icons.Default.Email,
        keyboardType = KeyboardType.Email,
        imeAction = ImeAction.Next
    )

    Spacer(Modifier.height(12.dp))

    PhoneCountryPicker(
        countryCode = subscribtionTextValidationViewModel.stateSbscribtion.countryData.countryCode.uppercase(),
        onCountryChange = {
            subscribtionTextValidationViewModel.onSubscribtionEvent(
                SubscribtionFormEvent.CountryDataChanged(it)
            )
        },
        stringId = R.string.phone1_field_title,

        errorValue = subscribtionTextValidationViewModel.stateSbscribtion.phoneError,
        value = subscribtionTextValidationViewModel.stateSbscribtion.phone,
        hint = getNumberHint(subscribtionTextValidationViewModel.stateSbscribtion.countryData.countryCode),
        onValueChange = {
            subscribtionTextValidationViewModel.onSubscribtionEvent(
                SubscribtionFormEvent.PhoneNumberChanged(
                    it
                )
            )
        },
        imeAction = ImeAction.Done
    )

    Spacer(Modifier.height(12.dp))

    FilledTonalButton(
        onClick = {
            authViewModel.onShowSubscribtionPeriodChange(true)
        }
    ) {
        Text(text = stringResource(R.string.subscribtion_for, state.subscriptionTime))
    }
    Spacer(Modifier.height(12.dp))

    //  LoadingAnimation()


    MultiChoiceSegmentedButtonRow(
        modifier = Modifier.fillMaxWidth().padding(start = 9.dp, end = 9.dp)
    ) {
        options.forEachIndexed { index, label ->
            SegmentedButton(
                shape = SegmentedButtonDefaults.itemShape(index = index, count = options.size),
                icon = {
                    SegmentedButtonDefaults.Icon(active = index in checkedLicenceList) {
                        Icon(
                            painter = icons[index],
                            contentDescription = null,
                            modifier = Modifier.size(SegmentedButtonDefaults.IconSize)
                        )
                    }
                },
                onCheckedChange = {
                    if (index in checkedLicenceList) {
                        authViewModel.onRemoveSubscribtionCheckedLicenceListChange(index)
                    } else {
                        authViewModel.onAddSubscribtionCheckedLicenceListChange(index)
                    }

                    if (label.contains(PRO_CAISSE_MOBILITY)) {
                        subscribtionTextValidationViewModel.onrequestProcaisseMobilityLicence(!requestProcaisseMobilityLicence)
                        subscribtionTextValidationViewModel.onSubscribtionEvent(
                            SubscribtionFormEvent.ProductSelectedChanged(
                                !requestProcaisseMobilityLicence || subscribtionTextValidationViewModel.requestProInventoryLicence
                            )
                        )
                    } else if (label.contains(PRO_INVENTORY_AUTHORIZATION_TYPE_MENU)) {
                        subscribtionTextValidationViewModel.onrequestProInventoryLicence(!requestProInventoryLicence)
                        subscribtionTextValidationViewModel.onSubscribtionEvent(
                            SubscribtionFormEvent.ProductSelectedChanged(
                                !requestProInventoryLicence || requestProcaisseMobilityLicence
                            )
                        )
                    }
                },
                checked = index in checkedLicenceList
            ) {
                Text(label)
            }
        }
    }




    Spacer(Modifier.height(12.dp))
    Button(
        enabled = isConnected && !subscribtionState.loading &&
                (subscribtionTextValidationViewModel.requestProcaisseMobilityLicence ||
                        subscribtionTextValidationViewModel.requestProInventoryLicence),
        onClick = {
            subscribtionTextValidationViewModel.onSubscribtionEvent(SubscribtionFormEvent.SubmitSubscribtion)
        },
        modifier = Modifier.wrapContentSize(),
        shape = MaterialTheme.shapes.medium
    ) {
        if (!isConnected) LottieAnim(lotti = R.raw.no_connection, size = 30.dp)


        if (subscribtionState.loading) LottieAnim(lotti = R.raw.loading, size = 30.dp)
        else Text(text = stringResource(R.string.send))
    }
}
}



@Composable
fun MainScreenRow(
    subscribtionTextValidationViewModel: SubscribtionTextValidationViewModel,
    authViewModel: AuthViewModel,
    dataViewModel: DataViewModel,
    padding: PaddingValues,
    isConnected: Boolean,
    subscribtionState: RemoteResponseState<ServerResponse>,
    requestProInventoryLicence: Boolean,
    requestProcaisseMobilityLicence: Boolean,
    options: ArrayList<String>,
    icons: ArrayList<Painter>,
    checkedLicenceList: List<Int>
) {
    val context = LocalContext.current
    val state = subscribtionTextValidationViewModel.stateSbscribtion

    Row(
        modifier = Modifier
            .fillMaxSize()
            .padding(padding)
            .padding(start = 9.dp, end = 9.dp),
    ) {
        Column(
            modifier = Modifier
                .customWidth(0.42f)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_asm),
                contentDescription = "",
                modifier = Modifier
                    .padding(6.dp)
                    .background(Color.DarkGray, CutCornerShape(6.dp))
                    .padding(6.dp)
            )

            //  Spacer(Modifier.height(6.dp))

            Text(text = DEVICE_ID)
            Spacer(Modifier.height(12.dp))

            Button(
                enabled = isConnected && !subscribtionState.loading &&
                        (subscribtionTextValidationViewModel.requestProcaisseMobilityLicence ||
                                subscribtionTextValidationViewModel.requestProInventoryLicence),
                onClick = {
                    subscribtionTextValidationViewModel.onSubscribtionEvent(SubscribtionFormEvent.SubmitSubscribtion)
                },
                modifier = Modifier.wrapContentSize(),
                shape = MaterialTheme.shapes.medium
            ) {
                if (!isConnected) LottieAnim(lotti = R.raw.no_connection, size = 30.dp)


                if (subscribtionState.loading) LottieAnim(lotti = R.raw.loading, size = 30.dp)
                else Text(text = stringResource(R.string.send))
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            EditTextField(
                text = state.nomsociete,
                errorValue = state.nomsocieteError?.asString(),
                onValueChange = {
                    subscribtionTextValidationViewModel.onSubscribtionEvent(
                        SubscribtionFormEvent.NomsocieteChanged(it)
                    )
                },
                label = stringResource(R.string.nom_prenom_societe),
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )

            Spacer(Modifier.height(12.dp))

            EditTextField(
                text = state.etablissement,
                errorValue = state.etablissementError?.asString(),
                onValueChange = {
                    subscribtionTextValidationViewModel.onSubscribtionEvent(
                        SubscribtionFormEvent.EtablissementChanged(
                            it
                        )
                    )
                },
                label = stringResource(R.string.Etablissement),
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )

            Spacer(Modifier.height(12.dp))

            EditTextField(
                text = state.email,
                errorValue = state.emailError?.asString(),
                onValueChange = {
                    subscribtionTextValidationViewModel.onSubscribtionEvent(
                        SubscribtionFormEvent.EmailChanged(it)
                    )
                },
                label = stringResource(R.string.email_field_title),
                leadingIcon = Icons.Default.Email,
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Next
            )

            Spacer(Modifier.height(12.dp))

            PhoneCountryPicker(
                countryCode = subscribtionTextValidationViewModel.stateSbscribtion.countryData.countryCode.uppercase(),
                onCountryChange = {
                    subscribtionTextValidationViewModel.onSubscribtionEvent(
                        SubscribtionFormEvent.CountryDataChanged(it)
                    )
                },
                stringId = R.string.phone1_field_title,

                errorValue = subscribtionTextValidationViewModel.stateSbscribtion.phoneError,
                value = subscribtionTextValidationViewModel.stateSbscribtion.phone,
                hint = getNumberHint(subscribtionTextValidationViewModel.stateSbscribtion.countryData.countryCode),
                onValueChange = {
                    subscribtionTextValidationViewModel.onSubscribtionEvent(
                        SubscribtionFormEvent.PhoneNumberChanged(
                            it
                        )
                    )
                },
                imeAction = ImeAction.Done
            )

            Spacer(Modifier.height(12.dp))

            FilledTonalButton(
                onClick = {
                    authViewModel.onShowSubscribtionPeriodChange(true)
                }
            ) {
                Text(text = stringResource(R.string.subscribtion_for, state.subscriptionTime))
            }
            Spacer(Modifier.height(12.dp))

            //  LoadingAnimation()


            MultiChoiceSegmentedButtonRow(
                modifier = Modifier.fillMaxWidth().padding(start = 9.dp, end = 9.dp)
            ) {
                options.forEachIndexed { index, label ->
                    SegmentedButton(
                        shape = SegmentedButtonDefaults.itemShape(index = index, count = options.size),
                        icon = {
                            SegmentedButtonDefaults.Icon(active = index in checkedLicenceList) {
                                Icon(
                                    painter = icons[index],
                                    contentDescription = null,
                                    modifier = Modifier.size(SegmentedButtonDefaults.IconSize)
                                )
                            }
                        },
                        onCheckedChange = {
                            if (index in checkedLicenceList) {
                                authViewModel.onRemoveSubscribtionCheckedLicenceListChange(index)
                            } else {
                                authViewModel.onAddSubscribtionCheckedLicenceListChange(index)
                            }

                            if (label.contains(PRO_CAISSE_MOBILITY)) {
                                subscribtionTextValidationViewModel.onrequestProcaisseMobilityLicence(!requestProcaisseMobilityLicence)
                                subscribtionTextValidationViewModel.onSubscribtionEvent(
                                    SubscribtionFormEvent.ProductSelectedChanged(
                                        !requestProcaisseMobilityLicence || subscribtionTextValidationViewModel.requestProInventoryLicence
                                    )
                                )
                            } else if (label.contains(PRO_INVENTORY_AUTHORIZATION_TYPE_MENU)) {
                                subscribtionTextValidationViewModel.onrequestProInventoryLicence(!requestProInventoryLicence)
                                subscribtionTextValidationViewModel.onSubscribtionEvent(
                                    SubscribtionFormEvent.ProductSelectedChanged(
                                        !requestProInventoryLicence || requestProcaisseMobilityLicence
                                    )
                                )
                            }
                        },
                        checked = index in checkedLicenceList
                    ) {
                        Text(label)
                    }
                }
            }
        }
    }

}


