package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.remote.api

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.domaine.BaseConfigLicenseCheck
import com.asmtunis.procaisseinventory.core.model.DataResult
import kotlinx.coroutines.flow.Flow


interface CheckLicenceApi {



    suspend fun checkLicenses(idDevice: String): Flow<DataResult<BaseConfigLicenseCheck>>

}
