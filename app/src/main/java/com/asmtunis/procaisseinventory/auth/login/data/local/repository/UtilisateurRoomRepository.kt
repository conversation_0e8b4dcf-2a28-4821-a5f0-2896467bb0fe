package com.asmtunis.procaisseinventory.auth.login.data.local.repository

import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import kotlinx.coroutines.flow.Flow

interface UtilisateurRoomRepository {

    fun upsert(value: Utilisateur)
    fun upsertAll(value: List<Utilisateur>)
    fun deleteAll()
    fun getAll(): Flow<List<Utilisateur>?>
    fun getUser(): Flow<Utilisateur?>
}