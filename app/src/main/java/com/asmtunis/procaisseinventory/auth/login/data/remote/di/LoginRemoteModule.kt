package com.asmtunis.procaisseinventory.auth.login.data.remote.di

import com.asmtunis.procaisseinventory.auth.login.data.remote.api.LoginApi
import com.asmtunis.procaisseinventory.auth.login.data.remote.api.LoginApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object LoginRemoteModule {

        @Provides
        @Singleton
        fun provideLoginApi(client: HttpClient): LoginApi = LoginApiImpl(client)


    }