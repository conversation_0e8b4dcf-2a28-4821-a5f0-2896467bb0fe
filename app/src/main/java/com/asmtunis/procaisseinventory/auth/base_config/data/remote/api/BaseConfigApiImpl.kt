package com.asmtunis.procaisseinventory.auth.base_config.data.remote.api

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class BaseConfigApiImpl(private val client: HttpClient) : BaseConfigApi {


    override suspend fun getListBaseConfig(
        deviceId: String,
        product: String
    ): Flow<DataResult<List<BaseConfig>>> = flow {
        var headers = emptyMap<String, String>().toMutableMap()

        if (product == PRO_INVENTORY) {
            headers =
                mapOf("Application-name" to PRO_INVENTORY_AUTHORIZATION_TYPE_MENU).toMutableMap()
        }


        val formsData = mapOf(
            "id_device" to deviceId,
            "produit" to product
        )
         val result = executePostApiCall<List<BaseConfig>>(
             client = client,
             endpoint = Urls.SELECTION_BASE_CONFIG,
             headers = headers,
             formsData = formsData
         )
        emitAll(result)
    }
}