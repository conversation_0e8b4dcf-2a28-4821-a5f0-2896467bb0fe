package com.asmtunis.procaisseinventory.auth.login.data.remote.api

import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.Urls.INSERT_USER_TOKEN
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow

class LoginApiImpl(private val client: HttpClient): LoginApi {
    override suspend fun postLogin(
        baseConfig: String,
        header: String
    ): Flow<DataResult<Utilisateur>> = flow {

        val headers = mapOf("Application-name" to header)

        val result = executePostApiCall<Utilisateur>(
            client = client,
            endpoint = Urls.AUTHENTIFICATION,
            headers = headers,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getUtilisateurs(
        baseConfig: String,
        header: String
    ): Flow<DataResult<List<Utilisateur>>> = flow {

        val headers = mapOf("Application-name" to header)

        val result = executePostApiCall<List<Utilisateur>>(
            client = client,
            endpoint = Urls.GET_UTILISATEURS,
            headers = headers,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun insertUserToken(
        baseConfig: String,
        token: String
    ): Flow<DataResult<Boolean>> = flow {
        val result =  executePostApiCall<Boolean>(
            client = client,
            endpoint = "${INSERT_USER_TOKEN}$token",
            baseConfig = baseConfig
        )
        emitAll(result)
    }
}
