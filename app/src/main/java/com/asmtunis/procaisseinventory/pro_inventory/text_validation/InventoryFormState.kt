package com.asmtunis.procaisseinventory.pro_inventory.text_validation

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.data.station.domaine.Station


data class InventoryFormState(

    val typePiece: String = "",
    val typePieceError: UiText? = null,

    val numPiece: String = "",
    val numPieceError: UiText? = null,


    val fournisseur: Fournisseur =  Fournisseur(),
    val fournisseurError: UiText? = null,

    val station: Station = Station(),
    val stationError: UiText? = null,

    val stationDestination: Station = Station(),
    val stationDestinationError: UiText? = null,

    val listSelectedArticleInventory: List<SelectedArticle> = emptyList(),
    val listSelectedArticleError: UiText? = null,



    val quantite: String = "",
    val quantiteError: UiText? = null,

    val selectedArticle: Article = Article(),
    val selectedArticleError: UiText? = null,

    val prixAchatHT: String = "",
    val prixAchatHTError: UiText? = null,

    val tva: Tva = Tva(),
    val tvaError: UiText? = null,
)