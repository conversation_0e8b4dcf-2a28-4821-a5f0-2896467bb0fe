package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.TYPE_PRIX_UNITAIRE_HT_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import kotlinx.coroutines.flow.Flow


@Dao
interface TypePrixUnitaireHTDAO {
    @get:Query("SELECT * FROM $TYPE_PRIX_UNITAIRE_HT_TABLE")
    val all: Flow<List<TypePrixUnitaireHT>>

    @get:Query("SELECT Type_PrixUnitaireHT FROM $TYPE_PRIX_UNITAIRE_HT_TABLE")
    val list: Flow<List<String>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<TypePrixUnitaireHT>)

    @Query("DELETE FROM $TYPE_PRIX_UNITAIRE_HT_TABLE")
    fun deleteAll()
}
