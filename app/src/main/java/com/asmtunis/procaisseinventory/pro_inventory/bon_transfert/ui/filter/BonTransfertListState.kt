package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui.filter

import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraisonWithArticle
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch


data class BonTransfertListState(
    val lists: Map<BonLivraison, List<LigneBonLivraisonWithArticle>> = emptyMap(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch(),
    val filterByStationSource: String = "",
    val filterByStationDestination: String = "",
    val filterByEtatBnTransfert: String = ""
)