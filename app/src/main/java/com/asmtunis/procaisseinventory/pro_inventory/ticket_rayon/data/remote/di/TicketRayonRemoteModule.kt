package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.remote.di

import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.remote.api.TicketRayonApi
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.remote.api.TicketRayonApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object TicketRayonRemoteModule {

        @Provides
        @Singleton
        fun provideTicketRayonApi(client: HttpClient): TicketRayonApi = TicketRayonApiImpl(client)

    }