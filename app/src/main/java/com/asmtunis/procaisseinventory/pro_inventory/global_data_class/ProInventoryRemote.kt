package com.asmtunis.procaisseinventory.pro_inventory.global_data_class

import com.asmtunis.procaisseinventory.data.famille.remote.api.FamilleApi
import com.asmtunis.procaisseinventory.data.marque.remote.api.MarqueApi
import com.asmtunis.procaisseinventory.data.station.remote.api.StationApi
import com.asmtunis.procaisseinventory.data.tva.remote.api.TvaApi
import com.asmtunis.procaisseinventory.data.unite.remote.api.UniteApi
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.bn_entree.BonEntreeApi
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.ligne_bn_entree.LigneBonEntreeApi
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.bn_livraison.BonLivraisonApi
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.ligne_bn_livraison.LigneBonLivraisonApi
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.remote.api.FournisseurApi
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.remote.api.TypePrixApi
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.inv.InventaireApi
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.ligne_inv.LigneInventaireApi
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.remote.api.TicketRayonApi
import javax.inject.Inject


data class ProInventoryRemote  @Inject constructor(
    val stations: StationApi,
    val typePrix: TypePrixApi,
    val tva: TvaApi,
    val unite: UniteApi,
    val famille: FamilleApi,
    val fournisseur: FournisseurApi,
    val marque: MarqueApi,
    val bonEntree: BonEntreeApi,
    val ligneBonEntree: LigneBonEntreeApi,
    val bonLivraison: BonLivraisonApi,
    val ligneBonLivraison: LigneBonLivraisonApi,
    val inventaire: InventaireApi,
    val ligneInventaire: LigneInventaireApi,
    val ticketRayon: TicketRayonApi,
    )