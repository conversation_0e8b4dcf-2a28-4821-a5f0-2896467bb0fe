package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProInventoryConstants.TYPE_PRIX_UNITAIRE_HT_TABLE)
@Serializable
data class TypePrixUnitaireHT (
    @PrimaryKey
    @ColumnInfo(name = "Type_PrixUnitaireHT")
    @SerialName("Type_PrixUnitaireHT")
    var type_PrixUnitaireHT : String = "",

    @ColumnInfo(name = "Type_user")
    @SerialName("Type_user")
    var type_user: String? = "",

    @ColumnInfo(name = "Type_station")
    @SerialName("Type_station")
    var type_station: String? = "",

    @ColumnInfo(name = "Type_export")
    @SerialName("Type_export")
    var type_export: String? = "",

    @ColumnInfo(name = "Type_DDm")
    @SerialName("Type_DDm")
    var type_DDm: String? = ""

)