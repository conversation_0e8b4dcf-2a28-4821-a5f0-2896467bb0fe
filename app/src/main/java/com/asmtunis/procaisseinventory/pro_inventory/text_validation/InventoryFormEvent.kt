package com.asmtunis.procaisseinventory.pro_inventory.text_validation

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.data.station.domaine.Station


sealed class InventoryFormEvent {

    data class typePieceChanged(val typePiece: String) : InventoryFormEvent()
    data class numPieceChanged(val numPiece: String) : InventoryFormEvent()

    data class fournisseurChanged(val fournisseur: Fournisseur) : InventoryFormEvent()
    data class stationChanged(val station: Station) : InventoryFormEvent()
    data class stationDestinationChanged(val stationDestination: Station) : InventoryFormEvent()
    data class selectedListArticleChanged(val listLigneBonEntree: List<SelectedArticle>) : InventoryFormEvent()
    data class quantiteChanged(val quantite: String) : InventoryFormEvent()
    data class prixAchatHTChanged(val prixAchatHT: String) : InventoryFormEvent()
    data class tvaChanged(val tva: Tva) : InventoryFormEvent()
    data class selectedArticleChanged(val article: Article) : InventoryFormEvent()

    data object SubmitAchat : InventoryFormEvent()
    data object SubmitBonTransfert : InventoryFormEvent()
    data object SubmitInventaire : InventoryFormEvent()
    data object SubmitAddNewLigneBnTransfert : InventoryFormEvent()
    data object SubmitAddNewLigneAchat : InventoryFormEvent()
    data object SubmitAddNewLigneInventaire : InventoryFormEvent()


}