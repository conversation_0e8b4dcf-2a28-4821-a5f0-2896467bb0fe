package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.view_model
import androidx.lifecycle.viewModelScope
import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class BonTransfertPrintViewModel @Inject constructor(
    private val sunmiPrintManager: SunmiPrintManager
) : ViewModel() {

    private val _printStatus = MutableStateFlow<String?>(null)
    val printStatus: StateFlow<String?> = _printStatus.asStateFlow()

    var isPrinting by mutableStateOf(false)
        private set

    /**
     * Print a bon transfert using the SUNMI printer
     */
    fun printBonTransfertSunmi(
        context: Context,
        bonTransfert: BonLivraison,
        lgBonTransfert: List<LigneBonLivraison>,
        articleMapByBarCode: Map<String, Article>,
        stationSource: Station,
        stationDestination: Station,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        viewModelScope.launch {
            isPrinting = true
            try {
                sunmiPrintManager.printBonTransfert(
                    context = context,
                    bonTransfert = bonTransfert,
                    lgBonTransfert = lgBonTransfert,
                    articleMapByBarCode = articleMapByBarCode,
                    stationSource = stationSource,
                    stationDestination = stationDestination,
                    utilisateur = utilisateur,
                    printParams = printParams
                )
                _printStatus.value = "Printing bon transfert..."
            } catch (e: Exception) {
                _printStatus.value = "Error: ${e.message}"
            } finally {
                isPrinting = false
            }
        }
    }

    fun clearPrintStatus() {
        _printStatus.value = null
    }
}
