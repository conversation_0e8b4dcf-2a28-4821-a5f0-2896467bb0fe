package com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class FournisseurApiImpl(private val client: HttpClient) : FournisseurApi {
    override suspend fun getFournisseurs(baseConfig: String): Flow<DataResult<List<Fournisseur>>> = flow {
        val result = executePostApiCall<List<Fournisseur>>(
            client = client,
            endpoint = Urls.GET_FOURNISSEURS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }