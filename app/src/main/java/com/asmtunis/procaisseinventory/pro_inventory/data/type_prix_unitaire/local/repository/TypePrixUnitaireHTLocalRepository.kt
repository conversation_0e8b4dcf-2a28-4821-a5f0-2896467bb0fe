package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.repository

import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import kotlinx.coroutines.flow.Flow


interface TypePrixUnitaireHTLocalRepository {
    fun upsertAll(value: List<TypePrixUnitaireHT>)
    fun deleteAll()

    fun getAll(): Flow<List<TypePrixUnitaireHT>>

}