package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.AddTicketRayonItem
import kotlinx.coroutines.flow.Flow


interface TicketRayonApi {

    suspend fun addTicketRayon(baseConfig: String): Flow<DataResult<List<AddTicketRayonItem>>>

}