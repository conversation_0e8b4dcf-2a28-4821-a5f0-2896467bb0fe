package com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.setSelectedArticleNoCalcul
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.updateQteStationFromDB
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.SelectArticlesNoCalculRoute
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.InventaireViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ui.AddNewProductDialogue
import com.asmtunis.procaisseinventory.pro_inventory.utils.SelectedArticle.addNewLigneSelectedInventoryArtcle
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.*
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableHeader
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.four_column.FourColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.date_time_picker.DatePickerView
import com.simapps.ui_kit.date_time_picker.TimePickerView
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InventaireAddScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    inventaireViewModel: InventaireViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    selectArtNoCalculVM: SelectArticleNoCalculViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel
) {

    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val station = inventaireViewModel.station
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val parametrage = mainViewModel.parametrage

    val inventaire = inventaireViewModel.selectedInventaire
    val ligneInventaire = inventaireViewModel.selectedListLgInventaire
    val controlQuantity = selectArtNoCalculVM.controlQuantity
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val utilisateur = mainViewModel.utilisateur
    val isAutoScanMode = mainViewModel.isAutoScanMode
    val stationStockArticlMapByBarCode = mainViewModel.stationStockArticlMapByBarCode

    val selectedArticleInventory = selectArtNoCalculVM.selectedArticleInventory
    var stationList = mainViewModel.stationList

    val hasPromo = mainViewModel.hasPromo(stationList.firstOrNull { it.sTATCode == utilisateur.Station })


    val selectedArticleInventoryList = selectArtNoCalculVM.selectedArticleInventoryList

    LaunchedEffect(key1 = Unit) {
        if (!utilisateur.typeUser.lowercase().contains(Globals.ADMIN)) {
            stationList = stationList.filter { it.sTATCode == utilisateur.Station }

            inventaireViewModel.onSelectedStationChange(stationList.first())

        }
    }


    val codeM = mainViewModel.codeM

    val barCodeInfo = barCodeViewModel.barCodeInfo
     LaunchedEffect(key1 = barCodeInfo.value) {
        //TODO SEE WHY THIS BLOCK EXECUTE WHEN ROTATE SCREEN THEN REMOVE CHECH IF == ""
        if (barCodeInfo.value == "") return@LaunchedEffect
        val scannedArticle = articleMapByBarCode.values.firstOrNull{ it.aRTCodeBar == barCodeInfo.value}

        if (scannedArticle == null) {
            selectArtNoCalculVM.setSelectedArticlInventory(SelectedArticle())

            showToast(
                context = context,
                toaster = toaster,
                message = barCodeInfo.value + "\n" + context.resources.getString(R.string.introuvable),
                type = ToastType.Info,
            )
            return@LaunchedEffect
        }
        selectArtNoCalculVM.setControlQte(false)


        setSelectedArticleNoCalcul(
            scannedArticle = scannedArticle,
            stationOrigineCode = station.sTATCode,
            selectedArticleInventoryList = selectedArticleInventoryList,
            stationStockArticlMapByBarCode = stationStockArticlMapByBarCode,
            setSelectedArticlInventory = { value: SelectedArticle, from: String ->
                selectArtNoCalculVM.setSelectedArticlInventory(value = value, from = from)
            }
        )
       val selectedArtInventory = selectArtNoCalculVM.selectedArticleInventory

        if (!isAutoScanMode) {
            mainViewModel.setAddNewProductDialogueVisibility(true)
            return@LaunchedEffect
        }

        addNewLigneSelectedInventoryArtcle(
            toaster = toaster,
            context = context,
            station = station,
            stationStockArticl = stationStockArticlMapByBarCode,
            selectedArticle = selectedArtInventory,
            setSelectedArticlInventory = { selectArtNoCalculVM.setSelectedArticlInventory(it) },
            addItemToSelectedArticleInventoryList = { selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it) },
            resetBarCode = { barCodeViewModel.onBarCodeInfo(barCode = BareCode()) },
            isAutoScanMode = true,
            tva = if (selectedArtInventory.tva == Tva()) mainViewModel.tvaList.first()
            else selectedArtInventory.tva,
            controlQuantity = controlQuantity
        )

        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
    }





    if (mainViewModel.showDatePicker) {
        DatePickerView(
            setDateVisibility = { mainViewModel.onShowDatePickerChange(it) },
            onSelectedDateChange = {
                mainViewModel.onSelectedDateChange(it)
                mainViewModel.onShowTimePickerChange(true)
            },
            confirmText = stringResource(id = R.string.confirm),
            cancelText = stringResource(id = R.string.cancel)
        )
    }



    if (mainViewModel.showTimePicker) {
        TimePickerView(
            setTimeVisibility = {
                mainViewModel.onShowTimePickerChange(it)
            },
            onSelectedTimeChange = {
                mainViewModel.onSelectedTimeChange(it)
            },
            confirmText = stringResource(id = R.string.confirm),
            cancelText = stringResource(id = R.string.cancel)
        )
    }

    if (mainViewModel.openAddNewProductDialogue) {
        AddNewProductDialogue(
            showTvaMenu = false,
            prixLabel = stringResource(R.string.price_title),
            canModify = true,
            station = station,
            selectedArticleInventory = selectedArticleInventory,
            tvaExpand = mainViewModel.tvaExpand,
            tvaList = mainViewModel.tvaList,
            onTvaExpandedChange = { mainViewModel.onTvaExpandedChange(it) },
            setSelectedArticlInventory = { selectArtNoCalculVM.setSelectedArticlInventory(it) },
            onDismiss = {
                barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                mainViewModel.setAddNewProductDialogueVisibility(false)
            },
            onConfirm = {
                addNewLigneSelectedInventoryArtcle(
                    toaster = toaster,
                    context = context,
                    fromScan = false,
                    selectedArticle = selectedArticleInventory,
                    setSelectedArticlInventory = {
                        selectArtNoCalculVM.setSelectedArticlInventory(it)
                    },
                    addItemToSelectedArticleInventoryList = {
                        //  Log.d("rdgdfgdsggd", "addItemToSelectedArticleInventoryList "+ it.quantity)
                        selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)
                    },
                    resetBarCode = { barCodeViewModel.onBarCodeInfo(barCode = BareCode()) },
                    isAutoScanMode = isAutoScanMode,
                    tva = if (selectedArticleInventory.tva == Tva()) mainViewModel.tvaList.first()
                    else selectedArticleInventory.tva,
                    controlQuantity = controlQuantity,
                    station = station,
                    stationStockArticl = stationStockArticlMapByBarCode,
                )

                barCodeViewModel.onBarCodeInfo(barCode = BareCode())

                mainViewModel.setAddNewProductDialogueVisibility(false)
            }
        )
    }


    CustomAlertDialogue(
        title = stringResource(id = R.string.voulez_vous),
        msg = "",
        openDialog = inventaireViewModel.showSaveDialogue,
        setDialogueVisibility = {
            inventaireViewModel.onShowSaveDialogueChange(it)
        },
        customAction = {


            showToast(
                context = context,
                toaster = toaster,
                message = context.getString(R.string.cd_add) + "\n" + context.getString(
                    R.string.sucess_add_inventaire
                ),
                type = ToastType.Success,
            )
            inventaireViewModel.saveInventaire(
                hasPromo = hasPromo,
                status = ItemStatus.INSERTED.status,
                inventaireGeneratedNum = codeM,
                utilisateur = utilisateur,
                selectedArticleInventoryList = selectedArticleInventoryList,
                navigateUp = {
                    mainViewModel.resetTimePicker()
                    popBackStack()
                },
            )
        },
        confirmText = stringResource(id = R.string.save_and_sync),
        cancelText = stringResource(id = R.string.save),
        negatifAction = {
            inventaireViewModel.saveInventaire(
                hasPromo = hasPromo,
                status = ItemStatus.WAITING.status,
                inventaireGeneratedNum = codeM,
                utilisateur = utilisateur,
                selectedArticleInventoryList = selectedArticleInventoryList,
                navigateUp = {
                    mainViewModel.resetTimePicker()
                    popBackStack()
                },
            )

        }
    )

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,
                onNavigationClick = {
                    mainViewModel.onShowDismissScreenAlertDialogChange(true)
                },
            )
        },
        bottomBar = {
            AnimatedVisibility(
                visible = station == Station(),
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.you_must_select_station),
                    color = MaterialTheme.colorScheme.error,
                    textAlign = TextAlign.Center
                )
            }
            AnimatedVisibility(
                visible = station != Station(),
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                AddViewBottomAppBar(
                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                    toaster = toaster,
                    showAddArticleBtn = station != Station(),
                    showBareCodeScannerBtn = barCodeViewModel.haveCameraDevice,
                    showSaveBtn = selectedArticleInventoryList.isNotEmpty(),

                    onSaveClick = { inventaireViewModel.onShowSaveDialogueChange(true) },
                    onClickAddArticle = {
                        mainViewModel.setAddNewProductDialogueVisibility(false)
                        selectArtNoCalculVM.setControlQte(false)
                        selectArtNoCalculVM.setSelectedArticlInventory(selectedArticleInventory)

                        navigate(SelectArticlesNoCalculRoute(stationOrigineCode = station.sTATCode))
                    },
                    isAutoScanMode = isAutoScanMode,
                    setAutoScanMode = {
                        mainViewModel.setAutoAddMode(!isAutoScanMode)
                    },
                    openBareCodeScanner = {
                        openBareCodeScanner(
                            navigate = { navigate(it) },
                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                        )
                    }
                    )
            }
        }


    ) { padding ->

        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                popBackStack()
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )
        CustomAlertDialogue(
            title = context.getString(R.string.delete),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = mainViewModel.showAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowAlertDialogChange(it)
            },
            customAction = {
                selectArtNoCalculVM.deleteItemToSelectedArticleInventoryList(
                    selectedArticleInventory.article
                )
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {





            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(0.95f),
                designation = station.sTATDesg,
                errorValue = inventaireViewModel.stationError?.asString(),
                label = stringResource(R.string.station),
                readOnly = true,
                itemExpanded = mainViewModel.stationExpand,
                itemList = stationList,
                selectedItem = station,
                getItemDesignation = { it.sTATDesg },
                getItemTrailing = { it.sTATCode },
                onItemExpandedChange = { mainViewModel.onStationExpandedChange(true) },
                onClick = {
                    inventaireViewModel.onSelectedStationChange(it)


                    updateQteStationFromDB(
                        stationCode = it.sTATCode,
                        stationStockArticl = stationStockArticlMapByBarCode,
                        selectedArticleList = selectedArticleInventoryList,
                        addItemToSelectedArticleInventoryList = { selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)},
                    )
                    mainViewModel.onStationExpandedChange(false)
                },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }
            )
            Spacer(modifier = Modifier.height(12.dp))

            TableHeader(
                onClickShowCalendar = {
                    mainViewModel.onShowDatePickerChange(true)
                },
                date = inventaireViewModel.selectedInventaire.iNVDateFormatted,
                canModify = true,
                selectedDateTime = mainViewModel.getSelectedDateTime()
            )


            Spacer(modifier = Modifier.height(20.dp))


            FourColumnTable(
                canModify = true,
                rowTitls  = context.resources.getStringArray(R.array.threeColumnTable_inventaire_array).toList(),
                onSwipeToDelete = { selectArtNoCalculVM.deleteItemToSelectedArticleInventoryList(it.article) },
                selectedListArticle = selectedArticleInventoryList,
                onTap = {
                    selectArtNoCalculVM.setSelectedArticlInventory(it)
                    mainViewModel.setAddNewProductDialogueVisibility(true)
                },
                onLongPress = {},
                firstColumn = { item ->
                    TableTextUtils.firstColumn(
                        selectedArticle = item,
                        parametrage = parametrage,
                        articleMapByBarCode = articleMapByBarCode
                    )
                },
                secondColumn = { StringUtils.removeTrailingZeroInDouble(it.qteStationFromDB).ifEmpty { "0" } },
                thirdColumn = { StringUtils.removeTrailingZeroInDouble(it.quantity).ifEmpty { "0" } },
                infoText = { context.getString(R.string.qte_in_all_stations, StringUtils.removeTrailingZeroInDouble(it.article.aRTQteStock).ifEmpty { "0" }) + " "+ it.unite.uNIDesignation }
            )

            /*   if (state.listSelectedArticleError != null && selectedArticleInventoryList.isEmpty())
                   Text(
                       text = state.listSelectedArticleError,
                       color = MaterialTheme.colorScheme.error,
                       fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                       // modifier = Modifier.align(Alignment.Start)
                   )
*/

        }


    }


}
