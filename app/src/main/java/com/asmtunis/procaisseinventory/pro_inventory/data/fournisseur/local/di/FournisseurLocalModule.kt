package com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.dao.FournisseurDAO
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.repository.FournisseurLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.repository.FournisseurLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class FournisseurLocalModule {

    @Provides
    @Singleton
    fun provideFournisseurDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.fournisseurDAO()

    @Provides
    @Singleton
    @Named("Fournisseur")
    fun provideFournisseurRepository(
        fournisseurDAO: FournisseurDAO
    ): FournisseurLocalRepository = FournisseurLocalRepositoryImpl(
        fournisseurDAO = fournisseurDAO
    )

}