package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.local.ligne_inv.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.LIGNE_INVENTAIRE_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneInventaireDAO {
    @get:Query("SELECT * FROM $LIGNE_INVENTAIRE_TABLE where Status<>'DELETED'")
    val all: Flow<List<LigneInventaire>>

    @get:Query("SELECT * FROM $LIGNE_INVENTAIRE_TABLE")
    val everything: Flow<List<LigneInventaire>>

    @Query("SELECT count(*) FROM $LIGNE_INVENTAIRE_TABLE where LG_INV_Code_Inv=:myInv")
    fun countLignes(myInv: String): Flow<String>

    @Query("SELECT * FROM $LIGNE_INVENTAIRE_TABLE WHERE isSync=0 and  (Status=:status)")
    fun getNonSync(status: String): Flow<List<LigneInventaire>>

    @Query("SELECT * FROM $LIGNE_INVENTAIRE_TABLE WHERE   (Status=:status)")
    fun getByStatusForced(status: String): Flow<List<LigneInventaire>>

    @Query("SELECT * FROM $LIGNE_INVENTAIRE_TABLE where LG_INV_Code_Inv=:code and LG_INV_Station =:station")
    fun getByCodeAndStation(code: String, station: String): Flow<List<LigneInventaire>>

    @Query("SELECT * FROM $LIGNE_INVENTAIRE_TABLE where LG_INV_Code_Inv=:code")
    fun getByCode(code: String): Flow<List<LigneInventaire>>


    @Query("SELECT ifnull(MAX(cast(substr(LG_INV_Code_Inv,length(:prefix) + 1 ,length('LG_INV_Code_Inv'))as integer)),0)+1 FROM   $LIGNE_INVENTAIRE_TABLE WHERE substr(LG_INV_Code_Inv, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>

    @Query(
        "SELECT COUNT(*) FROM $LIGNE_INVENTAIRE_TABLE where  isSync=0 and" +
                "(Status='INSERTED'  or Status='UPDATED' or Status='DELETED')"
    )
    fun count(): Flow<Int>

    @Query("SELECT COUNT(*) FROM $LIGNE_INVENTAIRE_TABLE where LG_INV_Code_Inv =:codeInv")
    fun countByCode(codeInv: String):Flow<Int>

    @get:Query("SELECT * FROM $LIGNE_INVENTAIRE_TABLE LIMIT 1")
    val one: LigneInventaire

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: LigneInventaire)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<LigneInventaire>)

    @Query("DELETE FROM $LIGNE_INVENTAIRE_TABLE")
    fun deleteAll()

    @Delete
    fun deleteList(ligneInventaireList: List<LigneInventaire>)

    @Query("DELETE FROM $LIGNE_INVENTAIRE_TABLE where isSync=1")
    fun deleteOnlyOnline()

    @Query("DELETE FROM $LIGNE_INVENTAIRE_TABLE where LG_INV_Code_Inv=:code_inv and LG_INV_Code_Article=:code_article")
    fun delete(code_inv: String?, code_article: String)

    @Query("DELETE FROM $LIGNE_INVENTAIRE_TABLE where LG_INV_Code_Inv=:code_inv  ")
    fun deleteByInvCode(code_inv: String)


    @Query("delete  from $LIGNE_INVENTAIRE_TABLE where LG_INV_Code_Inv=:num  and  LG_INV_Code_Article= :codeArticle")
    fun deleteByCodeAndCodeArticle(num: String, codeArticle : String)

    @Query("UPDATE $LIGNE_INVENTAIRE_TABLE SET Status = 'SELECTED', IsSync = 1, LG_INV_Code_Inv = :code, LG_INV_EtatLigne = :etat where LG_INV_Code_Inv = :codeLocal")
    fun updateStatus(code: String, codeLocal : String, etat: String)


    @Query("UPDATE $LIGNE_INVENTAIRE_TABLE SET Status= 'INSERTED' where LG_INV_Code_Inv = :codeM")
    fun setToInserted(codeM: String)

    @Delete
    fun delete(ligneInventaire: LigneInventaire)
}
