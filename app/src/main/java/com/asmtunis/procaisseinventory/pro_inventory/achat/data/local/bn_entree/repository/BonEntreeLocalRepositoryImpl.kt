package com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.repository

import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.dao.BonEntreeDAO
import kotlinx.coroutines.flow.Flow


class BonEntreeLocalRepositoryImpl(
        private val bonEntreeDAO: BonEntreeDAO
    ) : BonEntreeLocalRepository {
    override fun upsertAll(value: List<BonEntree>) = bonEntreeDAO.insertAll(value)

    override fun upsert(value: BonEntree) = bonEntreeDAO.insert(value)

    override fun deleteAll() = bonEntreeDAO.deleteAll()
    override fun delete(bonEntree: BonEntree) = bonEntreeDAO.delete(bonEntree)

    override fun getAll(): Flow<List<BonEntree>> = bonEntreeDAO.all
    override fun count(): Flow<Int> = bonEntreeDAO.count()

    override fun getNotSync(): Flow<List<BonEntree>> = bonEntreeDAO.nonSync
    override fun noSynced(): Flow<Map<BonEntree, List<LigneBonEntree>>> = bonEntreeDAO.noSynced()

    override fun getNewCode(prefix: String): Flow<String> = bonEntreeDAO.getNewCode(prefix)
    override fun updateBonEntreeStatus(bonEntNum: String, bonEntNumM: String, exercice: String) = bonEntreeDAO.updateBonEntreeStatus(bonEntNum = bonEntNum, bonEntNumM = bonEntNumM, exercice = exercice)

    override fun getAllFiltred(
        isAsc: Int,
        filterByStationEntree: String,
        filterByFournisseur: String,
        sortBy: String
    ): Flow<Map<BonEntree, List<LigneBonEntree>>> = bonEntreeDAO.getAllFiltred(
        isAsc = isAsc,
        filterByStationEntree = filterByStationEntree,
        filterByFournisseur= filterByFournisseur,
        sortBy = sortBy
    )

     override fun filterByBonEntNum(
        searchString: String,
        filterByStationEntree: String,
        filterByFournisseur: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<BonEntree, List<LigneBonEntree>>> = bonEntreeDAO.filterByBonEntNum(
        searchString = searchString,
        filterByStationEntree = filterByStationEntree,
         filterByFournisseur= filterByFournisseur,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun filterByBonEntNumPiece(
        searchString: String,
        filterByStationEntree: String,
        filterByFournisseur: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<BonEntree, List<LigneBonEntree>>> = bonEntreeDAO.filterByBonEntNumPiece(
        searchString = searchString,
        filterByStationEntree = filterByStationEntree,
        filterByFournisseur= filterByFournisseur,
        sortBy = sortBy,
        isAsc = isAsc
    )

    override fun filterByBonEntCodeFrs(
        searchString: String,
        filterByStationEntree: String,
        filterByFournisseur: String,
        sortBy: String,
        isAsc: Int
    ): Flow<Map<BonEntree, List<LigneBonEntree>>> = bonEntreeDAO.filterByBonEntCodeFrs(
        searchString = searchString,
        filterByStationEntree = filterByStationEntree,
        filterByFournisseur= filterByFournisseur,
        sortBy = sortBy,
        isAsc = isAsc
    )
}