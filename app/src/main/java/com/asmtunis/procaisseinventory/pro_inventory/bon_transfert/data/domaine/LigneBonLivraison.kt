package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProInventoryConstants.LIGNE_BON_LIVRAISON_TABLE,
  /*  primaryKeys = ["LG_BonTrans_NumBon", "LG_BonTrans_CodeArt", "LG_BonTrans_Exerc"]

   */
)
@Serializable
data class LigneBonLivraison(
   @PrimaryKey(autoGenerate = true)
  @Transient
   val id: Long = 0,

    @ColumnInfo(name = "LG_BonTrans_NumBon")
    @SerialName("LG_BonTrans_NumBon")
    var lGBonTransNumBon: String = "",

    @ColumnInfo(name = "LG_BonTrans_CodeArt")
    @SerialName("LG_BonTrans_CodeArt")
    var lGBonTransCodeArt: String = "",

    @ColumnInfo(name = "LG_BonTrans_Exerc")
    @SerialName("LG_BonTrans_Exerc")
    var lGBonTransExerc: String = "",

    @ColumnInfo(name = "Qte_transfert")
    @SerialName("Qte_transfert")
    var qteTransfert: Double = -1.1,

    @ColumnInfo(name = "QteDecTransferer")
    @SerialName("QteDecTransferer")
    var qteDecTransferer: String? = "",

    @ColumnInfo(name = "LG_BonTrans_Unite")
    @SerialName("LG_BonTrans_Unite")
    var lGBonTransUnite: String? = "",

    @ColumnInfo(name = "LG_BonTrans_PUHT")
    @SerialName("LG_BonTrans_PUHT")
    var lGBonTransPUHT: String? = "",

    @ColumnInfo(name = "LG_BonTrans_SYNC")
    @SerialName("LG_BonTrans_SYNC")
    var lGBonTransSYNC: String? = "",

    @ColumnInfo(name = "LG_BonTrans_Stat")
    @SerialName("LG_BonTrans_Stat")
    var lGBonTransStat: String? = "",

    @ColumnInfo(name = "LG_BonTrans_User")
    @SerialName("LG_BonTrans_User")
    var lGBonTransUser: String? = "",

    @ColumnInfo(name = "LG_BonTrans_export")
    @SerialName("LG_BonTrans_export")
    var lGBonTransExport: String? = "",

    @ColumnInfo(name = "LG_BonTrans_DDm")
    @SerialName("LG_BonTrans_DDm")
    var lGBonTransDDm: String? = "",

    @ColumnInfo(name = "LIG_BonEntree_QtePiece")
    @SerialName("LIG_BonEntree_QtePiece")
    var lIGBonEntreeQtePiece: String? = "",

    @ColumnInfo(name = "LG_ETAT")
    @SerialName("LG_ETAT")
    var lGETAT: String? = "",

    @ColumnInfo(name = "export")
    @SerialName("export")
    var export: String? = "",

    @ColumnInfo(name = "DDm")
    @SerialName("DDm")
    var dDm: String? = "",

    @ColumnInfo(name = "LG_BonTrans_PACHATNHT")
    @SerialName("LG_BonTrans_PACHATNHT")
    var lGBonTransPACHATNHT: String? = "",

    @ColumnInfo(name = "LG_BonTrans_TVA")
    @SerialName("LG_BonTrans_TVA")
    var lGBonTransTVA: String? = "",

    @ColumnInfo(name = "LG_BonTrans_MNTHT")
    @SerialName("LG_BonTrans_MNTHT")
    var lGBonTransMNTHT: String? = "",

    @ColumnInfo(name = "LG_BonTrans_MNTTC")
    @SerialName("LG_BonTrans_MNTTC")
    var lGBonTransMNTTC: String? = "",

    @ColumnInfo(name = "LG_BonTrans_StatDest")
    @SerialName("LG_BonTrans_StatDest")
    var lGBonTransStatDest: String? = "",

    @ColumnInfo(name = "LG_BonTrans_NumOrdre")
    @SerialName("LG_BonTrans_NumOrdre")
    var LG_BonTrans_NumOrdre: Int = -1,
) : BaseModel()
