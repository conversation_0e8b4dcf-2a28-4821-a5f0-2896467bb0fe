package com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.repository

import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.domaine.TicketRayon
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.data.local.dao.TicketRayonDAO
import kotlinx.coroutines.flow.Flow


class TicketRayonLocalRepositoryImpl(
        private val ticketRayonDAO: TicketRayonDAO
    ) : TicketRayonLocalRepository {
    override fun upsertAll(value: List<TicketRayon>) = ticketRayonDAO.insertAll(value)

    override fun upsert(value: TicketRayon)  = ticketRayonDAO.insert(value)

    override fun deleteAll()  = ticketRayonDAO.deleteAll()

    override fun delete(value: TicketRayon)  = ticketRayonDAO.delete(value)

    override fun getAll(): Flow<List<TicketRayon>>  = ticketRayonDAO.all
    override fun notSync(): Flow<List<TicketRayon>> = ticketRayonDAO.nonSync
    override fun updateSyncTicketRayon(code: String)  = ticketRayonDAO.updateSyncTicketRayon(code)


}