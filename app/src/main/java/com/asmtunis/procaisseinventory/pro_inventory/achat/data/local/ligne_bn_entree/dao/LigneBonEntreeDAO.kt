package com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.LIGNE_BON_ENTREE_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneBonEntreeDAO {
    @get:Query("SELECT * FROM $LIGNE_BON_ENTREE_TABLE where Status<>'DELETED'")
    val all: Flow<List<LigneBonEntree>>

    @Query("SELECT * FROM $LIGNE_BON_ENTREE_TABLE WHERE isSync=0 and (Status=:status)")
    fun getByStatus(status: String): Flow<List<LigneBonEntree>>

    @Query("SELECT * FROM $LIGNE_BON_ENTREE_TABLE WHERE (Status=:status)")
    fun getByStatusForced(status: String): Flow<List<LigneBonEntree>>

    @Query("SELECT * FROM $LIGNE_BON_ENTREE_TABLE where LIG_BonEntree_NumBon=:num and LIG_BonEntree_Exerc=:exerce")
    fun getByNumBe(num: String, exerce: String): Flow<List<LigneBonEntree>>

    @Delete
    fun delete(ligneBonEntree: LigneBonEntree)

    @Delete
    fun deleteAllList(listLigneBE: List<LigneBonEntree>)

    @Query("DELETE FROM $LIGNE_BON_ENTREE_TABLE where LIG_BonEntree_NumBon=:code and LIG_BonEntree_Exerc=:exercice")
    fun deleteByBeCode(code: String, exercice: String)

    @Query("DELETE FROM $LIGNE_BON_ENTREE_TABLE where LIG_BonEntree_NumBon=:code and LIG_BonEntree_Exerc=:exercice and LIG_BonEntree_CodeArt=:codeBare")
    fun deleteByBeCodeAndCodeBar(code: String, exercice: String, codeBare : String)
    @Query("SELECT COUNT(*) FROM $LIGNE_BON_ENTREE_TABLE where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='DELETED')")
    fun count(): Flow<Int>

    @Query("SELECT count(*) FROM $LIGNE_BON_ENTREE_TABLE where LIG_BonEntree_NumBon=:myBon")
    fun countLignes(myBon: String): Flow<String>

    @get:Query("SELECT * FROM $LIGNE_BON_ENTREE_TABLE LIMIT 1")
    val one: Flow<LigneBonEntree>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: LigneBonEntree)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<LigneBonEntree>)

    @Query("DELETE FROM $LIGNE_BON_ENTREE_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $LIGNE_BON_ENTREE_TABLE where LIG_BonEntree_NumBon=:num and LIG_BonEntree_CodeArt =:codeArticle and LIG_BonEntree_Exerc=:exercice ")
    fun delete(num: String, codeArticle: String, exercice: String)


    @Query("UPDATE $LIGNE_BON_ENTREE_TABLE SET Status = 'SELECTED', IsSync = 1, LIG_BonEntree_NumBon= :bonEntNum WHERE  LIG_BonEntree_NumBon_M= :bonEntNumM and LIG_BonEntree_Exerc=:exercice")
    fun updateLigneBonEntreeStatus(bonEntNum: String, bonEntNumM: String, exercice: String)
}
