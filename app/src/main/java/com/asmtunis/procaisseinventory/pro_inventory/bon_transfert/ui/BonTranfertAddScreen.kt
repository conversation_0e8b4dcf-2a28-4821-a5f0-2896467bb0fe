package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.EtatBonTransfert
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.SelectArticlesNoCalculRoute
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.BonTransfertViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ui.AddNewProductDialogue
import com.asmtunis.procaisseinventory.pro_inventory.utils.SelectedArticle.addNewLigneSelectedInventoryArtcle
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AddViewBottomAppBar
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils
import com.asmtunis.procaisseinventory.shared_ui_components.tables.four_column.FourColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu


@Composable
fun BonTranfertAddScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    bonTransfertViewModel: BonTransfertViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    selectArtNoCalculVM : SelectArticleNoCalculViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    settingViewModel: SettingViewModel
) {


    val context = LocalContext.current
    val bonTransfert = bonTransfertViewModel.selectedBonTransfert
    val ligneBonTransfert = bonTransfertViewModel.selectedListLgBonTransfert

    val etatBonTransfert = bonTransfertViewModel.etatBonTransfert
    val station = bonTransfertViewModel.station
    val stationDestination = bonTransfertViewModel.stationDestination
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val parametrage =mainViewModel.parametrage

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val selectedArticleInventoryList = selectArtNoCalculVM.selectedArticleInventoryList

    val stationStockArticlMapByBarCode = mainViewModel.stationStockArticlMapByBarCode

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

    val selectedArticleInventory = selectArtNoCalculVM.selectedArticleInventory

    val controlQuantity = selectArtNoCalculVM.controlQuantity
    val utilisateur = mainViewModel.utilisateur

    val stationList = mainViewModel.stationList

    val stationsDestination = mainViewModel.stationList/*.filter { it.sTATCode != utilisateur.Station }*/ // todo verify if keep user station or not

    val etatBonTransfertList = EtatBonTransfert.entries

     val codeM = mainViewModel.codeM
     val barCodeInfo = barCodeViewModel.barCodeInfo

    val isAutoScanMode = mainViewModel.isAutoScanMode
    val tvaList = mainViewModel.tvaList

    LaunchedEffect(key1 = barCodeInfo.value) {
        if(barCodeInfo.value=="") return@LaunchedEffect

        val scannedArticle = articleMapByBarCode[barCodeInfo.value]

        if (scannedArticle == null) {
            selectArtNoCalculVM.setSelectedArticlInventory(SelectedArticle(), from = "1")

            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(R.string.article_introvable, barCodeInfo.value),
                type =  ToastType.Error,
            )
            return@LaunchedEffect
        }

        val stationStockArticle = stationStockArticlMapByBarCode[barCodeInfo.value + station]?: StationStockArticle()


        val selectedArticle: SelectedArticle = selectedArticleInventoryList.firstOrNull { it.article.aRTCode == scannedArticle.aRTCode }?:
            SelectedArticle(
                article = scannedArticle,
                //stationStockArticleList = stationStockArticlMapByBarCode.filter { it.sARTCodeArt ==  barCodeInfo.value}
                stationStockArticleList = stationStockArticle
            )


        selectArtNoCalculVM.setSelectedArticlInventory(selectedArticle, from = "2")



        if (!isAutoScanMode) {
            mainViewModel.setAddNewProductDialogueVisibility(true)
            return@LaunchedEffect
        }

        addNewLigneSelectedInventoryArtcle(
            toaster = toaster,
            context = context,
            selectedArticle = selectedArticle,
            setSelectedArticlInventory = {
                selectArtNoCalculVM.setSelectedArticlInventory(it, from = "3")
            },
            addItemToSelectedArticleInventoryList = {
                selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)
            },
            resetBarCode = { barCodeViewModel.onBarCodeInfo(barCode = BareCode()) },
            isAutoScanMode = true,
            tva = if (selectedArticle.tva == Tva()) tvaList.first()
            else selectedArticle.tva,
            controlQuantity =  controlQuantity
        )

        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
    }

    LaunchedEffect(key1 = Unit) {

        if (utilisateur.typeUser.lowercase() != Globals.ADMIN && station == Station()){
            bonTransfertViewModel.onSelectedStationChange(stationList.first { it.sTATCode == utilisateur.Station })
        }

    }

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }
    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    mainViewModel.onShowDismissScreenAlertDialogChange(true)
                },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = codeM,
            )
        },
        bottomBar = {
            AnimatedVisibility(
                visible = true,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                AddViewBottomAppBar(
                    haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                    toaster = toaster,
                    showSaveBtn = selectedArticleInventoryList.isNotEmpty() && stationDestination != Station(),

                    onSaveClick = {
                      //  if(stationDestination == Station()) {
                      //      bonTransfertViewModel.onStationDestinationErrorChange(value =  UiText.StringResource(resId = R.string.required_field_error))
                     //  return@AddViewBottomAppBar
                     //   }
                        bonTransfertViewModel.saveBonTransfer(
                            toaster = toaster,
                            popBackStack = {
                                mainViewModel.resetTimePicker()
                                popBackStack()
                                           },
                            codeM = codeM,
                            selectedDateTime = mainViewModel.getSelectedDateTime(),
                            exerciceList = mainViewModel.exerciceList,
                            message = context.resources.getString(R.string.added_succesfully),
                            station = station,
                            stationDestination = stationDestination,
                            utilisateur = utilisateur,
                            selectedArticleInventoryList = selectedArticleInventoryList,
                            listStationStockArticl = stationStockArticlMapByBarCode,
                            updateQtePerStation = { newQteStation, newSartQteDeclare, codeArticle, codeStation->
                                mainViewModel.updateQtePerStation(newQteStation = newQteStation, newSartQteDeclare = newSartQteDeclare, codeArticle=codeArticle,codeStation=codeStation)
                            },
                            insertStationStockArticle = {stationStockArticl ->
                                mainViewModel.insertStationStockArticle(stationStockArticl)
                            },
                            updateArtQteStock = {  newQteAllStations,newQteStation,codeArticle ->
                                mainViewModel.updateArtQteStock(
                                    newQteAllStations = newQteAllStations,
                                    newQteStation= newQteStation,
                                    codeArticle=codeArticle
                                )
                            }
                        )
                    },
                    onClickAddArticle = {
                        mainViewModel.setAddNewProductDialogueVisibility(false)
                        selectArtNoCalculVM.setControlQte(true)
                        selectArtNoCalculVM.setSelectedArticlInventory(SelectedArticle(), from = "4")

                       navigate(SelectArticlesNoCalculRoute(stationOrigineCode = station.sTATCode))
                    },
                    isAutoScanMode = isAutoScanMode,
                    setAutoScanMode = {
                        mainViewModel.setAutoAddMode(!isAutoScanMode)
                    },
                    openBareCodeScanner  = {
                        openBareCodeScanner(
                            navigate = { navigate(it) },
                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                        )
                    },
                    showBareCodeScannerBtn = barCodeViewModel.haveCameraDevice
                )
            }
        }


    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                popBackStack()
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )
        CustomAlertDialogue(
            title = context.getString(R.string.delete),
            msg = context.getString(R.string.confirm_remove_ligne_data),
            openDialog = mainViewModel.showAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowAlertDialogChange(it)
            },
            customAction = {
                selectArtNoCalculVM.deleteItemToSelectedArticleInventoryList(selectedArticleInventory.article)
            },
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non)

        )

        if (mainViewModel.openAddNewProductDialogue) {

            AddNewProductDialogue(
                showTvaMenu = false,
                canModify = true,
                station = station,
                selectedArticleInventory = selectedArticleInventory,
                tvaExpand = mainViewModel.tvaExpand,
                uniteArticleExpand = mainViewModel.uniteArticleExpand,
                tvaList = tvaList,
                uniteArticleList = mainViewModel.uniteArticleList,
                onTvaExpandedChange = {
                    mainViewModel.onTvaExpandedChange(it)
                },
                onUniteArticleExpandedChange = {
                    mainViewModel.onUniteArticleExpandedChange(it)
                },
                setSelectedArticlInventory = {
                    selectArtNoCalculVM.setSelectedArticlInventory(it, from = "6")
                    //  selectArtInventoryVM.addItemToSelectedArticleInventoryList(it)
                },
                onDismiss = {
                    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                    selectArtNoCalculVM.setSelectedArticlInventory(SelectedArticle(), from = "fdds")
                    mainViewModel.setAddNewProductDialogueVisibility(false)
                },
                onConfirm = {
                addNewLigneSelectedInventoryArtcle(
                    toaster = toaster,
                    context = context,
                    fromScan = false,
                    selectedArticle = selectedArticleInventory,
                    setSelectedArticlInventory = {
                        selectArtNoCalculVM.setSelectedArticlInventory(it)
                    },
                    addItemToSelectedArticleInventoryList = {
                      //  Log.d("rdgdfgdsggd", "addItemToSelectedArticleInventoryList "+ it.quantity)
                        selectArtNoCalculVM.addItemToSelectedArticleInventoryList(it)
                    },
                    resetBarCode = { barCodeViewModel.onBarCodeInfo(barCode = BareCode()) },
                    isAutoScanMode = isAutoScanMode,
                    tva = if (selectedArticleInventory.tva == Tva()) tvaList.first()
                    else selectedArticleInventory.tva,
                    controlQuantity = controlQuantity
                )

                //  barCodeViewModel.onBarCodeInfo(BareCode())
                // selectArtInventoryVM.setSelectedArticlInventory(SelectedArticle(), from = "f")
                mainViewModel.setAddNewProductDialogueVisibility(false)
            }
            )
        }

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {

//           Row (
//               modifier = Modifier.fillMaxWidth().padding(top = 9.dp, start = 12.dp, end = 12.dp),
//               horizontalArrangement = Arrangement.SpaceEvenly,
//               verticalAlignment = Alignment.Top
//           ) {
//
//           }
            Row (
                modifier = Modifier.fillMaxWidth().padding(top = 9.dp, start = 12.dp, end = 12.dp),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.Top
            ) {
                GenericDropdownMenu(
                    modifier = Modifier.customWidth(0.45f),
                    designation = station.sTATDesg,
                    errorValue = bonTransfertViewModel.stationError?.asString(),
                    label = stringResource(R.string.station),
                    readOnly = true,
                    itemList = stationList/*.filter { it.sTATCode != stationDestination.sTATCode }*/,
                    selectedItem = station,
                    itemExpanded = mainViewModel.stationExpand,
                    getItemDesignation = { it.sTATDesg },
                    getItemTrailing = { it.sTATCode },
                    onClick = {
                        bonTransfertViewModel.onSelectedStationChange(it)

                        if(stationDestination.sTATCode == it.sTATCode) bonTransfertViewModel.onStationDestinationChange(Station())
                        mainViewModel.onStationExpandedChange(false)
                    },
                    onItemExpandedChange = { mainViewModel.onStationExpandedChange(it) },
                    lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                    lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
                )

                // Spacer(modifier = Modifier.width(12.dp))

                GenericDropdownMenu (
                    modifier = Modifier.customWidth(0.45f),
                    designation = stationDestination.sTATDesg,
                    errorValue =  bonTransfertViewModel.stationDestinationError?.asString(),
                    label = stringResource(R.string.distination_stations),
                    selectedItem = stationDestination,
                    itemList = stationsDestination.filter { it.sTATCode != station.sTATCode },
                    readOnly = true,
                    itemExpanded = mainViewModel.stationDestinationExpand,
                    getItemDesignation = { it.sTATDesg },
                    onClick = {
                        bonTransfertViewModel.onStationDestinationChange(it)
                        //  if(station.sTATCode == it.sTATCode) bonTransfertViewModel.onSelectedStationChange(Station())
                        mainViewModel.onStationDestinationExpandedChange(false)
                    },
                    getItemTrailing = { it.sTATCode },
                    onItemExpandedChange = { mainViewModel.onStationDestinationExpandedChange(it) },
                    lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                    lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
                )
            }




            GenericDropdownMenu (
                modifier = Modifier.fillMaxWidth(0.95f),
                designation = etatBonTransfert,
                errorValue =  null,
                label = stringResource(R.string.etat),
                selectedItem = etatBonTransfert,
                itemList = etatBonTransfertList.map { it.value },
                readOnly = true,
                itemExpanded = bonTransfertViewModel.etatBnTransfertExpand,
                getItemDesignation = { it },
                onClick = {
                    bonTransfertViewModel.onEtatBonTransfertChange(it.toString())
                    //  if(station.sTATCode == it.sTATCode) bonTransfertViewModel.onSelectedStationChange(Station())
                    bonTransfertViewModel.onEtatBnTransfertExpandChange(false)
                },
              //  getItemTrailing = { it. },
                onItemExpandedChange = { bonTransfertViewModel.onEtatBnTransfertExpandChange(it) },
                lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
            )

          /*  Spacer(modifier = Modifier.height(12.dp))

            TableHeader(
                onClickShowCalendar = {
                    mainViewModel.onShowDatePickerChange(true)
                },
                date = bonTransfertViewModel.selectedBonTransfert.bONTransDate?: "N/A",
                canModify = true,
                selectedDateTime = mainViewModel.getSelectedDateTime()
            )*/


            Spacer(modifier = Modifier.height(20.dp))




            FourColumnTable(
                rowTitls = context.resources.getStringArray(R.array.threeColumnTable_bon_transfert_array).toList(),// listOf("N°","Article","Qté","Prix"),//threeColumnTable_bon_transfert_array
                canModify = true,
                onTap = {
                    //  inventoryTextValidationViewModel.resetAddNewLigneVariable()

                    selectArtNoCalculVM.setSelectedArticlInventory(it, from = "5")

                    mainViewModel.setAddNewProductDialogueVisibility(true)

                },
                selectedListArticle = selectArtNoCalculVM.selectedArticleInventoryList,
                onSwipeToDelete = {
                    //    mainViewModel.onShowAlertDialogChange(true)
                    selectArtNoCalculVM.deleteItemToSelectedArticleInventoryList(it.article)

                },
                firstColumn = { item->
                    TableTextUtils.firstColumn(
                        selectedArticle = item,
                        parametrage = parametrage,
                        articleMapByBarCode = articleMapByBarCode
                    )


                },
                secondColumn = {
                    StringUtils.removeTrailingZeroInDouble(it.quantity)
                },
                thirdColumn = {
                    StringUtils.removeTrailingZeroInDouble(
                        CalculationsUtils.totalPriceArticle(
                        price = it.prixAchatHt,
                        quantity = it.quantity
                    )
                    )
                },
                infoText = {
                    if(stringToDouble(it.quantity) != 0.0)
                        context.getString(R.string.unit_price_field_title_info, StringUtils.removeTrailingZeroInDouble(it.prixAchatHt).ifEmpty { "0" })
                    else ""
                }
            )

            /*  if (state.listSelectedArticleError != null && selectedArticleInventoryList.isEmpty())
                  Text(
                      text = state.listSelectedArticleError,
                      color = MaterialTheme.colorScheme.error,
                      fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                      // modifier = Modifier.align(Alignment.Start)
                  )*/


        }


    }




}


