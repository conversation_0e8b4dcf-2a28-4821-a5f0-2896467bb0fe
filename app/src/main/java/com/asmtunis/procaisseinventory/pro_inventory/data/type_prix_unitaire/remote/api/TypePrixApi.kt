package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import kotlinx.coroutines.flow.Flow


interface TypePrixApi {


        suspend fun getTypePrixUnitaireHT(baseConfig: String): Flow<DataResult<List<TypePrixUnitaireHT>>>
}
