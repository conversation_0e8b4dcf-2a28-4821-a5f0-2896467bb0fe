package com.asmtunis.procaisseinventory.pro_inventory.inventaire.di

// This module is not needed as InventairePrintViewModel is directly injected with @HiltViewModel
// The file is kept for reference but the module is disabled

//@Module
//@InstallIn(ViewModelComponent::class)
object InventairePrintModule {
    
//    @Provides
//    @ViewModelScoped
//    fun provideInventairePrintViewModel(
//        sunmiPrintManager: SunmiPrintManager
//    ): InventairePrintViewModel {
//        return InventairePrintViewModel(sunmiPrintManager)
//    }
}
