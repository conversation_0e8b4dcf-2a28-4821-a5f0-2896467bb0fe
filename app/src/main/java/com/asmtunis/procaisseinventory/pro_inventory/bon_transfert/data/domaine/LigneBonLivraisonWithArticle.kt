package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class LigneBonLivraisonWithArticle (
    @Embedded
    @SerialName("ligneBonTransfert")
    var ligneTicket: LigneBonLivraison? = null,


    @Relation(
        parentColumn = "LG_BonTrans_CodeArt",
        entityColumn = "ART_Code"
    )
    @SerialName("article")
    var article: Article? = null,



    @Relation(
        parentColumn = "LG_BonTrans_CodeArt",
        entityColumn = "SART_CodeArt"
    )
    @SerialName("stationArticle")
    var stationArticle: List<StationStockArticle>? = emptyList(),
)