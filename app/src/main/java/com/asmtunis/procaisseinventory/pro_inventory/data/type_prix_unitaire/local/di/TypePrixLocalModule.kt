package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.dao.TypePrixUnitaireHTDAO
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.repository.TypePrixUnitaireHTLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.local.repository.TypePrixUnitaireHTLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class TypePrixLocalModule {

    @Provides
    @Singleton
    fun provideTypePrixDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.typePrixDAO()

    @Provides
    @Singleton
    @Named("TypePrix")
    fun provideTypePrixRepository(
        typePrixUnitaireHTDAO: TypePrixUnitaireHTDAO
    ): TypePrixUnitaireHTLocalRepository = TypePrixUnitaireHTLocalRepositoryImpl(
        typePrixUnitaireHTDAO = typePrixUnitaireHTDAO
    )

}