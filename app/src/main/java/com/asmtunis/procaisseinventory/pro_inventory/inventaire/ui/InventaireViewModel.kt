package com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.enum_classes.InventoryStatus
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.filter.InventaireFilterListState
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class InventaireViewModel @Inject constructor(
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val proInventoryRemote: ProInventoryRemote,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb
) : ViewModel() {

    var showSaveDialogue by mutableStateOf(false)
        private set
    fun onShowSaveDialogueChange(value: Boolean) {
        showSaveDialogue = value
    }

    var showFilterLine by mutableStateOf(false)
        private set
    fun onShowFilterLineChange(value: Boolean) {
        showFilterLine = value
    }


    var fiterValue by mutableStateOf("")
        private set
    fun onFilterValueChange(value: String) {
        fiterValue = value
    }


    var station by mutableStateOf(Station())
        private set
    fun onSelectedStationChange(value: Station) {
        station = value
    }

    var stationError: UiText? by mutableStateOf(null)
        private set
    fun onStationErrorChange(value: UiText?) {
        stationError = value
    }

    var showSearchView: Boolean by mutableStateOf(false)
        private set
    fun onShowSearchViewChange(value: Boolean) {
        showSearchView = value
    }
    var showCustomFilter: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomFilterChange(value: Boolean) {
        showCustomFilter = value
    }
    var showCustomModalBottomSheet: Boolean by mutableStateOf(false)
        private set
    fun onShowCustomModalBottomSheetChange(value: Boolean) {
        showCustomModalBottomSheet = value
    }


    var searchTextState: TextFieldValue by mutableStateOf(TextFieldValue(""))
        private set
    fun onSearchValueChange(value: TextFieldValue) {
        searchTextState = value
    }






     fun deleteInventaire(inventaire: Inventaire, listLgInventaire: List<LigneInventaire>) {
        viewModelScope.launch(ioDispatcher) {
            proInventoryLocalDb.inventaire.delete(inventaire)
            proInventoryLocalDb.ligneInventaire.deleteList(listLgInventaire)
        }
    }






    private fun saveInventaire(inventaire: Inventaire) {
        viewModelScope.launch(ioDispatcher) {
            proInventoryLocalDb.inventaire.upsert(inventaire)
        }
    }

    private fun saveLigneInventaire(inventaire: LigneInventaire) {
        viewModelScope.launch(ioDispatcher) {
            proInventoryLocalDb.ligneInventaire.upsert(inventaire)
        }
    }

    var selectedListLgInventaire = mutableStateListOf<LigneInventaire>()
        private set
    var selectedInventaire: Inventaire by mutableStateOf(Inventaire())
        private set
    fun onSelectedInventaireWithLineChange(value:  Map<Inventaire, List<LigneInventaire>>) {
        restInventaire()

        value.forEach { (key, value) ->
            run {
                selectedInventaire = key
                selectedListLgInventaire.addAll(value.map { it })
            }
        }

    }

    fun onSelectedInventaireChange(value: Inventaire) {
        selectedInventaire = value
    }

    fun onSelectedLigneInventaireChange(value: List<LigneInventaire>) {
        selectedListLgInventaire.clear()
        selectedListLgInventaire.addAll(value)
    }


    fun restInventaire(){
        selectedListLgInventaire.clear()
        selectedInventaire = Inventaire()
    }


    var inventaireListstate: InventaireFilterListState by mutableStateOf(
        InventaireFilterListState()
    )
        private set
    fun onEvent(event: ListEvent) {
        when (event) {
            is ListEvent.Order -> {
                if (inventaireListstate.listOrder::class == event.listOrder::class &&
                    inventaireListstate.listOrder.orderType == event.listOrder.orderType
                ) {
                    return
                }
                inventaireListstate = inventaireListstate.copy(
                    listOrder = event.listOrder
                )
                filterInventaire(inventaireListstate)
            }
            is ListEvent.Delete -> TODO()
            is ListEvent.Restore -> TODO()



            is ListEvent.ListSearch -> {
                inventaireListstate = inventaireListstate.copy(
                    search = event.listSearch
                )

                filterInventaire(inventaireListstate)
            }

            is ListEvent.FirstCustomFilter -> {
                inventaireListstate = inventaireListstate.copy(
                    filterByEtatInventaire = event.firstFilter
                )

                filterInventaire(inventaireListstate)
            }

            is ListEvent.SecondCustomFilter -> {
               TODO()
            }
            is ListEvent.ThirdCustomFilter -> TODO()
        }

    }

    var isLoadingFromDb by mutableStateOf(false)
    private set

    private var getInventaireJob: Job? = null
    fun filterInventaire(inventaireListState: InventaireFilterListState) {
        val searchedText = searchTextState.text
        val searchValue = inventaireListState.search
        val filterByEtatInventaire = inventaireListState.filterByEtatInventaire

        getInventaireJob?.cancel()
        isLoadingFromDb = true
        if (searchedText.isEmpty()) {
            getInventaireJob = when (inventaireListState.listOrder.orderType) {
                is OrderType.Ascending -> {
                    when (inventaireListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proInventoryLocalDb.inventaire.getAllFiltred(
                                isAsc = 1,
                                sortBy = "INV_Code",
                                filterByEtatInv = filterByEtatInventaire,
                                

                                ).collect {
                                setInventaireList(inventaire = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proInventoryLocalDb.inventaire.getAllFiltred(
                                isAsc = 1,
                                sortBy = "INV_Date",
                                filterByEtatInv = filterByEtatInventaire,
                                

                                ).collect {
                                setInventaireList(inventaire = it)
                            }
                        }

                        is ListOrder.Third -> TODO()
                    }
                }

                is OrderType.Descending -> {
                    when (inventaireListState.listOrder) {
                        is ListOrder.Title -> viewModelScope.launch {
                            proInventoryLocalDb.inventaire.getAllFiltred(
                                isAsc = 2,
                                sortBy = "INV_Code",
                                filterByEtatInv = filterByEtatInventaire,
                                
                            ).collect {
                                setInventaireList(inventaire = it)
                            }
                        }

                        is ListOrder.Date -> viewModelScope.launch {
                            proInventoryLocalDb.inventaire.getAllFiltred(
                                isAsc = 2,
                                sortBy = "INV_Date",
                                filterByEtatInv = filterByEtatInventaire,
                                

                                ).collect {
                                setInventaireList(inventaire = it)
                            }
                        }

                        is ListOrder.Third -> TODO()
                    }
                }
            }
        }
        else {
            if (searchValue is ListSearch.FirstSearch) {
                    getInventaireJob = when (inventaireListState.listOrder.orderType) {
                        is OrderType.Ascending -> {
                            when (inventaireListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.inventaire.filterByInvCode(
                                        searchString = searchedText,
                                        sortBy = "INV_Code",
                                        isAsc = 1,
                                        filterByEtatInv = filterByEtatInventaire,
                                        

                                        ).collect {
                                        setInventaireList(inventaire = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.inventaire.filterByInvCode(
                                        searchString = searchedText,
                                        sortBy = "INV_Date",
                                        isAsc = 1,
                                        filterByEtatInv = filterByEtatInventaire,
                                        

                                        ).collect {
                                        setInventaireList(inventaire = it)
                                    }
                                }

                                is ListOrder.Third -> TODO()
                                 
                            }
                        }

                        is OrderType.Descending -> {
                            when (inventaireListState.listOrder) {
                                is ListOrder.Title -> viewModelScope.launch {
                                    proInventoryLocalDb.inventaire.filterByInvCode(
                                        searchString = searchedText,
                                        sortBy = "INV_Code",
                                        isAsc = 2,
                                        filterByEtatInv = filterByEtatInventaire,
                                        

                                        ).collect {
                                        setInventaireList(inventaire = it)
                                    }
                                }

                                is ListOrder.Date -> viewModelScope.launch {
                                    proInventoryLocalDb.inventaire.filterByInvCode(
                                        searchString = searchedText,
                                        sortBy = "INV_Date",
                                        isAsc = 2,
                                        filterByEtatInv = filterByEtatInventaire,
                                        

                                        ).collect {
                                        setInventaireList(inventaire = it)
                                    }
                                }

                                is ListOrder.Third -> TODO()

                            }
                        }
                    }
                }

        }
    }




    private fun setInventaireList(inventaire:  Map<Inventaire, List<LigneInventaire>>){
        getInventaireJob = null
        isLoadingFromDb = false
        inventaireListstate = inventaireListstate.copy(
            lists =  emptyMap() // ,
            // listOrder = articlesListState.listOrder
        )

        inventaireListstate = inventaireListstate.copy(
            lists = inventaire // ,
            // listOrder = articlesListState.listOrder
        )
    }


    fun saveInventaire(
        navigateUp: () -> Unit,
        status: String = ItemStatus.INSERTED.status,
        hasPromo: Boolean = false,
        inventaireGeneratedNum: String,
        utilisateur: Utilisateur,
        selectedArticleInventoryList: List<SelectedArticle>,
    ) {
        viewModelScope.launch {
            val userId = utilisateur.codeUt

            val inventaire = Inventaire(
                iNVCode = inventaireGeneratedNum,
                invCodeM = inventaireGeneratedNum,
                iNVDate = getCurrentDateTime(),
                iNVCodeStation = station.sTATCode,
                iNVEtat = InventoryStatus.IN_PROGRESS.status,
                iNVUser = userId,
                timestamp = 0

            )
//TODO UPDATE ARTICLE QTE LOCALLY


            inventaire.isSync = false
            inventaire.status = status

            saveInventaire(inventaire)



            saveListLgInventaire(
                status = status,
                hasPromo = hasPromo,
                lGINVCodeInv = inventaireGeneratedNum,
                station = station,
                selectedArticleList = selectedArticleInventoryList,
                onSave = {
                    saveLigneInventaire(it)

                }
            )
            navigateUp()

        }


    }






    private fun saveListLgInventaire(
        status: String,
        hasPromo: Boolean = false,
        lGINVCodeInv : String,
        station: Station,
        selectedArticleList : List<SelectedArticle>,
        onSave : (LigneInventaire)-> Unit
    ){


        for (i in selectedArticleList.indices) {
            val articl = selectedArticleList[i].article

        //    val articleStationStock = selectedArticleList[i].stationStockArticleList.firstOrNull { it.sARTCodeSatation == station.sTATCode }?.sARTQteStation?: "0"//article.aRTQteStock
            val articleStationStock = selectedArticleList[i].stationStockArticleList.sARTQteStation?: "0" //.filter { it.value.sARTCodeSatation == station.sTATCode }[articl.aRTCode]?.sARTQteStation?: "0"//article.aRTQteStock


            val ligneInventaire =  LigneInventaire(
                lGINVCodeInv = lGINVCodeInv,
                lGINVCodeArticle = articl.aRTCode,
                lGINVCode = (i+1).toString(),
              //  lGINVQteStock = articl.sARTQte.toString(),
                lGINVQteStock = articleStationStock,
                lGINVQteReel = selectedArticleList[i].quantity,
                lGINVEtatLigne = InventoryStatus.IN_PROGRESS.status,
                lGINVValidationLigne = InventoryStatus.IN_PROGRESS.status,
                lGINVStation = station.sTATCode,
                lGINVPrixCMP = if(hasPromo) articl.prixSolde else articl.aRTPrixUnitaireHT
            )


            ligneInventaire.isSync = false
            ligneInventaire.status = ItemStatus.INSERTED.status

            onSave(ligneInventaire)

        }

    }



    fun setInventaireToInserted() {
        viewModelScope.launch(ioDispatcher) {
            proInventoryLocalDb.inventaire.setToInserted(codeM = selectedInventaire.invCodeM?: selectedInventaire.iNVCode)
            proInventoryLocalDb.ligneInventaire.setToInserted(codeM = selectedInventaire.invCodeM?: selectedInventaire.iNVCode)
        }

    }

}
