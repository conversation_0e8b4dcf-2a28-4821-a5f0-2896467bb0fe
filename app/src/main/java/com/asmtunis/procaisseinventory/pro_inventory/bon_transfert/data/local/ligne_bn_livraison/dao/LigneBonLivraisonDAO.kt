package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.local.ligne_bn_livraison.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.LIGNE_BON_LIVRAISON_TABLE
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import kotlinx.coroutines.flow.Flow


@Dao
interface LigneBonLivraisonDAO {
    @Query("SELECT * FROM $LIGNE_BON_LIVRAISON_TABLE WHERE isSync=0 and  (Status=:status)")
    fun getByStatus(status: String): Flow<List<LigneBonLivraison>>

    @Query("SELECT * FROM $LIGNE_BON_LIVRAISON_TABLE WHERE (Status=:status)")
    fun getByStatusForced(status: String): Flow<List<LigneBonLivraison>>

    @Query("SELECT * FROM $LIGNE_BON_LIVRAISON_TABLE where LG_BonTrans_NumBon=:num and LG_BonTrans_Exerc=:exercice")
    fun getByCode(num: String, exercice: String): Flow<List<LigneBonLivraison>>

    @Query("DELETE FROM $LIGNE_BON_LIVRAISON_TABLE where LG_BonTrans_NumBon=:num and LG_BonTrans_Exerc=:exercice and  LG_BonTrans_CodeArt= :codeArticle")
    fun deleteByCodeAndCodeArticle(num: String, exercice: String, codeArticle : String)

    @Query("UPDATE $LIGNE_BON_LIVRAISON_TABLE SET LG_BonTrans_NumBon = :code, Status = 'SELECTED' , IsSync = 1 where LG_BonTrans_NumBon = :codeM and LG_BonTrans_Exerc= :exercice" )
    fun updateState(code: String, codeM:String, exercice : String)


    @Query("SELECT COUNT(*) FROM $LIGNE_BON_LIVRAISON_TABLE where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='DELETED')")
    fun count(): Flow<Int>

    @get:Query("SELECT * FROM $LIGNE_BON_LIVRAISON_TABLE LIMIT 1")
    val one: Flow<LigneBonLivraison>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: LigneBonLivraison)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<LigneBonLivraison>)

    @Query("DELETE FROM $LIGNE_BON_LIVRAISON_TABLE")
    fun deleteAll()

    @Query("SELECT count(*) FROM $LIGNE_BON_LIVRAISON_TABLE where LG_BonTrans_NumBon=:myBon")
    fun countLignes(myBon: String): Flow<String>

    @Query("DELETE FROM $LIGNE_BON_LIVRAISON_TABLE where LG_BonTrans_NumBon=:num and LG_BonTrans_CodeArt=:code_article and LG_BonTrans_Exerc=:exercice ")
    fun delete(num: String?, code_article: String?, exercice: String?)

    @Delete
    fun deleteList(bonLivraisonList: List<LigneBonLivraison>)
}
