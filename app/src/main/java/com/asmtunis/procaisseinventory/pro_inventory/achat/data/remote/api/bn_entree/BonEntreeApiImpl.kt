package com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.bn_entree

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntreeResponse
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class BonEntreeApiImpl(private val client: HttpClient) : BonEntreeApi {
    override suspend fun getBonEntrees(
        baseConfig: String,
        mois: String
    ): Flow<DataResult<List<BonEntree>>> = flow {
        val queryParams = mapOf("mois" to mois)

        val result = executePostApiCall<List<BonEntree>>(
            client = client,
            endpoint = Urls.GET_BON_ENTREES,
            baseConfig = baseConfig,
            queryParams = queryParams
        )

        emitAll(result)
    }

        override suspend fun addBatchBonEntreesWithLines(baseConfig: String): Flow<DataResult<List<BonEntreeResponse>>> = flow {
            val result = executePostApiCall<List<BonEntreeResponse>>(
                client = client,
                endpoint = Urls.ADD_BATCH_BON_ENTREES_WITH_LINES,
                baseConfig = baseConfig,
            )

            emitAll(result)
        }

    }