package com.asmtunis.procaisseinventory.pro_inventory.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField

@Composable
fun AddNewProductDialogue(
    showTvaMenu : Boolean,
    onlyModifyQte : Boolean = true,
    station: Station = Station(),
    prixLabel : String = stringResource(R.string.purchase_price_title),
    canModify: Boolean,
    tvaExpand: Boolean,
    tvaList: List<Tva>,
    uniteArticleList: List<UniteArticle> = emptyList(),
    uniteArticleExpand: Boolean = false,
    selectedArticleInventory: SelectedArticle,
    setSelectedArticlInventory: (SelectedArticle) -> Unit,
    onTvaExpandedChange: (Boolean) -> Unit,
    onUniteArticleExpandedChange: (Boolean) -> Unit = {},
    onDismiss: () -> Unit,
    onConfirm: () -> Unit = {}
) {
    val scrollState = rememberScrollState()
   val prixAchatHt = selectedArticleInventory.prixAchatHt

    val article = selectedArticleInventory.article
    val stationStockArticle = selectedArticleInventory.stationStockArticleList

 //  val stockArticle = stationStockArticle.firstOrNull { it.sARTCodeSatation == station.sTATCode }?.sARTQteStation?:  article.sARTQte.toString()
   val stockArticle = selectedArticleInventory.qteStationFromDB
    //val stockArticle = article.aRTQteStock

    val currentUniteArticleList =   uniteArticleList.filter { it.uNITEARTICLECodeArt == article.aRTCode }

    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.
            onDismiss()

        },
        properties = DialogProperties(
            usePlatformDefaultWidth = true
        ),
        content = {
            Card(
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp)
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .verticalScroll(scrollState)
                      //  .background(color = MaterialTheme.colorScheme.background)
                       // .wrapContentSize()

                ) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = article.aRTDesignation.ifEmpty { "N/A" },
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.primary

                    )
                    Spacer(modifier = Modifier.height(9.dp))
                    Text(
                        text = stringResource(R.string.bar_code_value, article.aRTCode),
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))

                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.8f),
                        showLeadingIcon = false,
                      //  text = state.quantite,
                        text = selectedArticleInventory.quantity,
                        errorValue = selectedArticleInventory.quantityError?.asString(),
                        label = stringResource(R.string.quantity,  removeTrailingZeroInDouble(stockArticle)+" "+ article.uNITEARTICLECodeUnite)   ,
                        onValueChange = {
                            setSelectedArticlInventory(selectedArticleInventory.copy(quantity = it))
                        },
                        readOnly = !canModify,
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next
                    )



                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.8f),
                        showLeadingIcon = false,
                        text = if(canModify) prixAchatHt else convertStringToPriceFormat(prixAchatHt),
                        errorValue = selectedArticleInventory.prixAchatHtError?.asString(),
                        label = prixLabel,
                        onValueChange = { setSelectedArticlInventory(selectedArticleInventory.copy(prixAchatHt = it)) },
                        readOnly = !(canModify && !onlyModifyQte),
                        enabled = true,
                        leadingIcon = Icons.Default.Person,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next
                    )


                   if(currentUniteArticleList.isNotEmpty() && currentUniteArticleList.size>1) {
                        GenericDropdownMenu (
                            designation = removeTrailingZeroInDouble(selectedArticleInventory.uniteArticle.uNITEARTICLECodeUnite),
                            errorValue =  selectedArticleInventory.uniteArticleError?.asString(),
                            label = stringResource(R.string.unite_article),
                            readOnly = canModify,
                            itemExpanded = uniteArticleExpand,
                            itemList = currentUniteArticleList,
                            selectedItem = selectedArticleInventory.uniteArticle,
                            getItemDesignation = { it.uNITEARTICLECodeUnite },
                          //  getItemTrailing = { "%" },
                            onClick = {
                                setSelectedArticlInventory(selectedArticleInventory.copy(uniteArticle = it))
                                onUniteArticleExpandedChange(false)
                            },
                            onItemExpandedChange = {
                                onUniteArticleExpandedChange(it)
                            },
                            lottieAnimEmpty = {
                                LottieAnim(lotti = R.raw.emptystate)
                            },
                            lottieAnimError = {
                                LottieAnim(lotti = R.raw.connection_error, size = it)
                            }
                        )
                 }


                if(showTvaMenu) {
                    GenericDropdownMenu (
                        designation = removeTrailingZeroInDouble(selectedArticleInventory.tva.tVACode),
                        errorValue =  selectedArticleInventory.tvaError?.asString(),
                        label = stringResource(R.string.tva),
                        readOnly = canModify,
                        itemExpanded = tvaExpand,
                        itemList = tvaList,
                        selectedItem = selectedArticleInventory.tva,
                        getItemDesignation = { it.tVACode },
                        getItemTrailing = { "%" },
                        onClick = {
                            setSelectedArticlInventory(selectedArticleInventory.copy(tva = it))
                            onTvaExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            onTvaExpandedChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )
                }

                    Row (
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(30.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                         OutlinedButton(
                            onClick = {
                                onDismiss()
                            },
                            /* colors = ButtonDefaults.buttonColors(
                                 backgroundColor = orange,
                                 contentColor = white
                             ),*/
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            shape = CircleShape
                        ) {
                            Text(
                                text = stringResource(R.string.quitter),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                            )
                        }
                        if (canModify)
                            Button(
                            onClick = {
                                onConfirm()
                            },

                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            shape = CircleShape
                        ) {
                            Text(
                                text = stringResource(R.string.confirm),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                            )
                        }
                    }


                }
            }

        }
    )
}