package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.remote.api.ligne_bn_livraison

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class LigneBonLivraisonApiImpl(private val client: HttpClient) : LigneBonLivraisonApi {
    override suspend fun getLigneBonLivraison(baseConfig: String): Flow<DataResult<List<LigneBonLivraison>>> = flow {

        val result = executePostApiCall<List<LigneBonLivraison>>(
            client = client,
            endpoint = Urls.GET_LIGNE_BON_LIVRAISON,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }
    }