package com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.remote.api


import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class TypePrixApiImpl(private val client: HttpClient) : TypePrixApi {
    override suspend fun getTypePrixUnitaireHT(baseConfig: String): Flow<DataResult<List<TypePrixUnitaireHT>>> = flow {
        val result = executePostApiCall<List<TypePrixUnitaireHT>>(
            client = client,
            endpoint = Urls.GET_TYPE_PRIX_UNITAIRE_HT,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

}