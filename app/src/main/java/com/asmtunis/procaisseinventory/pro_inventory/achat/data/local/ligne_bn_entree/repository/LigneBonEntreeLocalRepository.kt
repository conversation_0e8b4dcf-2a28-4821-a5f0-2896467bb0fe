package com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.repository

import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import kotlinx.coroutines.flow.Flow



interface LigneBonEntreeLocalRepository {
    fun upsertAll(value: List<LigneBonEntree>)
    fun upsert(value: LigneBonEntree)
    fun deleteAll()
    fun deleteAllList(listLigneBE: List<LigneBonEntree>)
    fun  deleteByBeCode(code: String, exercice: String)
    fun deleteByBeCodeAndCodeBar(code: String, exercice: String, codeBare : String)

    fun getAll(): Flow<List<LigneBonEntree>>

    fun  updateLigneBonEntreeStatus(bonEntNum: String, bonEntNumM: String, exercice: String)
}