package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProInventoryConstants.BON_LIVRAISON_TABLE/*,primaryKeys = ["BON_Trans_Num", "BON_Trans_Exerc"]*/)
@Serializable
data class BonLivraison (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @ColumnInfo(name = "BON_Trans_Num")
    @SerialName("BON_Trans_Num")
    var bONTransNum: String = "",


    @ColumnInfo(name = "BON_Trans_Num_M")
    @SerialName("BON_Trans_Num_M")
    var bONTransNumM: String? = "",

    @ColumnInfo(name = "BON_Trans_Exerc")
    @SerialName("BON_Trans_Exerc")
    var bONTransExerc: String = "",

    @ColumnInfo(name = "BON_Trans_Date")
    @SerialName("BON_Trans_Date")
    var bONTransDate: String? = "",

    @ColumnInfo(name = "BON_Trans_StatSource")
    @SerialName("BON_Trans_StatSource")
    var bONTransStatSource: String? = "",

    @ColumnInfo(name = "BON_Trans_StatDest")
    @SerialName("BON_Trans_StatDest")
    var bONTransStatDest: String? = "",

    @ColumnInfo(name = "BON_Trans_Etat")
    @SerialName("BON_Trans_Etat")
    var bONTransEtat: String? = "",

    @ColumnInfo(name = "BON_Trans_Stat")
    @SerialName("BON_Trans_Stat")
    var bONTransStat: String? = "",

    @ColumnInfo(name = "BON_Trans_User")
    @SerialName("BON_Trans_User")
    var bONTransUser: String? = "",

    @ColumnInfo(name = "BON_Trans_export")
    @SerialName("BON_Trans_export")
    var bONTransExport: String? = "",

    @ColumnInfo(name = "BON_Trans_DDm")
    @SerialName("BON_Trans_DDm")
    var bONTransDDm: String? = "",

    @ColumnInfo(name = "Declaree")
    @SerialName("Declaree")
    var declaree: String? = "",

    @ColumnInfo(name = "Exportation")
    @SerialName("Exportation")
    var exportation: String? = "",

    @ColumnInfo(name = "DDm")
    @SerialName("DDm")
    var dDm: String? = "",

    @ColumnInfo(name = "Num_Manuelle")
    @SerialName("Num_Manuelle")
    var numManuelle: String? = "",

    @ColumnInfo(name = "Observation")
    @SerialName("Observation")
    var observation: String? = "",

    @ColumnInfo(name = "BON_Trans_Etat_Saisie")
    @SerialName("BON_Trans_Etat_Saisie")
    var bONTransEtatSaisie: String? = "",

    @ColumnInfo(name = "BON_Trans_Mnt_HT")
    @SerialName("BON_Trans_Mnt_HT")
    var bONTransMntHT: String? = "",

    @ColumnInfo(name = "BON_Trans_Mnt_TTC")
    @SerialName("BON_Trans_Mnt_TTC")
    var bONTransMntTTC: String? = "",

    @ColumnInfo(name = "BON_Trans_Transporteur")
    @SerialName("BON_Trans_Transporteur")
    var bONTransTransporteur: String? = "",

    @ColumnInfo(name = "BON_Trans_Vehicule")
    @SerialName("BON_Trans_Vehicule")
    var bONTransVehicule: String? = "",

    @ColumnInfo(name = "BON_Trans_obs")
    @SerialName("BON_Trans_obs")
    var bONTransObs: String? = "",

    @ColumnInfo(name = "BON_ENT_SYNC")
    @SerialName("BON_ENT_SYNC")
    var bONENTSYNC: String? = "",

    @ColumnInfo(name = "timestamp")
 //   @Exclude
    var timestamp: Long = 0




): BaseModel() {
    @Ignore
    val bONTransDateFormatted = this.bONTransDate?.substringBefore(".")?: "N/A"
}
