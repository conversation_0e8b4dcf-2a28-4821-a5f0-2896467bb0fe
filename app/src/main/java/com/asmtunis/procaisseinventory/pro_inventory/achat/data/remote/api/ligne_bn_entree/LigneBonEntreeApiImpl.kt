package com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.ligne_bn_entree

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class LigneBonEntreeApiImpl(private val client: HttpClient) : LigneBonEntreeApi {
    override suspend fun getLigneBonEntrees(
        baseConfig: String,
        mois: String
    ): Flow<DataResult<List<LigneBonEntree>>> = flow {

        val queryParams = mapOf("mois" to mois)

        val result = executePostApiCall<List<LigneBonEntree>>(
            client = client,
            endpoint = Urls.GET_LIGNE_BON_ENTREES,
            baseConfig = baseConfig,
            queryParams = queryParams
        )

        emitAll(result)
    }
    }