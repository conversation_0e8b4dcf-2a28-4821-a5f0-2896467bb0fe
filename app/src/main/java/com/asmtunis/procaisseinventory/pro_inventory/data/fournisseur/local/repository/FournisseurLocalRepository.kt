package com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.local.repository

import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import kotlinx.coroutines.flow.Flow


    interface FournisseurLocalRepository {
        fun upsertAll(value: List<Fournisseur>)
        fun upsert(value: Fournisseur)
        fun deleteAll()

        fun getAll(): Flow<List<Fournisseur>>

}