package com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.remote.di

import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.remote.api.FournisseurApi
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.remote.api.FournisseurApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object FournisseurRemoteModule {

    @Provides
    @Singleton
    fun provideFournisseurApi(client: HttpClient): FournisseurApi = FournisseurApiImpl(client)

}