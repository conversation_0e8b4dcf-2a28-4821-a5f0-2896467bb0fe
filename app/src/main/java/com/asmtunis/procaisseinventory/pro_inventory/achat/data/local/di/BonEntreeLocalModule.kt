package com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.dao.BonEntreeDAO
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.repository.BonEntreeLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.bn_entree.repository.BonEntreeLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.dao.LigneBonEntreeDAO
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.repository.LigneBonEntreeLocalRepository
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.local.ligne_bn_entree.repository.LigneBonEntreeLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class BonEntreeLocalModule {

    @Provides
    @Singleton
    fun provideBonEntreeDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.bonEntreeDAO()

    @Provides
    @Singleton
    @Named("BonEntree")
    fun provideBonEntreeRepository(
        bonEntreeDAO: BonEntreeDAO
    ): BonEntreeLocalRepository = BonEntreeLocalRepositoryImpl(
        bonEntreeDAO = bonEntreeDAO
    )



    @Provides
    @Singleton
    fun provideLigneBonEntreeDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.ligneBonEntreeDAO()

    @Provides
    @Singleton
    @Named("LigneBonEntree")
    fun provideLigneBonEntreeRepository(
        lignebonEntreeDAO: LigneBonEntreeDAO
    ): LigneBonEntreeLocalRepository = LigneBonEntreeLocalRepositoryImpl(
        ligneBonEntreeDAO = lignebonEntreeDAO
    )

}