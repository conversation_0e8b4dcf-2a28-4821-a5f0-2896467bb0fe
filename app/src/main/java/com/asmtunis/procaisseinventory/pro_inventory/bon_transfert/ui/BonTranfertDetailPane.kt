package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material.icons.twotone.Print
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothInfoDialogue
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.print.PrintFunctions.print
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.BonTransfertViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ui.AddNewProductDialogue
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.tables.TableTextUtils.firstColumn
import com.asmtunis.procaisseinventory.shared_ui_components.tables.four_column.FourColumnTable
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.edit_text.EditTextField
import java.util.Locale


@Composable
fun BonTranfertDetailPane(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    bonTransfertViewModel: BonTransfertViewModel,
    barCodeViewModel: BarCodeViewModel,
    dataViewModel: DataViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    networkViewModel: NetworkViewModel,
    selectArtInventoryVM : SelectArticleNoCalculViewModel,
    mainViewModel: MainViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    settingViewModel: SettingViewModel
) {

    val context = LocalContext.current

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig
    val printParams = dataViewModel.printData

   val selectedArticleInventoryList = selectArtInventoryVM.selectedArticleInventoryList
   val stationOrigine = bonTransfertViewModel.station
    val stationDestination = bonTransfertViewModel.stationDestination
    val articleMapByBarCode = mainViewModel.articleMapByBarCode
     val parametrage =mainViewModel.parametrage

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

     val tvaList = mainViewModel.tvaList

    val fiterValue = bonTransfertViewModel.fiterValue

    val stationList = mainViewModel.stationList


    val selectedBonTransfert = bonTransfertViewModel.selectedBonTransfert
    val selectedListLgBonTransfert = bonTransfertViewModel.selectedListLgBonTransfert

    LaunchedEffect(key1 = Unit){
        if(printViewModel.proccedPrinting && printViewModel.deviceAddress.isNotEmpty()) {

            print(
                context = context,
                toaster = toaster,
                navigate = { navigate(it) },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                printParams = printParams,
                toPrintBT = {
                    printViewModel.printBonTransfert(
                        context = context,
                        lgBonTransfert = selectedListLgBonTransfert,
                        bonTransfert = selectedBonTransfert,
                        articleMapByBarCode = articleMapByBarCode,
                        stationSource = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatSource },
                        stationDestination = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatDest },
                        printParams = printParams
                    )
                }
            )
        }


      /*      for (i in ligneBonTransfert.indices) {
                val tva = tvaList.firstOrNull { it.tVACode == ligneBonTransfert[i].lGBonTransTVA}
                val article = mainViewModel.articleList.firstOrNull { it.aRTCode == ligneBonTransfert[i].lGBonTransCodeArt}?: Article(aRTCodeBar = ligneBonTransfert[i].lGBonTransCodeArt, aRTCode = ligneBonTransfert[i].lGBonTransCodeArt)

            val stationArt = stationArtList.firstOrNull{ it.sARTCodeArt == article.aRTCode}?: StationStockArticle()

                selectArtInventoryVM.setConsultationSelectedArticleInventoryList(
                    article = article,
                    tva = tva,
                    quantity= ligneBonTransfert[i].qteDecTransferer!!,
                    prixCaisse = ligneBonTransfert[i].lGBonTransPUHT!!,
                    prixAchatHt = ligneBonTransfert[i].lGBonTransPUHT!!,
                    qteTtStation = article.aRTQteStock.ifEmpty { "0" }
                )

            }*/



      //  val tvaMap = tvaList.associateBy { it.tVACode }
      //  val articleMap = mainViewModel.articleList.associateBy { it.aRTCode }



    }





        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = {
                       popBackStack()
                    },
                    navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                    title = selectedBonTransfert.bONTransNum,

                    actions = {

                        IconButton(
                            onClick = {
                                print(
                                    context = context,
                                    toaster = toaster,
                                    navigate = { navigate(it) },
                                    printViewModel = printViewModel,
                                    bluetoothVM = bluetoothVM,
                                    printParams = printParams,
                                    toPrintBT = {
                                        printViewModel.printBonTransfert(
                                            context = context,
                                            lgBonTransfert = selectedListLgBonTransfert,
                                            bonTransfert = selectedBonTransfert,
                                            articleMapByBarCode = articleMapByBarCode,
                                            stationSource = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatSource },
                                            stationDestination = stationList.first { it.sTATCode == selectedBonTransfert.bONTransStatDest },
                                            printParams = printParams
                                        )
                                    }
                                )
                            }
                        ) {
                            Icon(
                                imageVector = Icons.TwoTone.Print,
                                contentDescription = stringResource(id = R.string.print)
                            )
                        }
                    }
                )
            }



        ) { padding ->
            if(printViewModel.openPrintInfoDialogue) {
                BluetoothInfoDialogue(
                    printResult = printViewModel.printResult,
                    onOpenPrintInfoDialogueChange = {
                        printViewModel.onOpenPrintInfoDialogueChange(it)
                    }
                )
            }



            if (mainViewModel.openAddNewProductDialogue) {

                AddNewProductDialogue(
                    showTvaMenu = false,
                    station = stationOrigine,
                    canModify = false,
                    selectedArticleInventory = selectArtInventoryVM.selectedArticleInventory,
                    tvaExpand = mainViewModel.tvaExpand,
                    tvaList = tvaList,
                    onTvaExpandedChange = { mainViewModel.onTvaExpandedChange(it) },
                    setSelectedArticlInventory = { selectArtInventoryVM.setSelectedArticlInventory(it, from = "6") },
                    onDismiss = {
                        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                        selectArtInventoryVM.setSelectedArticlInventory(SelectedArticle(), from = "fdds")
                        mainViewModel.setAddNewProductDialogueVisibility(false)
                    }
                )
            }

            Column(
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
            ) {


                Row (
                    modifier = Modifier.fillMaxWidth().padding(top = 9.dp, start = 12.dp, end = 12.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.Top
                ) {
                    EditTextField(
                        modifier = Modifier.customWidth(0.45f),
                        text = stationOrigine.sTATDesg,
                        label = stringResource(R.string.station),
                        onValueChange = {},
                        readOnly = true,
                        enabled = true,
                        leadingIcon = Icons.Default.QrCodeScanner,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )
                   Spacer(modifier = Modifier.width(12.dp))

                    EditTextField(
                        modifier = Modifier.customWidth(0.45f),
                        text = stationDestination.sTATDesg,
                        label = stringResource(R.string.distination_stations),
                        onValueChange = {},
                        readOnly = true,
                        enabled = true,
                        leadingIcon = Icons.Default.QrCodeScanner,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )
                }

                EditTextField(
                    modifier = Modifier.customWidth(0.95f),
                    text = selectedBonTransfert.bONTransEtat?: "N/A",
                    label = stringResource(R.string.etat),
                    onValueChange = {},
                    readOnly = true,
                    enabled = true,
                    leadingIcon = Icons.Default.QrCodeScanner,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                )

               /* Spacer(modifier = Modifier.height(12.dp))

                TableHeader(
                    onClickShowCalendar = {
                        mainViewModel.onShowDatePickerChange(true)
                    },
                    date = selectedBonTransfert.bONTransDate?: "N/A",
                    canModify = false,
                    selectedDateTime = mainViewModel.getSelectedDateTime()
                )*/


                Spacer(modifier = Modifier.height(20.dp))


            //    inventoryTextValidationViewModel.onAddNewEvent(InventoryFormEvent.selectedListArticleChanged(selectedArticleInventoryList))


                FourColumnTable(
                    showFilterLine = selectedArticleInventoryList.size>9 && bonTransfertViewModel.showFilterLine,
                    onShowFilterLineChange = { bonTransfertViewModel.onShowFilterLineChange(it) },
                    fiterValue = fiterValue,
                    onFilterValueChange = { bonTransfertViewModel.onFilterValueChange(it) },
                    rowTitls = context.resources.getStringArray(R.array.threeColumnTable_bon_transfert_array).toList(),
                    canModify = false,
                    selectedListArticle = if(fiterValue.isNotEmpty()) selectedArticleInventoryList.filter { it.article.aRTCode.lowercase(
                        Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT))  || it.article.aRTDesignation.lowercase(
                        Locale.ROOT).contains(fiterValue.lowercase(Locale.ROOT)) } else selectedArticleInventoryList,

                    onTap = {
                        //  inventoryTextValidationViewModel.resetAddNewLigneVariable()

                        selectArtInventoryVM.setSelectedArticlInventory(it, from = "5")

                        mainViewModel.setAddNewProductDialogueVisibility(true)

                    },

                    onSwipeToDelete = {
                        //    mainViewModel.onShowAlertDialogChange(true)
                        selectArtInventoryVM.deleteItemToSelectedArticleInventoryList(it.article)

                    },
                    firstColumn = { item->
                        firstColumn(
                            selectedArticle = item,
                            parametrage = parametrage,
                            articleMapByBarCode = articleMapByBarCode
                        )
                    },
                    secondColumn = {
                        StringUtils.removeTrailingZeroInDouble(it.quantity)
                    },
                    thirdColumn = {
                        //StringUtils.convertStringToPriceFormat(it.prixCaisse)
                      //  StringUtils.removeTrailingZeroInDouble(it.prixCaisse)
                        StringUtils.removeTrailingZeroInDouble(
                            CalculationsUtils.totalPriceArticle(
                                price = it.prixAchatHt,
                                quantity = it.quantity
                            )
                        )
                    },
                    infoText = {
                        if(stringToDouble(it.quantity) != 0.0)
                            context.getString(R.string.unit_price_field_title_info, StringUtils.removeTrailingZeroInDouble(it.prixAchatHt).ifEmpty { "0" })
                        else ""
                    }
                )

              /*  if (state.listSelectedArticleError != null && selectedArticleInventoryList.isEmpty())
                    Text(
                        text = state.listSelectedArticleError,
                        color = MaterialTheme.colorScheme.error,
                        fontSize = MaterialTheme.typography.bodyLarge.fontSize,
                        // modifier = Modifier.align(Alignment.Start)
                    )*/


            }


        }




 }












