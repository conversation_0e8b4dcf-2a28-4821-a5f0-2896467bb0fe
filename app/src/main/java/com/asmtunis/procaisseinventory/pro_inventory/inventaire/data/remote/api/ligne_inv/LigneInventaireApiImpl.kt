package com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.remote.api.ligne_inv

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.Urls.GET_LIGNE_INVENTAIRES_BY_CODE
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class LigneInventaireApiImpl(private val client: HttpClient) : LigneInventaireApi {
    override suspend fun getLigneInventaires(baseConfig: String): Flow<DataResult<List<LigneInventaire>>> = flow {

        val result = executePostApiCall<List<LigneInventaire>>(
            client = client,
            endpoint = Urls.GET_LIGNE_INVENTAIRES,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }

    override suspend fun getLigneInventairesByCode(
        baseConfig: String,
        code: String
    ): Flow<DataResult<List<LigneInventaire>>> = flow {
        val queryParams = emptyMap<String, String>().toMutableMap()



        queryParams += mapOf("LG_INV_Code_Inv" to code)

        val result = executePostApiCall<List<LigneInventaire>>(
            client = client,
            endpoint = GET_LIGNE_INVENTAIRES_BY_CODE,
            queryParams = queryParams,
            baseConfig = baseConfig,
        )

        emitAll(result)
    }
    }


