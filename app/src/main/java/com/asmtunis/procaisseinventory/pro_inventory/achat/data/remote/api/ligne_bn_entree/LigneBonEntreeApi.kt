package com.asmtunis.procaisseinventory.pro_inventory.achat.data.remote.api.ligne_bn_entree

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import kotlinx.coroutines.flow.Flow


interface LigneBonEntreeApi {
        suspend fun getLigneBonEntrees(baseConfig: String, mois: String): Flow<DataResult<List<LigneBonEntree>>>

}