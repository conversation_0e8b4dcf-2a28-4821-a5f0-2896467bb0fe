package com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.di

import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.view_model.BonTransfertPrintViewModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.scopes.ViewModelScoped

// This module is no longer needed as BonTransfertPrintViewModel is now directly injected with @HiltViewModel
// The file is kept for reference but the module is disabled

//@Module
//@InstallIn(ViewModelComponent::class)
object BonTransfertPrintModule {

//    @Provides
//    @ViewModelScoped
//    fun provideBonTransfertPrintViewModel(
//        sunmiPrintManager: SunmiPrintManager
//    ): BonTransfertPrintViewModel {
//        return BonTransfertPrintViewModel(sunmiPrintManager)
//    }
}
