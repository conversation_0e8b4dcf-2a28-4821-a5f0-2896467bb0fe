package com.asmtunis.procaisseinventory.shared_ui_components.lazy_column

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.LocationOff
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.Globals.ACTIVE
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationUtils.isValidGPScoordinates
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.simapps.ui_kit.ModifiersUtils.detectTapGestures

@Composable
fun RowClientList(
    selectedClient: Client = Client(),
    client: Client,
    cLINomPren: String = "",
    color: Color = Color.Unspecified,
    onClick: () -> Unit,
    onLongPress: () -> Unit = {}
) {
    OutlinedCard(
        border = if(selectedClient != client) CardDefaults.outlinedCardBorder() else BorderStroke(color = MaterialTheme.colorScheme.outline, width = 3.dp),
        modifier = Modifier.padding(start = 12.dp, end = 12.dp)
            .detectTapGestures(
                key1 = Unit,
            onDoubleTap = {},
            onLongPress = {
                onLongPress()
            },
            onPress = {

            },
            onTap = {
                onClick()
            },
        ),
      /*  onClick = {

        }*/
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start,
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .padding(bottom = 6.dp)

            // .background("#063041".color)
        ) {
            Column(
                modifier = Modifier
                    //  .background(colorResource(id =if(state.lists[index].solde.toDouble() < 0.0) R.color.teal_700 else R.color.white ))
                    .wrapContentSize(),
                // .padding(padding) ,
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (!client.isSync) {
                    LottieAnim(
                        lotti = R.raw.connection_error,
                        size = 85.dp,
                        onClick = { onClick() }
                    )
                } else {

                    Icon(
                        imageVector = if(isValidGPScoordinates(lat = client.cltLatitude, long = client.cltLongitude)) Icons.Default.Person else Icons.Default.LocationOff,
                        contentDescription = "",
                        modifier = Modifier
                            .size(85.dp)
                        // .padding(5.dp)
                    )
                }

                Text(
                    modifier = Modifier.width(80.dp),
                    text = client.cLICode,
                    style = MaterialTheme.typography.bodySmall,
                    //  color = Color.Black,
                    maxLines = 1,
                    textAlign = TextAlign.Center
                )


                if(client.cLIEtat.lowercase() != ACTIVE.lowercase()) {
                    Text(
                        modifier = Modifier.width(80.dp),
                        text = client.cLIEtat,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error,
                        maxLines = 1,
                        textAlign = TextAlign.Center
                    )
                }
            }

            Column(
                modifier = Modifier
                    //  .background(colorResource(id =if(state.lists[index].solde.toDouble() < 0.0) R.color.teal_700 else R.color.white ))
                    .wrapContentSize(),
                // .padding(padding) ,
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = cLINomPren.ifEmpty { client.cLINomPren.ifEmpty { client.cLICode } },
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 2,
                     color = color
                )

                Text(
                    text = stringResource(id = R.string.type, client.cLIType),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    //   color = Color.Black
                )

                Text(
                    text = buildAnnotatedString {
                        append(stringResource(id = R.string.solde))

                        withStyle(
                            SpanStyle(
                                color = if (StringUtils.stringToDouble(client.solde) < 0.0) MaterialTheme.colorScheme.error
                                else MaterialTheme.colorScheme.primary
                            )
                        ){
                            append(" ")
                            append(
                                StringUtils.convertStringToPriceFormat(client.solde)
                            )
                        }
                    },
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}