package com.asmtunis.procaisseinventory.shared_ui_components.buttons

import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.AttachEmail
import androidx.compose.material.icons.twotone.Sync
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.utils.IntentUtils.openEmailApp
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim


@Composable
fun <T> SyncButtons(
    nbrNotSync: Int = 0,
    text: String,
    remoteResponseState: RemoteResponseState<T>,
    emailBody: String,
    isConnected: Boolean,
    onClickSync: () -> Unit
) {




     if (nbrNotSync>0) {
         val context = LocalContext.current
         val isLoading = remoteResponseState.loading
         val error = remoteResponseState.error

         OutlinedCard(
             modifier = Modifier.fillMaxWidth().padding(8.dp),
             shape = RoundedCornerShape(10.dp),
             elevation = CardDefaults.cardElevation(
                 defaultElevation = 4.dp
             )
         ) {

                 Column(
                     modifier = Modifier.fillMaxWidth().padding(8.dp),
                     verticalArrangement = Arrangement.Center,
             horizontalAlignment = Alignment.CenterHorizontally
                 ) {
                     Text(
                         modifier = Modifier.fillMaxWidth(),
                         textAlign = TextAlign.Center,
                         text = "$text ($nbrNotSync)",
                         style = MaterialTheme.typography.titleMedium
                     )
                    Spacer(modifier = Modifier.height(12.dp))

                     if(error != null) {
                         Text(text = error, color = MaterialTheme.colorScheme.error)
                         Spacer(modifier = Modifier.height(12.dp))
                     }
                     if (!isConnected) {
                         LottieAnim(lotti = R.raw.no_connection, size = 25.dp)
                         Spacer(modifier = Modifier.height(12.dp))
                     }
                     else if (isLoading) {
                         LottieAnim(lotti = R.raw.loading, size = 100.dp)
                         Spacer(modifier = Modifier.height(12.dp))
                     }
                     else
                     Row(
                         modifier = Modifier.fillMaxWidth().padding(8.dp),
                         horizontalArrangement = Arrangement.Center,
                     verticalAlignment = Alignment.CenterVertically,
                     ) {
                         Spacer(modifier = Modifier.height(12.dp))
                         IconButton (
                             onClick = { onClickSync() },
                             modifier = Modifier,
                         ) {

                             Icon(
                                 imageVector = Icons.TwoTone.Sync,
                                 contentDescription = stringResource(id = R.string.cd_favorite_button)
                             )

                         }

                         Spacer(modifier = Modifier.width(12.dp))

                         IconButton(
                             onClick = {
                                 openEmailApp(
                                     context = context,
                                     email = "<EMAIL>",
                                     body = emailBody,
                                     subject = text,
                                     onComplete = { error ->
                                         if(error != null)
                                             Toast.makeText(context, error, Toast.LENGTH_LONG).show()

                                     }
                                 )
                             },
                             modifier = Modifier,
                            // shape = MaterialTheme.shapes.medium
                         ) {
                             Icon(
                                 imageVector = Icons.TwoTone.AttachEmail,
                                 contentDescription = stringResource(id = R.string.cd_favorite_button)
                             )
                         }
                         Spacer(modifier = Modifier.height(12.dp))
                 }
             }
         }

    }


}
