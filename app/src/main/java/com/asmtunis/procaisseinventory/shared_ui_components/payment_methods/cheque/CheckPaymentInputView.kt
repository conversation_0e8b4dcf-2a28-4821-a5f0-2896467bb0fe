@file:OptIn(ExperimentalMaterial3Api::class)

package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.twotone.DateRange
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.simapps.ui_kit.date_time_picker.DatePickerView
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField
import com.simapps.ui_kit.edit_text.EditTextFieldClickable
import com.simapps.ui_kit.utils.PastOrPresentSelectableDates
import kotlinx.coroutines.launch
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CheckPaymentInputView(
    montantCheque :String,
    requestFocus : Boolean,
    montantChequeError :UiText?,
    onMontantChequeChange: (String) -> Unit,
    checkNbr: String,
    checkNbrError: UiText?,
    onCheckNbrChange: (String) -> Unit,
    checkDeadline : String,
    checkDeadlineError : UiText?,
    onCheckDeadlineChange: (String) -> Unit,
    onDismissRequest: () -> Unit,
    onAddClicked: () -> Unit,
    bankList: List<Banque>,
    onBanqueExpandChange:(Boolean)->Unit,
    isBanqueExpanded : Boolean,
    selectedBanque : Banque,
    selectedBanqueError : UiText?,
    onSelectedBanqueChange :(Banque)->Unit,
    showDatePicker: Boolean,
    onShowDatePickerChange:(Boolean)->Unit

) {
    val sheetState = rememberModalBottomSheetState()
    val scope = rememberCoroutineScope()
    var chequesFilter by rememberSaveable { mutableStateOf("") }


//boundary = DATE_RANGE_FROM_NOW
    if(showDatePicker) {
        DatePickerView(
            selectableDates = PastOrPresentSelectableDates,
            setDateVisibility = {
                onShowDatePickerChange(it)
            },
            onSelectedDateChange = {
                onCheckDeadlineChange(it)
            },
            confirmText = stringResource(R.string.confirm),
            cancelText = stringResource(R.string.cancel)
        )
    }
    Dialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.
            onDismissRequest()

        },
        properties = DialogProperties(usePlatformDefaultWidth = true),
        content = {
            Card(
                elevation = CardDefaults.cardElevation(),
                shape = RoundedCornerShape(15.dp),
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth().padding(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Top
                ) {

                    Text(
                        text = stringResource(R.string.check_title),
                      //  color = MaterialTheme.colorScheme.outline,
                        fontSize = MaterialTheme.typography.titleLarge.fontSize
                    )


                    Spacer(modifier = Modifier.height(16.dp))
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.95f),
                        text = checkNbr,
                        requestFocus = requestFocus,
                        errorValue = checkNbrError?.asString(),
                        label = stringResource(R.string.check_number_field_title),
                        onValueChange = {
                            onCheckNbrChange(it)
                        },
                        readOnly = false,
                        //   enabled = distNumViewModel.modifyVisite,
                        leadingIcon = Icons.Default.Home,
                        showLeadingIcon  = false,
                        showTrailingIcon = true,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next,
                        onKeyboardActions = {
                            //  scope.launch {
                            //      sheetState.hide()
                            //  }
                            //   onDismissRequest()
                        },
                    )
                    Spacer(modifier = Modifier.height(9.dp))
                    EditTextField(
                        modifier = Modifier.fillMaxWidth(0.95f),
                        text = if(montantCheque=="0.0") "" else montantCheque,
                        errorValue = montantChequeError?.asString(),
                        label = stringResource(R.string.amount_field_title),
                        onValueChange = {
                            onMontantChequeChange(it)
                        },
                        readOnly = false,
                        //   enabled = distNumViewModel.modifyVisite,
                        leadingIcon = Icons.Default.Home,
                        showLeadingIcon  = false,
                        showTrailingIcon = true,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next,
                        onKeyboardActions = {
                        },
                    )
                    Spacer(modifier = Modifier.height(9.dp))

                    EditTextFieldClickable(
                        modifier = Modifier.fillMaxWidth(0.95f),
                        onClickTrailingIcon = {
                            onShowDatePickerChange(!showDatePicker)
                        },
                        text = checkDeadline,
                        errorValue = checkDeadlineError?.asString(),
                        label = stringResource(R.string.deadline_field_title),
                        onValueChange = {
                           // onMontantChequeChange(it)
                        },
                        readOnly = false,
                        //   enabled = distNumViewModel.modifyVisite,
                        leadingIcon = Icons.Default.Home,
                        trailingIcon = Icons.TwoTone.DateRange,
                        showLeadingIcon  = false,
                         showTrailingIcon = true,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next,
                        onKeyboardActions = {
                        },
                    )




                    Spacer(modifier = Modifier.height(9.dp))
                    GenericDropdownMenu (
                        modifier = Modifier.fillMaxWidth(0.95f),
                        fiterValue = chequesFilter,
                        showFilter = true,
                        onFilterValueChange = { chequesFilter = it },
                        designation =  selectedBanque.bANDes?:"",
                        errorValue = selectedBanqueError?.asString(),
                        label = stringResource(R.string.banque_title),
                        readOnly =  true,
                        itemExpanded = isBanqueExpanded,
                        itemList = if(chequesFilter.isNotEmpty()) bankList.filter { it.bANDes?.lowercase(Locale.ROOT)?.contains(chequesFilter.lowercase(Locale.ROOT)) == true } else bankList,

                        selectedItem = selectedBanque,
                        getItemTrailing = { it.bANCode },
                        getItemDesignation = { it.bANDes?: it.bANCode },
                        onClick = {
                            onSelectedBanqueChange(it)
                            onBanqueExpandChange(false)
                        },
                        onItemExpandedChange = {
                            onBanqueExpandChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                    lottieAnimError = {
                        LottieAnim(lotti = R.raw.connection_error, size = it)
                    }
                    )




                    Spacer(modifier = Modifier.height(15.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceAround,
                        verticalAlignment = Alignment.CenterVertically
                    ) {




                        OutlinedButton(
                            onClick = {
                                onDismissRequest()
                            },

                        ) {
                            Text(text = stringResource(R.string.quitter))
                        }


                        Button(
                            enabled = montantCheque!="" && checkNbr!=""
                                    && checkDeadline!=""
                                    &&  selectedBanque!= Banque(),
                            onClick = {
                                scope.launch {
                                    sheetState.hide()
                                }
                                onAddClicked()
                            }
                        ) {
                            Text(text = stringResource(R.string.OK))
                        }
                    }
                }
            }

        }
    )
            }


data class CheckPayment(
   val montantCheque :String = "",
   val  montantChequeError :String? = "",
   val checkNbr: String = "",
   val   checkNbrError: String? = "",
   val    checkDeadline : String = "",
   val   checkDeadlineError : String? = "",
   val    selectedBanque : Banque = Banque(),
   val  selectedBanqueError : String? = ""
)


