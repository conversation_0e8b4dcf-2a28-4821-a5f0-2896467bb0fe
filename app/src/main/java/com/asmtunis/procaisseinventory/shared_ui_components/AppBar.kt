package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Menu
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppBar(
    titleVisibilty : Boolean = true,
    baseConfig: BaseConfig = BaseConfig(),
    isConnected : Boolean,
    title: String = stringResource(id = R.string.app_name),
    showNavIcon: Boolean = true,
    navIcon: ImageVector = Icons.TwoTone.Menu,
    onNavigationClick: () -> Unit = {},
    actions: @Composable (RowScope.() -> Unit) = {}
) {

    TopAppBar(
        title = {
            AnimatedVisibility(
                visible = titleVisibilty,
                enter = fadeIn() + slideInVertically(),
                exit = fadeOut() + slideOutVertically()
            ) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (!isConnected) {
                        LottieAnim(lotti = R.raw.no_connection, size = 30.dp)
                        Spacer(modifier = Modifier.width(12.dp))
                    }

                    if(baseConfig.designation_base == Globals.DEMO_BASE_CONFIG) {
                        LottieAnim(lotti = R.raw.free_trial, size = 30.dp)
                        Spacer(modifier = Modifier.width(12.dp))
                    }


                    Text(text = title, textAlign = TextAlign.Center, fontSize = MaterialTheme.typography.titleSmall.fontSize)


                }
            }

        },
        navigationIcon = {
            if (showNavIcon){
                IconButton(
                    onClick = onNavigationClick
                ) {
                    Icon(
                        imageVector = navIcon ,
                        contentDescription = stringResource(
                            id = R.string.cd_toggle_drawer

                        )
                    )
                }
            }
        },
        actions = {
            actions()
        }
    )
}
