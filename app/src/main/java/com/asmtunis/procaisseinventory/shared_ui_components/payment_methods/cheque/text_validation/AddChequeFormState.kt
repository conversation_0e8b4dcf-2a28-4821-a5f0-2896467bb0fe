package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.cheque.text_validation

import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque


data class AddChequeFormState(

    val numeroCheque: String = "",
    val numeroChequeError: UiText? = null,

    val echeance: String = "",
    val echeanceError: UiText? = null,

    val bank: Banque = Banque(),
    val bankError: UiText? = null,

    val montant: String = "",
    val montantError: UiText? = null,



    )
