package com.asmtunis.procaisseinventory.shared_ui_components.text_animation

import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import kotlinx.coroutines.delay
import java.text.BreakIterator
import java.text.StringCharacterIterator

@Composable
fun LetterByLetterAnimatedText(
    text: String,
    color: Color = Color.Unspecified,
    style: TextStyle = LocalTextStyle.current) {

    // Iterate over the characters.
    val breakIterator = remember(text) { BreakIterator.getCharacterInstance() }

    // Define the duration (milliseconds) of the pause before each successive
    // character is displayed. These pauses between characters create the
    // illusion of an animation.
    val typingDelayInMs = 50L

    var substringText by remember {
        mutableStateOf("")
    }
    LaunchedEffect(text) {
        // Initial start delay of the typing animation
       // delay(200)
        breakIterator.text = StringCharacterIterator(text)

        var nextIndex = breakIterator.next()
        // Iterate over the string, by index boundary
        while (nextIndex != BreakIterator.DONE) {
            substringText = text.subSequence(0, nextIndex).toString()
            // Go to the next logical character boundary
            nextIndex = breakIterator.next()
            delay(typingDelayInMs)
        }
    }
    Text(text = substringText, color = color, style = style)
}