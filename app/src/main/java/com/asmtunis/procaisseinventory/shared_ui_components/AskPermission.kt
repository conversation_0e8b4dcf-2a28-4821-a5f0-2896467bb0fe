package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.runtime.Composable
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.MultiplePermissionsState
import com.google.accompanist.permissions.rememberMultiplePermissionsState


@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun AskPermission(
    permission: List<String>,
    permissionNotAvailableContent: @Composable (permissionState: MultiplePermissionsState) -> Unit = { },
    content: @Composable () -> Unit = { }
) {
    val permissionState = rememberMultiplePermissionsState(permission)


    if (permissionState.allPermissionsGranted) {
        content()
    }
    else {
        val allPermissionsRevoked =
            permissionState.permissions.size == permissionState.revokedPermissions.size

        if(!allPermissionsRevoked){
            content()
            //SEE THIS LINk https://github.com/google/accompanist/blob/main/sample/src/main/java/com/google/accompanist/sample/permissions/RequestLocationPermissionsSample.kt
        }
        else {

            permissionNotAvailableContent(permissionState)

        }
    }


}
