package com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticleWithStation
import com.simapps.ui_kit.ModifiersUtils.detectTapGestures
import kotlinx.coroutines.delay


@Composable
fun ThreeColumnTable(
    article: Article,
    showFilterLine: Boolean = false,
    onShowFilterLineChange: (Boolean) -> Unit = {},
    fiterValue: String = "",
    onFilterValueChange: (String) -> Unit = {},
    stationStockArtWithStation: List<StationStockArticleWithStation>,
    onPress: (StationStockArticleWithStation) -> Unit = {},
    onLongPress: (StationStockArticleWithStation) -> Unit = {},
    rowTitls: List<String> = listOf("Stations", "Quantité"),
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val listState = rememberLazyListState()
    val nbrOfLines = stationStockArtWithStation.size

    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(key1 = focusRequester, key2 = showFilterLine) {
        if(!showFilterLine) return@LaunchedEffect
        focusRequester.requestFocus()
        delay(100) // Make sure you have delay here
        keyboardController?.show()

    }


    HorizontalDivider(color = MaterialTheme.colorScheme.outline)
    ThreeTableHeaderTitles(
        rowTitls = rowTitls,
        showFilterLine = showFilterLine,
        onShowFilterLineChange = { onShowFilterLineChange(it) }
    )


    AnimatedVisibility(
        visible = showFilterLine,
        enter = slideInVertically {
            with(density) { 40.dp.roundToPx() }
        } + fadeIn(),
        exit = fadeOut(
            animationSpec = keyframes {
                this.durationMillis = 120
            }
        )
    ) {

        OutlinedTextField(
            value = fiterValue,
            onValueChange = onFilterValueChange,
            label = { Text(stringResource(id = R.string.filter) + " ($nbrOfLines linges)") },
            modifier = Modifier
                .focusRequester(focusRequester)
                .fillMaxWidth()
                .padding(18.dp),
            trailingIcon = {
                Row(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AnimatedVisibility(
                        visible = fiterValue.isNotEmpty(),
                        enter = fadeIn(),
                        exit = fadeOut(),
                    ) {
                        IconButton(onClick = { onFilterValueChange("") }) {
                            Icon(
                                imageVector = Icons.Filled.Clear,
                                contentDescription = stringResource(id = R.string.your_divice_id),
                            )
                        }
                    }


                }
            },
            keyboardActions = KeyboardActions(
                onSearch = {
                    keyboardController?.hide()
                }
            ),
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Search,
                keyboardType = KeyboardType.Password
            )
        )


    }
    HorizontalDivider(color = MaterialTheme.colorScheme.outline)

    LazyColumn(
        // modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        state = listState,
    ) {
        items(
            //  count = selectedPatrimoineList.size,
            items = stationStockArtWithStation,
            key = { item -> item.station?.sTATCode?:item.stationStockArticle!! }
        ) { item ->
           // val qte = removeTrailingZeroInDouble(convertStringToDoubleFormat(input =item.stationStockArticle?.sARTQte?:"0"))
            val qte = removeTrailingZeroInDouble(item.stationStockArticle?.sARTQteStation?:"0").ifEmpty { "0" }
            val unite = article.uNITEARTICLECodeUnite?:""


            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start,
                    modifier =
                    Modifier
                        .padding(start = 3.dp, end = 3.dp)
                        .heightIn(40.dp, 150.dp)
                        .fillMaxWidth()
                        .detectTapGestures(
                            key1 = item,
                            onPress = {
                                //  selectArtPatrimoineVM.setSelectedPat(selectedArticle[index])

                            },
                            onDoubleTap = {
                            },
                            onLongPress = {
                                onLongPress(item)
                            },
                            onTap = {
                                //  mainViewModel.setAddNewProductDialogueVisibility(true)
                                onPress(item)
                            },
                        ),
                ) {
                    Text(
                        text = item.station?.sTATDesg?:item.stationStockArticle!!.sARTCodeSatation,
                        fontSize = MaterialTheme.typography.bodySmall.fontSize,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(0.4f),
                    )

                    Text(
                        text = "$qte $unite",
                        textAlign = TextAlign.Center,
                        fontSize = MaterialTheme.typography.bodySmall.fontSize,
                        modifier = Modifier.fillMaxWidth(),
                    )
                }

                HorizontalDivider(color = MaterialTheme.colorScheme.outline)
            }
            //  }
            //  )


        }
    }


}