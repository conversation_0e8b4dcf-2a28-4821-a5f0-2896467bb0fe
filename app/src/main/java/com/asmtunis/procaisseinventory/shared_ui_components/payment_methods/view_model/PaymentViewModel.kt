package com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.utils.mobilecode.MobileCodeGeneration
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisse
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentUtils.getPaymentMode
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import javax.inject.Inject


@OptIn(SavedStateHandleSaveableApi::class)
    @HiltViewModel
    class PaymentViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proCaisseRemote: ProCaisseRemote,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        savedStateHandle : SavedStateHandle
    ) : ViewModel() {
        var codeM by savedStateHandle.saveable { mutableStateOf("") }
            private set
      //  var codeM: String by mutableStateOf("")
      //      private set
        fun generateCodeM(utilisateur: Utilisateur, prefix: String) {
          val stationUtilisateur = utilisateur.Station
          val userID = utilisateur.codeUt

          codeM = prefix+"_" + stationUtilisateur + "_" + userID  + "_" + MobileCodeGeneration.generateCommonCode(
              num =  Math.random().toInt().toString(),
              numLign = Math.random().toInt().toString())


          codeM = codeM.replace("__", "_")

          Log.d("sdfsfsfezfsfsf", "codeMc " + codeM)
        }






    var requestFocus by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onRequestFocusChange(value: Boolean) {
        requestFocus = value

    }
        var selectedListChequeCaisse = mutableStateListOf<ChequeCaisse>()
          private set


        fun onSelectedChequeCaisseListChange(chequeCaisse: ChequeCaisse) {
            removeChequeCaisse(chequeCaisse)
            selectedListChequeCaisse.add(chequeCaisse)

        }

         fun resetPaymentsValue() {

               montantTotalCheques =  ""
               montantTotalTicketResto = ""
             cashValue = ""

             selectedListChequeCaisse.clear()
             selectedTicketRestoList.clear()
         }


           var selectedTicketRestoList= mutableStateListOf<TraiteCaisse>()
           private set


        fun onSelectedTicketRestoListChange(traiteCaisse: TraiteCaisse) {
            removeTraiteCaisse(traiteCaisse)
            selectedTicketRestoList.add(traiteCaisse)



        }
        fun removeTraiteCaisse(traiteCaisse: TraiteCaisse){
            if(selectedTicketRestoList.isNotEmpty())     selectedTicketRestoList.removeIf { it.tRAITCompteLocal == traiteCaisse.tRAITCompteLocal}
        }

        fun removeChequeCaisse(chequeCaisse: ChequeCaisse){
            if(selectedListChequeCaisse.isNotEmpty())     selectedListChequeCaisse.removeIf { it.numCheque == chequeCaisse.numCheque}
        }

        var restValue by savedStateHandle.saveable { mutableStateOf("") }
            private set
      //  var restValue by mutableStateOf("")

        fun onRestValueChange(cltSolde: String) {
            val rest   = stringToDouble(cltSolde) + stringToDouble(cashValue) + stringToDouble(montantTotalCheques) + stringToDouble(montantTotalTicketResto)

            restValue = rest.toString()
        }


        var restPaymentValue by savedStateHandle.saveable { mutableStateOf("") }
            private set
        fun onRestPaymentValueChange(total: Double) {

            val rest = total - stringToDouble(cashValue) - stringToDouble(montantTotalCheques) - stringToDouble(montantTotalTicketResto)
       //  restPaymentValue = convertStringToDoubleFormat(rest.toString())

           // restPaymentValue = if(rest< 0.0001) "0.0" else rest.toString()


          //  restPaymentValue = convertDoubleToDoubleFormat(rest)
            restPaymentValue = rest.toString()

        }


        var paymentType by savedStateHandle.saveable { mutableStateOf("") }
            private set
        fun onPaymentTypeChange(value: String) {

            paymentType = value
        }


        var montantTotalCheques by savedStateHandle.saveable { mutableStateOf("") }
            private set
        //var montantTotalCheques by mutableStateOf("")
        fun claculateMontantTotalCheques(){
            //  viewModelScope.launch {
            // snapshotFlow { selectedListChequeCaisse.size }
            //  .distinctUntilChanged()
            //  .filter { it.data != null }
            // .collectLatest {
            var total = 0.0
            if(selectedListChequeCaisse.isNotEmpty())   for (chequeCaisse in selectedListChequeCaisse){
                total += stringToDouble(chequeCaisse.montant)
            }

            montantTotalCheques= total.toString()
            //  }
            //    }


        }


        var montantTotalTicketResto by savedStateHandle.saveable { mutableStateOf("") }
            private set
        //var montantTotalTicketResto by mutableStateOf("")
        fun claculateMontantTotalTicketResto(){
            var total = 0.0
            if(selectedTicketRestoList.isNotEmpty())   for (ticketResto in selectedTicketRestoList){
                total += ticketResto.tRAITMontant
            }

            montantTotalTicketResto= total.toString()
        }


        fun calculateAmountTTCNet(amount: Double, discount: Double, nbr : Int): Double {
            return (amount -((discount * amount) / 100.0))*nbr
        }

        var cashValue by savedStateHandle.saveable { mutableStateOf("") }
            private set
       // var cashValue by mutableStateOf("")

        fun onCashValueChange(state: String) {
            cashValue = state
        }



        var showChequeDetail by savedStateHandle.saveable { mutableStateOf(false) }
            private set
        // var echeance by mutableStateOf("")

        fun onShowChequeDetailChange(state: Boolean) {
            showChequeDetail = state
        }

        var showTicketRestoDetail by savedStateHandle.saveable { mutableStateOf(false) }
            private set
        // var echeance by mutableStateOf("")

        fun onShowTicketRestoDetailChange(state: Boolean) {
            showTicketRestoDetail = state
        }
        var numeroCheque by savedStateHandle.saveable { mutableStateOf("") }
            private set
        //var numeroCheque by mutableStateOf("")

        fun onNumeroChequeChange(state: String) {
            numeroCheque = state
        }

        var echeance by savedStateHandle.saveable { mutableStateOf("") }
            private set
       // var echeance by mutableStateOf("")

        fun onEcheanceChange(state: String) {
            echeance = state
        }


        var montantCheque by savedStateHandle.saveable { mutableStateOf("") }
            private set
       // var montantCheque by mutableStateOf("")

        fun onMontantChequeChange(state: String) {
            montantCheque = state
        }

        var bank : Banque by  mutableStateOf(Banque())
            private set
       // var bank by mutableStateOf(Banque())

        fun onBankChange(state: Banque) {
            bank = state
        }
        //  var chequeCaisse by mutableStateOf(ChequeCaisse())





        fun resetChequeVariable(){
            bank = Banque()
            echeance = ""
            montantCheque = ""
            numeroCheque = ""
        }

        var banqueExpand by savedStateHandle.saveable { mutableStateOf(false) }
            private set
        //var banqueExpand by mutableStateOf(false)

        fun onBanqueExpandChange(state: Boolean) {
            banqueExpand = state
        }

        var showPaimentEspece by savedStateHandle.saveable { mutableStateOf(false) }
            private set
       // var showPaimentEspece by mutableStateOf(false)

        fun onShowPaimentEspeceChange(state: Boolean) {
            showPaimentEspece = state
        }

        var showPaimentCheque by savedStateHandle.saveable { mutableStateOf(false) }
            private set
     //   var showPaimentCheque by mutableStateOf(false)

        fun onShowPaimentChequeChange(state: Boolean) {
            showPaimentCheque = state
        }

        var showPaimentTicketResto by savedStateHandle.saveable { mutableStateOf(false) }
            private set
     //   var showPaimentTicketResto by mutableStateOf(false)

        fun onShowPaimentTicketRestoChange(state: Boolean) {
            showPaimentTicketResto = state
        }

        var montantTicketResto by savedStateHandle.saveable { mutableStateOf("") }
            private set
       // var montantTicketResto by mutableStateOf("")

        fun onMontantTicketRestoChange(state: String) {
            montantTicketResto = state
        }

        var ticketRestoNbr by savedStateHandle.saveable { mutableStateOf("") }
            private set
      //  var ticketRestoNbr by mutableStateOf("")

        fun onTicketRestoNbrChange(state: String) {
            ticketRestoNbr = state
        }

        var tauxTicketResto by savedStateHandle.saveable { mutableStateOf("") }
            private set
      //  var tauxTicketResto by mutableStateOf("")

        fun onTauxTicketRestoChange(state: String) {
            tauxTicketResto = state
        }

        var selectedTicketResto by mutableStateOf(CarteResto())
            private set
      //  var selectedTicketResto by mutableStateOf(CarteResto())

        fun onSelectedTicketRestoChange(state: CarteResto) {
            selectedTicketResto = state
        }


        fun resetTicketRestoVariable(){
            selectedTicketResto = CarteResto()
            tauxTicketResto = ""
            ticketRestoNbr = ""
            montantTicketResto = ""
        }

        var ticketRestoExpand by savedStateHandle.saveable { mutableStateOf(false) }
            private set
       // var ticketRestoExpand by mutableStateOf(false)

        fun onTicketRestoExpandChange(state: Boolean) {
            ticketRestoExpand = state
        }





        fun savePayment(
            tIKMtTTC: String,
            isRegPart: Boolean = false,
            codeM: String,
            canSync: Boolean,
            sessionCaisse: SessionCaisse,
            client: Client,
            user: Utilisateur,
            exercice: String,
            rEGCNumTicket: Long? = 0,
            regRemarque: String,
            dateTime: String = getCurrentDateTime(),
        ){
            val regMntTotal = stringToDouble(montantTotalCheques)+stringToDouble(montantTotalTicketResto)+stringToDouble(cashValue)

            val rest = calculateRest(
                receivedTotal = regMntTotal,
                amount = stringToDouble(tIKMtTTC),
                isCredit = false
            )

            val reglement = ReglementCaisse(
                rEGCCode = codeM,
                rEGCCode_M = codeM,
                rEGCExercice = exercice,
                rEGCIdCarnet = sessionCaisse.sCIdCarnet,
                rEGCNumTicket = rEGCNumTicket.toString(),
                rEGCIdSCaisse = sessionCaisse.sCIdSCaisse,
                rEGNumTicketPart = if(isRegPart) rEGCNumTicket.toString() else "0",
                rEGCIdCaisse = sessionCaisse.sCIdCarnet,
                rEGCIdStation = sessionCaisse.sCStation,
                rEGCCodeClient = client.cLICode,
                rEGCNomPrenom = client.cLINomPren,
                rEGCModeReg = getPaymentMode(
                    montantTotalCheques = stringToDouble(montantTotalCheques),
                    montantTotalTicketResto =  stringToDouble(montantTotalTicketResto),
                    cashValue = stringToDouble(cashValue)
                ),
                rEGCDateReg = dateTime,
                rEGCMntEspece = stringToDouble(cashValue),
                rEGCMntCarteBancaire = 0.0,
                rEGCMntCheque = stringToDouble(montantTotalCheques),
                rEGCMntTraite = stringToDouble(montantTotalTicketResto),
                rEGCRemarque = regRemarque,
                rEGCMontant = regMntTotal,
                rEGCStation = sessionCaisse.sCStation,//user.Station,
                rEGCUser = user.codeUt,
                rEGCMntTotalRecue = regMntTotal,
                rEGCMntEspeceRecue = stringToDouble(cashValue),
                rest = rest,
                made = stringToDouble(restValue),
                canSync = canSync
            )
            //if(id!=-1) reglement.id = id
            reglement.isSync = false
            reglement.status = if(!canSync) ItemStatus.INSERTED.status else ItemStatus.INSERTED_REG_FROM_REGLEMENT.status

            viewModelScope.launch(dispatcher) {
                proCaisseLocalDb.reglementCaisse.upsert(reglement)
                proCaisseLocalDb.ticketResto.upsertAll(selectedTicketRestoList.toList())
                proCaisseLocalDb.chequeCaisse.upsertAll(selectedListChequeCaisse.toList())



            }
            if (rEGCNumTicket==0L) { //only update sold clt if is reglement Libre
                updateClientSold(client = client)
            }


      /*    val ticketWithLinesAndPayments = TicketWithLinesAndPayments(
              ticket = null,
             ligneTicket = null,
             cheques = selectedListChequeCaisse.toList(),
             traites = selectedTicketRestoList.toList(),
             reglement = reglement
          )*/
        }



        private fun updateClientSold(client: Client) {
            viewModelScope.launch(dispatcher) {
                proCaisseLocalDb.clients.updateSoldClient(codeClt = client.cLICode, soldClient = restValue)
            }
        }

   private fun calculateRest(
       receivedTotal: Double,
        amount: Double,
        isCredit: Boolean
    ): Double {

        val rest = if (isCredit) amount + receivedTotal else amount - receivedTotal
        return if (isCredit) {
            if (rest < 0) rest else 0.0
        } else {
            if (rest > 0) rest else 0.0
        }
    }
    fun deleteReglement(reglementCaisseWithTicketAndClient: ReglementCaisseWithTicketAndClient) {

        viewModelScope.launch(dispatcher) {
            val reglementCaisse = reglementCaisseWithTicketAndClient.reglementCaisse?: return@launch

            proCaisseLocalDb.reglementCaisse.deleteByCode(
                code = reglementCaisse.rEGCCode_M,
                exercice = reglementCaisse.rEGCExercice,
                idCaisse = reglementCaisse.rEGCIdSCaisse
            )

            proCaisseLocalDb.ticketResto.deleteByCodeM(
                codeM = reglementCaisse.rEGCCode_M,
                exercice = reglementCaisse.rEGCExercice
            )
            proCaisseLocalDb.chequeCaisse.deleteByCodeM(
                codeM = reglementCaisse.rEGCCode_M,
                exercice = reglementCaisse.rEGCExercice
            )


          if(reglementCaisse.rEGCCodeClient != null)
            proCaisseLocalDb.clients.updateSoldClient(codeClt = reglementCaisse.rEGCCodeClient, soldClient = ((reglementCaisse.rEGCMntTotalRecue?:0.0) * (-1)).toString())

        }
    }
    
    
    
    fun addTicketRestoPayment(
        codeM: String,
        client: Client,
        exerciceList: List<Exercice>,
        sessionCaisse: SessionCaisse
    ) {

        val traiteCaisse = TraiteCaisse(
            tRAITNum = codeM,
            tRAITOrdre = selectedTicketRestoList.size + 1,
            tRAITNUM_M = codeM,
            tRAITReglement = codeM,//"tRAITReglement",
            tRAITIdSession = sessionCaisse.sCIdSCaisse,
            tRAITExercice = exerciceList.first().exerciceCode,
            tRAITEcheance = getCurrentDateTime(),//TODO SEE IF ADD SELECT CUSTOM DATE
            tRAITClient = client.cLINomPren,
            tRAITMontant = calculateAmountTTCNet(
                amount = stringToDouble(montantTicketResto),
                discount = stringToDouble(tauxTicketResto),
                nbr = ticketRestoNbr.toInt()
            ),
            tRAITType = selectedTicketResto.societe,
            tRAITCompteLocal = selectedTicketResto.code
        )

        traiteCaisse.isSync = false
        traiteCaisse.status = ItemStatus.INSERTED.status

        traiteCaisse.taux = stringToDouble(tauxTicketResto)
        traiteCaisse.montantInitial = stringToDouble(montantTicketResto)
        traiteCaisse.nbrTicket = ticketRestoNbr.toInt()

        onSelectedTicketRestoListChange(traiteCaisse)
        onShowPaimentTicketRestoChange(false)
        claculateMontantTotalTicketResto()
        resetTicketRestoVariable()
    }
    
    
    fun addPaimentCheque (
        codeM: String,
        client: Client,
        utilisateur: Utilisateur,
        exerciceList: List<Exercice>,
        sessionCaisse: SessionCaisse
    ) {
        val chequeCaisse = ChequeCaisse(
            numCheque = numeroCheque,
            reglement = codeM,
            reglementM = codeM,
            codeUtilisateur = client.cLICode,
            reglementExercice = exerciceList.first().exerciceCode,
            reglementidsession = sessionCaisse.sCIdSCaisse,
            echeanceCheque = echeance,
            banque = bank.bANDes,
            montant = montantCheque

        )
        chequeCaisse.isSync = false
        chequeCaisse.status = ItemStatus.INSERTED.status
        onSelectedChequeCaisseListChange(chequeCaisse)
        onShowPaimentChequeChange(false)

        claculateMontantTotalCheques()
        resetChequeVariable()  
    }
    
    
    fun addChequeCaisse(
        client: Client,
        exerciceList: List<Exercice>,
        sessionCaisse: SessionCaisse
    ) {
        val chequeCaisse = ChequeCaisse(
            numCheque = numeroCheque,
            reglement = codeM,
            reglementM = codeM,
            codeUtilisateur = client.cLICode,
            reglementExercice = exerciceList.first().exerciceCode,
            reglementidsession = sessionCaisse.sCIdSCaisse,
            echeanceCheque = echeance,
            banque = bank.bANDes,
            montant = montantCheque

        )
        chequeCaisse.isSync = false
        chequeCaisse.status = ItemStatus.INSERTED.status
        onSelectedChequeCaisseListChange(chequeCaisse)
        onShowPaimentChequeChange(false)

        claculateMontantTotalCheques()
        resetChequeVariable()
    }
    
    fun addTraiteCaisse(
        sessionCaisse: SessionCaisse,
        client: Client,
        exerciceList: List<Exercice>
    ) {


        val traiteCaisse = TraiteCaisse(
            tRAITNum = codeM,
            tRAITOrdre = selectedTicketRestoList.size+1,
            tRAITNUM_M = codeM,
            tRAITReglement = codeM,
            tRAITIdSession = sessionCaisse.sCIdSCaisse,
            tRAITExercice = exerciceList.first().exerciceCode,
            tRAITEcheance = getCurrentDateTime(),//TODO SEE IF ADD SELECT CUSTOM DATE
            tRAITClient = client.cLINomPren,
            tRAITMontant = calculateAmountTTCNet(
                amount = stringToDouble(montantTicketResto),
                discount = stringToDouble(tauxTicketResto),
                nbr = ticketRestoNbr.toInt()
            ),
            tRAITType = selectedTicketResto.societe,
            tRAITCompteLocal = selectedTicketResto.code
        )

        traiteCaisse.isSync = false
        traiteCaisse.status = ItemStatus.INSERTED.status

        traiteCaisse.taux = stringToDouble(tauxTicketResto)
        traiteCaisse.montantInitial = stringToDouble(montantTicketResto)
        traiteCaisse.nbrTicket = ticketRestoNbr.toInt()

        onSelectedTicketRestoListChange(traiteCaisse)
        onShowPaimentTicketRestoChange(false)
        claculateMontantTotalTicketResto()
        resetTicketRestoVariable()
    }
    
    }