@file:OptIn(ExperimentalMaterial3Api::class)
package com.asmtunis.procaisseinventory.shared_ui_components.lazy_column

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun <T> PullToRefreshLazyVerticalStaggeredGrid(
    items: List<T>,
    isRefreshing: Boolean,
    pullToRefreshEnabled: Boolean = true,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
    lazyListState: LazyStaggeredGridState = rememberLazyStaggeredGridState(),
    key: (Int) -> Any,
    content: @Composable (Int) -> Unit,
) {
    val state = rememberPullToRefreshState()
 /*   val pullToRefreshState = rememberPullToRefreshState(enabled = { pullToRefreshEnabled })
    Box(
        modifier = modifier.nestedScroll(pullToRefreshState.nestedScrollConnection)
    ) {



        LazyVerticalStaggeredGrid(
            state = lazyListState,
            contentPadding = PaddingValues(12.dp),
            columns = StaggeredGridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalItemSpacing = 16.dp,
        ) {
            items(count = items.size,
                key = key
            ) {
                content(it)
            }
        }

        if(pullToRefreshState.isRefreshing) {
            LaunchedEffect(true) {
                onRefresh()
            }
        }

        LaunchedEffect(isRefreshing) {
            if(isRefreshing) {
                pullToRefreshState.startRefresh()
            } else {
                pullToRefreshState.endRefresh()
            }
        }

        if(pullToRefreshState.verticalOffset > 0F)
        PullToRefreshContainer(
            state = pullToRefreshState,
            modifier = Modifier.align(Alignment.TopCenter),
        )
    }*/

    PullToRefreshBox(
        //  modifier = Modifier.padding(it),
        state = state,
        isRefreshing = isRefreshing,
        onRefresh = { if(pullToRefreshEnabled) onRefresh() },
    ) {
        LazyVerticalStaggeredGrid(
            state = lazyListState,
            contentPadding = PaddingValues(12.dp),
            columns = StaggeredGridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalItemSpacing = 16.dp,
        ) {
            items(count = items.size,
                key = key
            ) {
                content(it)
            }
        }
    }
}