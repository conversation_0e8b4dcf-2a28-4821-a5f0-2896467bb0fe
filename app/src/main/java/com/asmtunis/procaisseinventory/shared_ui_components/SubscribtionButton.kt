package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig

@Composable
fun SubscribtionButton(
    productName: String,
    isActivated: () -> <PERSON><PERSON><PERSON>,
    isSubscriptionSent: () -> Boolean,
    isDemo: Boolean,
    selectedBaseconfig: BaseConfig,
    onClick: () -> Unit
) {
    if (!isActivated() || (isDemo && selectedBaseconfig.licences.any { it.produit == productName })) {
        OutlinedButton(
            enabled = !isSubscriptionSent(),
            onClick = onClick,
            modifier = Modifier.fillMaxWidth(),
            shape = MaterialTheme.shapes.medium
        ) {
            LottieAnim(lotti = R.raw.homeshop, size = 25.dp)
            Spacer(Modifier.width(9.dp))
            Text(
                text = when {
                    isDemo -> stringResource(id = R.string.get_licence) + " $productName"
                    !isSubscriptionSent() -> stringResource(id = R.string.get_licence) + " $productName"
                    else -> stringResource(id = R.string.demande_licence_envoyer)
                }
            )
        }
        Spacer(Modifier.height(9.dp))
    }
}