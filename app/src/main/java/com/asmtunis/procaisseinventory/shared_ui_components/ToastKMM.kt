package com.asmtunis.procaisseinventory.shared_ui_components

import android.content.Context
import android.os.Build
import android.widget.Toast
import androidx.compose.runtime.Composable
import com.dokar.sonner.ToastType
import com.dokar.sonner.Toaster
import com.dokar.sonner.ToasterDefaults
import com.dokar.sonner.ToasterState
import kotlin.time.Duration

@Composable
fun ToastKMM(
    toaster: ToasterState,
    maxVisibleToasts:Int = 1,
    richColors:Boolean = true,
    darkTheme:Boolean = true,
    showCloseButton:Boolean = true,
    expanded:Boolean = true
) {

    Toaster(
        state = toaster,
        maxVisibleToasts = maxVisibleToasts,
        richColors = richColors,
        darkTheme = darkTheme,
        showCloseButton = showCloseButton,
        expanded = expanded
    )



}

fun showToast(
    context: Context,
    toaster: ToasterState,
    message: Any,
    type: ToastType = ToastType.Normal,
    duration: Duration = ToasterDefaults.DurationLong,
    action: Any? = null
) {
    if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        toaster.show(
            message = message,
            type =  type,
            duration = duration,
            action = action
        )
    }
    else  Toast.makeText(context, message as String , Toast.LENGTH_SHORT).show()


}