package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.material3.FabPosition
import androidx.compose.material3.windowsizeclass.WindowSizeClass
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass

fun floatingActionButtonPosition(windowSize: WindowSizeClass): FabPosition =
    when (windowSize.widthSizeClass) {
        WindowWidthSizeClass.Compact -> {
            FabPosition.End
        }
        WindowWidthSizeClass.Medium  -> {
            FabPosition.Start
        }
        WindowWidthSizeClass.Expanded -> {
            FabPosition.Start
        }

        else -> {
            FabPosition.End
        }
    }