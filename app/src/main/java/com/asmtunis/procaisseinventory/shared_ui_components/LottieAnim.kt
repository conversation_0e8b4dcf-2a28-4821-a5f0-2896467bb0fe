package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun LottieAnim(
               modifier: Modifier = Modifier,
               lotti: Int,
               size: Dp = 250.dp,
               iterations: Int = Int.MAX_VALUE,
               restartOnPlay: Boolean = true,
               onClick: () -> Unit = {}
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(lotti))
    val progress by animateLottieCompositionAsState(
        composition = composition,
        restartOnPlay = restartOnPlay,
        iterations = iterations
    )


     LottieAnimation(
        modifier = modifier.size(size).clickable { onClick() },
        composition = composition,
        progress = { progress }
    )
}
