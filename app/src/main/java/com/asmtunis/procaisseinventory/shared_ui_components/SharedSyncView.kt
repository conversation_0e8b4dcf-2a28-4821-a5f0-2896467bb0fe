package com.asmtunis.procaisseinventory.shared_ui_components

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.asmtunis.procaisseinventory.shared_ui_components.buttons.SyncButtons
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels

@SuppressLint("SuspiciousIndentation")
@Composable
fun SharedSyncView (
    isConnected: Boolean,
                    syncSharedViewModels: SyncSharedViewModels
){
    val context = LocalContext.current

    val syncArticlesViewModel = syncSharedViewModels.syncArticlesViewModel
    /**
     * *********************
     *    FAMILLE
     * *********************
     */
        SyncButtons(
            text = "Sync Famille ",
            nbrNotSync = syncArticlesViewModel.familleNotSync.size,
            remoteResponseState = syncArticlesViewModel.addFamilleState,
            emailBody = syncArticlesViewModel.addFamilleSyncObj,
            isConnected = isConnected,
            onClickSync = { syncArticlesViewModel.syncFamille(syncArticlesViewModel.familleNotSync) }
        )

    /**
     * *********************
     *    MARQUE
     * *********************
     */
        SyncButtons(
            text = "Sync Marque ",
            nbrNotSync = syncArticlesViewModel.marqueNotSync.size,
            remoteResponseState = syncArticlesViewModel.marqueState,
            emailBody = syncArticlesViewModel.marqueSyncObj,
            isConnected = isConnected,
            onClickSync = { syncArticlesViewModel.syncMarque(syncArticlesViewModel.marqueNotSync) }
        )

    /**
     * *********************
     *    ARTICLE
     * *********************
     */

    SyncButtons(
        text = "Sync Article ",
        remoteResponseState = syncArticlesViewModel.articleState,
        nbrNotSync = syncArticlesViewModel.articleSync.size,
        emailBody = syncArticlesViewModel.articleSyncObj,
        isConnected = isConnected,
        onClickSync = { syncArticlesViewModel.syncArticle() }
    )

}