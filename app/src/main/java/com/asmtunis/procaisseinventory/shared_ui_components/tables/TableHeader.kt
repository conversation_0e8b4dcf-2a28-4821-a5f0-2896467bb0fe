package com.asmtunis.procaisseinventory.shared_ui_components.tables

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.CloseFullscreen
import androidx.compose.material.icons.twotone.DateRange
import androidx.compose.material.icons.twotone.OpenInFull
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalContentColor
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.asmtunis.procaisseinventory.R
import com.simapps.ui_kit.custom_cards.ItemDetail

@Composable
fun TableHeader(
    onClickShowCalendar:()-> Unit = {},
    date: String,
    canModify: Boolean = false,
    selectedDateTime: String,
    showExpanIcon: Boolean = false,
    expanIconColor: Color = LocalContentColor.current,
    isExpanded: Boolean = false,
    onExpand: () -> Unit = {}
){

    Row(
        modifier = Modifier.fillMaxWidth(),

    ) {
        ItemDetail(
            modifier = if(showExpanIcon)Modifier.fillMaxWidth(0.8f) else Modifier,
            title = stringResource(id = R.string.date_field_title),
            dataText = if(canModify) selectedDateTime.substringBefore(".") else date.substringBefore("."),
            icon = Icons.TwoTone.DateRange,
            onClick = {
                if(canModify) {
                    onClickShowCalendar()
                }

            }
        )

        if(showExpanIcon) {
            IconButton( modifier = Modifier.fillMaxWidth(),
                onClick = { onExpand() }) {
                Icon(
                    imageVector = if(isExpanded) Icons.TwoTone.CloseFullscreen else Icons.TwoTone.OpenInFull,
                    tint = expanIconColor,
                    contentDescription = "",
                )
            }
        }
    }
}
