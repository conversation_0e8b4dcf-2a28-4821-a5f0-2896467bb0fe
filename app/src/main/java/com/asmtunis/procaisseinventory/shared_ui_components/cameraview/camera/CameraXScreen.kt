package com.asmtunis.procaisseinventory.shared_ui_components.cameraview.camera

import android.util.Log
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY
import androidx.camera.core.Preview
import androidx.camera.core.UseCase
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import java.io.File

@ExperimentalPermissionsApi
@ExperimentalCoroutinesApi
@Composable
fun CameraXScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    modifier: Modifier = Modifier,
    onImageFile: (File) -> Unit = { },
    barCodeViewModel: BarCodeViewModel,
) {
    val context = LocalContext.current
    val flashState = barCodeViewModel.flashState

    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    val coroutineScope = rememberCoroutineScope()
    var previewUseCase by remember { mutableStateOf<UseCase>(Preview.Builder().build()) }
    val imageCaptureUseCase by remember {
        mutableStateOf(
            ImageCapture.Builder()
                .setFlashMode(if(flashState) ImageCapture.FLASH_MODE_ON else ImageCapture.FLASH_MODE_OFF)
                .setCaptureMode(CAPTURE_MODE_MAXIMIZE_QUALITY)
                //  .setMaxResolution(Size(60, 40))
                //     .setTargetAspectRatio(AspectRatio.RATIO_16_9)
                // .setTargetResolution(Size(320, 240)) //you must Decrease image size to encode to base64 and store in local db as string
                //  .setTargetResolution(Size(60, 40)) //you must Decrease image size to encode to base64 and store in local db as string
                .build(),
        )
    }

    LaunchedEffect(key1 = previewUseCase, key2 = flashState) {
        val cameraProvider = context.getCameraProvider()
        try {
            // Must unbind the use-cases before rebinding them.
            cameraProvider.unbindAll()

            val cameraSelector: CameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

            val camera =
                cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    previewUseCase,
                    imageCaptureUseCase,
                    //    imageAnalysis
                )
            camera.cameraControl.enableTorch(flashState)
        } catch (ex: Exception) {
            Log.e("CameraCapture", "Failed to bind camera use cases", ex)
        }
    }
    Box(modifier = modifier) {


            CameraPreview(
                modifier = Modifier.fillMaxSize(),
                onUseCase = {
                    previewUseCase = it
                },
            )

        CapturePictureButton(
            modifier =
                Modifier
                    .size(100.dp)
                    .padding(16.dp)
                    .align(Alignment.BottomCenter),
            onClick = {
                coroutineScope.launch {
                    imageCaptureUseCase.takePicture(context.executor).let {
                        onImageFile(it)
                        popBackStack()

                        //  onImageFile(createImageFile(context))

                        //     Log.d("eee","3"+ createImageFile(context).toUri().toString())
                    }
                }
            },
        )


    }
}

