package com.asmtunis.procaisseinventory.shared_ui_components.searchview

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.staggeredgrid.LazyHorizontalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.twotone.ArrowDropDown
import androidx.compose.material.icons.twotone.ArrowDropUp
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R


@ExperimentalMaterial3Api
@Composable
fun FilterSectionComposable (
    title : String,
    modifier: Modifier = Modifier,
    currentFilterLable: String,
    onEvent: (Int) -> Unit,
    onAllEvent: () -> Unit,
    filterCount : Int,
    customFilterCode : (Int) -> String,
    filterLabel: (Int) -> String,
    ) {
     var showCustomFilterSeachSection by rememberSaveable { mutableStateOf(false) }
    val density = LocalDensity.current
    Column(
        modifier = modifier,
    verticalArrangement = Arrangement.Center,
    horizontalAlignment = Alignment.Start,
    ) {

        val currentFilterValueLable = currentFilterLable.ifEmpty { stringResource(id = R.string.tous) }

        Spacer(modifier = Modifier.height(12.dp))
        Row(
            modifier = Modifier.fillMaxWidth().clickable {
                showCustomFilterSeachSection = !showCustomFilterSeachSection
            },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = "$title ($currentFilterValueLable)",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.outline,
                maxLines = 1
            )
            Spacer(modifier = Modifier.weight(1F))
            Icon(
                imageVector = if(showCustomFilterSeachSection) Icons.TwoTone.ArrowDropUp else Icons.TwoTone.ArrowDropDown,
                contentDescription = stringResource(
                    id = R.string.cd_toggle_drawer

                )
            )
            Spacer(modifier = Modifier.padding(end = 12.dp))
        }

          /*  if(filterCount>9) {
             //   Spacer(modifier = Modifier.weight(1f))
               Spacer(modifier = Modifier.width(12.dp))
                Icon(
                    imageVector =  if(showCustomFilterSeachSection) Icons.TwoTone.ShortText else Icons.TwoTone.Expand,
                    contentDescription = "Localized description",
                    modifier = Modifier.size(AssistChipDefaults.IconSize),
                    tint = MaterialTheme.colorScheme.outline

                )
            }*/

      //  }


        AnimatedVisibility(
            visible = filterCount>9 && showCustomFilterSeachSection,
            enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
            exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
        ) {
            LazyHorizontalStaggeredGrid(contentPadding = PaddingValues(6.dp),
                rows = StaggeredGridCells.Fixed(3),
                state =  rememberLazyStaggeredGridState(),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                horizontalItemSpacing = 6.dp,
                modifier = Modifier.height(150.dp)
            ) {
                item() {
                    FilterChip(
                        selected = currentFilterLable == "",
                        onClick = {
                            onAllEvent()
                        },
                        label = { Text(stringResource(id = R.string.tous)) },
                        leadingIcon = {
                            AnimatedVisibility(visible = currentFilterLable == "") {
                                Icon(
                                    Icons.Filled.Done,
                                    contentDescription = title,
                                    Modifier.size(AssistChipDefaults.IconSize)
                                )
                            }
                        }
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                }
                items(
                    //   items= filteredList.keys.toList()
                    count = filterCount,
                    key = { customFilterCode(it) + it }

                ) { index ->
                    FilterChip(
                        selected = currentFilterLable == customFilterCode(index),
                        onClick = {
                            onEvent(index)
                        },
                        label = { Text(filterLabel(index)) },
                        leadingIcon = {
                            AnimatedVisibility(visible = currentFilterLable == customFilterCode(index)) {
                                Icon(
                                    Icons.Filled.Done,
                                    contentDescription = stringResource(id =R.string.filter_by, filterLabel(index)),
                                    Modifier.size(AssistChipDefaults.IconSize)
                                )
                            }
                        }
                    )

                    Spacer(modifier = Modifier.width(6.dp))
                }
            }
        }

        AnimatedVisibility(
            visible = filterCount <= 9 && showCustomFilterSeachSection,
            enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(),
            exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
        ) {

            LazyRow(
                contentPadding = PaddingValues(6.dp)
            ) {
                item() {
                    FilterChip(
                        selected = currentFilterLable == "",
                        onClick = {
                            onAllEvent()
                        },
                        label = { Text(stringResource(id = R.string.tous)) },
                        leadingIcon = {
                            AnimatedVisibility(visible = currentFilterLable == "") {
                                Icon(
                                    Icons.Filled.Done,
                                    contentDescription = title,
                                    Modifier.size(AssistChipDefaults.IconSize)
                                )
                            }
                        }
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                }
                items(
                    //   items= filteredList.keys.toList()
                    count = filterCount,
                    key = {
                        customFilterCode(it) + it
                    }

                ) { index ->
                    FilterChip(
                        selected = currentFilterLable == customFilterCode(index),
                        onClick = {
                            onEvent(index)
                        },
                        label = { Text(filterLabel(index)) },
                        leadingIcon = {
                            AnimatedVisibility(
                                visible = currentFilterLable == customFilterCode(
                                    index
                                )
                            ) {
                                Icon(
                                    Icons.Filled.Done,
                                    contentDescription = stringResource(
                                        id = R.string.filter_by,
                                        filterLabel(index)
                                    ),
                                    Modifier.size(AssistChipDefaults.IconSize)
                                )
                            }
                        }
                    )

                    Spacer(modifier = Modifier.width(6.dp))
                }
            }

        }


    }

}