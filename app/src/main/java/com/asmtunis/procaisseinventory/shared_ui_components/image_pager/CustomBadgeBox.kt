package com.asmtunis.procaisseinventory.shared_ui_components.image_pager



import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Delete
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint

@Composable
fun CustomBadgeBox(
    modifier :Modifier = Modifier,
    image : ImagePieceJoint,
    onDeleteClick: (ImagePieceJoint) -> Unit,
    page : Int,
    totalPages : Int,
    showIcon : Boolean
) {

     BadgedBox(
        modifier =  modifier,
        badge = {
            Badge {
                Text(
                    text = (page + 1).toString() +"/"+  totalPages,
                    modifier = Modifier.semantics {
                        contentDescription ="Image Number"+ (page + 1).toString()
                    }
                )
            }
        }) {
        if(showIcon) {
            Icon(
                modifier = Modifier
                    .padding(end = 9.dp)
                    .size(35.dp)
                    .clickable {
                        onDeleteClick(image)
                    },
                tint = MaterialTheme.colorScheme.error,
                imageVector=  Icons.TwoTone.Delete,
                contentDescription = "Delete Image Number"+ (page + 1).toString()
            )
        }
    }
}