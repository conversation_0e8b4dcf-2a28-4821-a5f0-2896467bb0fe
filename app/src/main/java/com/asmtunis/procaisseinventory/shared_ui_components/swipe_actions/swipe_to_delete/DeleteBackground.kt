package com.asmtunis.procaisseinventory.shared_ui_components.swipe_actions.swipe_to_delete
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SwipeToDismissBoxState
import androidx.compose.material3.SwipeToDismissBoxValue
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeleteBackground(
    swipeDismissState: SwipeToDismissBoxState
) {

    val direction = swipeDismissState.dismissDirection ?: return

    val progress = swipeDismissState.progress
   val color by animateColorAsState(
        when (swipeDismissState.targetValue) {
            SwipeToDismissBoxValue.Settled -> MaterialTheme.colorScheme.background.copy(alpha = 0.5f)
            else -> MaterialTheme.colorScheme.error.copy(alpha = progress)
        }, label = ""
    )

   /* val color = when(swipeDismissState.dismissDirection) {
        DismissDirection.EndToStart -> Color.Red
            DismissDirection.StartToEnd -> Color.Green
            null -> Color.Transparent
    }*/
    val alignment = when (direction) {
        SwipeToDismissBoxValue.StartToEnd -> Alignment.CenterStart
        SwipeToDismissBoxValue.EndToStart -> Alignment.CenterEnd
     //   else -> Alignment.Center
        else -> {
           return
        }
    }
    val scale by animateFloatAsState(
        //if (swipeDismissState.targetValue == DismissValue.Default) 0.75f else 2f, label = ""
        if (progress == 1f) 0.75f else 1f + progress, label = ""
    )



    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(color)
            .padding(16.dp),
        contentAlignment = alignment
    ) {
        Icon(
            imageVector = Icons.Default.Delete,
            contentDescription = null,
            modifier = Modifier.scale(scale),
            tint = MaterialTheme.colorScheme.errorContainer.copy(alpha =  if (progress == 1f) 1f else 1f - progress)
        )
    }
}