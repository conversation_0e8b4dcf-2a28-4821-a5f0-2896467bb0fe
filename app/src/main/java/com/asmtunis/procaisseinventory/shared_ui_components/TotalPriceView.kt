package com.asmtunis.procaisseinventory.shared_ui_components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customWidth
import com.simapps.ui_kit.edit_text.EditTextField

@Composable
fun TotalPriceView(
    totalPrice: String,
    totalPriceAfterDiscount: String,
    showPriceWithDiscount: Boolean
) {

    Row(
        modifier = Modifier.fillMaxWidth().padding(start = 12.dp, end = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = if(showPriceWithDiscount) Arrangement.SpaceBetween else Arrangement.Center,
        //    modifier = Modifier.weight(1f)
    ) {



        EditTextField(
          //  modifier = Modifier.customWidth(if(showPriceWithDiscount) 0.45f else 0.95f),
            modifier = Modifier.customWidth(if(showPriceWithDiscount) 0.45f else 0.85f),
            text = convertStringToPriceFormat(totalPrice),
            errorValue = null,
            label = stringResource(R.string.total),
            onValueChange = {
                //  onTotalPriceWithDiscountChange(it)
            },
            readOnly = true,
            enabled = true,
            showTrailingIcon = false,
            keyboardType = KeyboardType.Decimal,
            imeAction = ImeAction.Next
        )

        if(showPriceWithDiscount) {
            EditTextField(
                modifier = Modifier.customWidth(0.45f),
                text = convertStringToPriceFormat(totalPriceAfterDiscount),
                errorValue = null,
                label = stringResource(R.string.total_price_with_discount_field_title),
                onValueChange = {
                    //  onTotalPriceWithDiscountChange(it)
                },
                readOnly = true,
                enabled = true,
                showTrailingIcon = false,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )
        }
    }
}