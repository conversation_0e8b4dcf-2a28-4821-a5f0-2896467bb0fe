package com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DriveFileRenameOutline
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.simapps.ui_kit.SaveCloseBtns
import com.simapps.ui_kit.edit_text.EditTextField
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddMarqueBottomSheet(
    toaster: ToasterState,
    marqueTxt: String,
    errorvalue: UiText? = null,
    onMarqueTextChange: (String) -> Unit,
    setVisibilty: (Boolean) -> Unit,
    onSaveClick: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val context = LocalContext.current
   ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = {
            scope.launch {
                sheetState.hide()
            }
            setVisibilty(false)
        },
    ) {

//    Dialog(
//        onDismissRequest = {
//            // Dismiss the dialog when the user clicks outside the dialog or on the back
//            // button. If you want to disable that functionality, simply use an empty
//            // onDismissRequest.
//            setVisibilty(false)
//        },
//        properties =
//        DialogProperties(
//            usePlatformDefaultWidth = true,
//        ),
//        content = {
    //    Card {
            Column(
                modifier = Modifier
                    // .padding(padding)
                    // .fillMaxSize()
                    .fillMaxWidth()
                  //  .wrapContentHeight()
                 //   .wrapContentSize(Alignment.Center)
                    .padding(12.dp),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = stringResource(id = R.string.cd_addMarque_button), style = MaterialTheme.typography.titleMedium)
                //   HorizontalDivider()
                Spacer(modifier = Modifier.height(12.dp))
                EditTextField(
                    modifier = Modifier.fillMaxWidth(0.8f),
                    text = marqueTxt,
                    errorValue = errorvalue?.asString(),
                    label = stringResource(R.string.designation_marque),
                    onValueChange = {
                        onMarqueTextChange(it)
                    },
                    readOnly = false,
                    enabled = true,
                    showTrailingIcon = true,
                    leadingIcon = Icons.Default.DriveFileRenameOutline,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                )
                Spacer(modifier = Modifier.height(20.dp))

                val density = LocalDensity.current
                AnimatedVisibility(
                    visible = marqueTxt.isNotEmpty(),
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {
                    SaveCloseBtns(
                        onSaveClick = {

                            showToast(
                                context = context,
                                toaster = toaster,
                                message = String.format(context.resources.getString(R.string.add_place_holder), marqueTxt) + " \n"+ context.resources.getString(R.string.cd_addMarque_succ),
                                type =  ToastType.Success,
                            )
                            onSaveClick()
                        },
                        onCloseClick = {
                            setVisibilty(false)
                        },
                        lableSave = R.string.cd_addVisite_button,
                        lableClose = R.string.cd_close_button,
                    )
                }
                Spacer(modifier = Modifier.height(20.dp))
                HorizontalDivider()

                Spacer(modifier = Modifier.height(20.dp))


            }
     //   }
        }
 //   )


  }
