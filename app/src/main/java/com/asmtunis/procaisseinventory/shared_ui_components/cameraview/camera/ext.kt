package com.asmtunis.procaisseinventory.shared_ui_components.cameraview.camera

import android.content.Context
import android.util.Log
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.ExecutionException
import java.util.concurrent.Executor
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

// ORIGINAL IMPL
//suspend fun Context.getCameraProvider(): ProcessCameraProvider = suspendCoroutine { continuation ->
//    ProcessCameraProvider.getInstance(this).also { future ->
//        future.addListener(
//            {
//                continuation.resume(future.get())
//            },
//            executor
//        )
//    }
//}

//suspend fun Context.getCameraProvider(): ProcessCameraProvider =
//    withContext(Dispatchers.Default) {
//        suspendCancellableCoroutine { continuation ->
//            val cameraProviderFuture = ProcessCameraProvider.getInstance(this@getCameraProvider)
//            cameraProviderFuture.addListener(
//                {
//                    try {
//                        Runnable {
//                            continuation.resume(cameraProviderFuture.get())
//                        }
//                    } catch (e: Exception) {
//                        continuation.resumeWithException(e)
//                    }
//                },
//                ContextCompat.getMainExecutor(this@getCameraProvider) // Or another safe executor
//            )
//        }
//    }

suspend fun Context.getCameraProvider(): ProcessCameraProvider = suspendCoroutine { continuation ->
    val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
    val executor = ContextCompat.getMainExecutor(this) // Use MainExecutor for safety

    val listener = Runnable {
        try {
            continuation.resume(cameraProviderFuture.get())
        } catch (e: ExecutionException) {
            continuation.resumeWithException(e.cause ?: e)
        } catch (e: InterruptedException) {
            continuation.resumeWithException(e)
        }
    }

    cameraProviderFuture.addListener(listener, executor)

//    continuation.invokeOnCancellation {
//        cameraProviderFuture.removeListener(listener)
//    }
}

val Context.executor: Executor
    get() = ContextCompat.getMainExecutor(this)

suspend fun ImageCapture.takePicture(executor: Executor): File {
    val photoFile = withContext(Dispatchers.IO) {
        kotlin.runCatching {
            File.createTempFile("image", "jpg")
        }.getOrElse { ex ->
            Log.e("TakePicture", "Failed to create temporary file", ex)
            File("/dev/null")
        }
    }

    return suspendCoroutine { continuation ->
        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()
        takePicture(
            outputOptions, executor,
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    continuation.resume(photoFile)
                }

                override fun onError(ex: ImageCaptureException) {
                    Log.e("TakePicture", "Image capture failed", ex)
                    continuation.resumeWithException(ex)
                }
            }
        )
    }
}
