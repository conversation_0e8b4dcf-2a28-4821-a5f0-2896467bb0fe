package com.asmtunis.procaisseinventory.shared_ui_components.image_pager

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Card
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.CameraViewModel
import com.simapps.ui_kit.ModifiersUtils.graphicsLayer

@Composable
fun HorizontalImagePager(
    onClicks: () -> Unit,
    onDeleteClick: (ImagePieceJoint) -> Unit,
    canModify : Boolean,
    cameraViewModel: CameraViewModel,
    imageList : List<ImagePieceJoint>
) {
  val pagerState =  rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f
    ) {
        // provide pageCount
        imageList.size
    }

    HorizontalPager(
        modifier = Modifier.fillMaxHeight(),
        state = pagerState,
        pageSpacing = 8.dp,
        contentPadding = PaddingValues(vertical = 32.dp),
    ) { page ->
        Card(
            Modifier
                .clickable {
                    onClicks()
                    cameraViewModel.onCurrentImagePageDialogChange(page)
                }
                .graphicsLayer(pagerState = pagerState, page = page)

        ) {
            PageContent(
                showExitIcon = false,
                page = page,
                canModify = canModify,
                imageList = imageList,
                onDismissRequest = { },
                onDeleteClick = { onDeleteClick(it) }
            )


        }
    }
}






