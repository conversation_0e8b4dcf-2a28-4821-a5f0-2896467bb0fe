package com.asmtunis.procaisseinventory.nav_components.data.model

import androidx.annotation.StringRes
import androidx.compose.ui.graphics.vector.ImageVector

data class MenuModel(
    val id: String,
    val route: Any,
    @StringRes var title: Int,
    @StringRes var titleShort: Int,
    @StringRes val contentDescription: Int,
    val icon: ImageVector,
    var badge: String = "",
    var authorizationID: String = ""
)
