package com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.ArticleOpeartions.getQuantity
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine.PricePerStation
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.NO_OPERATION
import com.asmtunis.procaisseinventory.core.Globals.PRIX_PUBLIQUE
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.calculateAmountExcludingTax
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.calculateAmountHT
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.calculateAmountTTCNet
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.calculateDiscountRate
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.returnOneIfZero
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.returnZeroIfNegative
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.totalPriceTTC
import com.asmtunis.procaisseinventory.core.utils.StringUtils.formatZeroDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.dokar.sonner.ToastType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class SelectArticleCalculViewModel @Inject constructor(
   // @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
   // @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
   // @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
   // @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val proCaisseLocalDb: ProCaisseLocalDb,
  //  private val proCaisseRemote: ProCaisseRemote
) :
    ViewModel() {

//    init {
//        getTvaList()
//    }
    var prixPerStation by mutableStateOf(PricePerStation())
        private set

    private fun getPricePerStation(article: Article) {
        viewModelScope.launch {
            proCaisseLocalDb.pricePerStation.getOneByArticle(
                article = article.aRTCode,
                station = article.sARTCodeSatation!!
            ).collect {
                if (it == null) PricePerStation()
                else prixPerStation = it
            }
        }
    }



    var showSetArticle by mutableStateOf(false)
        private set

    fun onShowSetArticleChange(value: Boolean) {
        showSetArticle = value
    }

    var showPriceCategory by mutableStateOf(false)
        private set

    fun setShowPriceCategoryChange(value: Boolean) {
        showPriceCategory = value
        showPriceCategorySingleArticle = value
    }


    var showPriceCategorySingleArticle by mutableStateOf(false)
        private set

    fun setShowPriceCategorySingleArticleChange(value: Boolean) {
        showPriceCategorySingleArticle = value
    }

    val priceCategoryList = listOf(PRIX_PUBLIQUE, "Prix Gros 1", "Prix Gros 2", "Prix Gros 3")

    var selectedPriceCategory by mutableStateOf(priceCategoryList.first())
        private set

    fun onSelectedPriceCategoryChange(value: String) {
        selectedPriceCategory = value
       selectedArticle = selectedArticle.copy(selectedPriceCategory = value)
    }




    fun getPrice(article: Article, showPrixVente: Boolean = false): String =
      //  if(showPrixVente) article.aRTPrixUnitaireHT
        if(showPrixVente) article.pvttc.toString()
        else
        when (selectedPriceCategory) {
            priceCategoryList.first() -> article.pvttc.toString()
            priceCategoryList[1] -> article.prixGros1
            priceCategoryList[2] -> article.prixGros2
            priceCategoryList[3] -> article.prixGros3
            else -> article.pvttc.toString()
        }

    var showPriceDetail by mutableStateOf(false)
        private set

    fun onShowPriceDetailChange(value: Boolean) {
        showPriceDetail = value
    }



    var totalDiscount by mutableStateOf("")
        private set

    fun onTotalDiscountChange(value: String) {
        totalDiscount = if (stringToDouble(value) < 0)
            "0"
        else {
            if(stringToDouble(value)>100) "100" else value
        }
    }

    fun setTotalDiscount(totalPriceAfterDiscount: Double = 0.0) {
        var result = 0.0

        if (selectedArticleList.isNotEmpty()) {

            val disc = if(totalPriceAfterDiscount == 0.0) totPriceWithoutDicount - totalPriceAfterDiscountChange
            else totPriceWithoutDicount - totalPriceAfterDiscount
            result = (disc / totPriceWithoutDicount) * 100
        }


        totalDiscount = if (result <= 0.0) "" else result.toString()
    }
    var totalDiscountError: String? by mutableStateOf(null)
        private set

    fun onTotalDiscountErrorChange(autValues: String, errorMsg: String) {

          totalDiscountError =

            if (stringToDouble(totalDiscount) > stringToDouble(autValues) && autValues.isNotEmpty()) {
                errorMsg
            }
            else {
                null
            }
    }





    var totPriceTTCWithDicountAndTimber by mutableDoubleStateOf(0.0)
        private set

    fun onTotPriceTTCWithDicountAndTimberChange(haveTimber: Boolean,  listActifTimber: List<Timbre>) {

        val timbValu = if (haveTimber) CalculationsUtils.timbersValueSum(listActifTimber = listActifTimber) else 0.0


        totPriceTTCWithDicountAndTimber = totalPriceAfterDiscountChange + timbValu
    }

    var totalPriceAfterDiscountChange by mutableDoubleStateOf(0.0)
        private set

    fun onTotalPriceAfterDiscountChange(value: String) {
        totalPriceAfterDiscountChange = if (stringToDouble(value) < 0) 0.0 else stringToDouble(value)
    }

    fun changeTotPriceAfterDiscount(value: String = "") {
        totalPriceAfterDiscountChange = stringToDouble(value.ifEmpty {
            (totPriceWithoutDicount - ((stringToDouble(totalDiscount) * totPriceWithoutDicount) / 100)).toString()
        })

    }

    var hasPromo by mutableStateOf(false)
        private set

    fun onHasPromoChange(value: Boolean) {
        hasPromo = value
    }




    fun updateDiscountInEveryLine() {
        if (selectedArticleList.isEmpty()) return
        for (selectedArt in selectedArticleList.toList()) {
            selectedArticle = selectedArt
            calculatePricesMobility(
                typeOperation = "remise",
                hasPromo = false,
                discount = totalDiscount,
                operation = NO_OPERATION,
            )

            updateSelectedArticleMobilityList()
        }
    }


    fun totalPriceDiscount() {

        var totPriceRemise = 0.0
        for (selectedArt in selectedArticleList) {
            totPriceRemise += stringToDouble(selectedArt.lTMtTTC)


        }





        onTotalPriceAfterDiscountChange(totPriceRemise.toString())

    }




    /**
     * Mobility select aticle
     */

    /* var initialArticleMobilityList = mutableStateListOf<SelectedArticleMobility>()
         private set*/


    var selectedArticleList = mutableStateListOf<SelectedArticle>()
        private set


    fun addOneItemToSelectedArticleMobilityList(item: SelectedArticle) {
        selectedArticleList.add(item)
    }

    var showTva by mutableStateOf(false)
        private set

    fun onShowTvaChange(value: Boolean) {
        showTva = value
    }




    fun addNewLigneSelectedMobilityArtcle(
        hasPromo: Boolean = false,
        operation: String,
        quantiti: String = "",
        remise: String = "0.0",
        typeOperation: String = "",
        prixCaisse: String = "",
        prixtotal: String = "",
        prixHT: String = "",
        useSalePrice: Boolean,
        tva: Tva = Tva(tVACode = selectedArticle.article.aRTTVA.toString())
    ) {

        if (quantiti.isEmpty() && operation == NO_OPERATION && typeOperation.isEmpty()) {
            return
        }

       // todo see case when bc to bl : control qte or not
        if (stringToDouble(quantiti) > stringToDouble(selectedArticle.article.aRTQteStock) && controlQuantity) {
            selectedArticle = selectedArticle.copy(quantityError = UiText.StringResource(resId = R.string.stock_qte_is, "${selectedArticle.article.aRTQteStock} ${selectedArticle.article.uNITEARTICLECodeUnite}"))
            return
        }
        selectedArticle = selectedArticle.copy(quantityError = null)

        if(useSalePrice) {
            calculatePricesMobility(
                hasPromo = hasPromo,
                discount = remise,
                operation = operation,
                qteInput = quantiti,
                prixCaisse = prixCaisse,
                typeOperation = typeOperation,
                prixTotal = prixtotal,
                prixHT = prixHT,
                tva = tva
            )
        }
        else {
            calculatePricesInventory(
                hasPromo = hasPromo,
                discount = remise,
                operation = operation,
                qteInput = quantiti,
                prixCaisse = prixCaisse,
                typeOperation = typeOperation,
                prixTotal = prixtotal,
                prixHT = prixHT,
                tva = tva
            )
        }

        updateSelectedArticleMobilityList()
    }


    fun calculatePricesInventory(
        hasPromo: Boolean,
        discount: String = "0.0",
        operation: String = Globals.ADD,
        qteInput: String = "",
        prixCaisse: String = "",
        prixTotal: String = "",
        typeOperation: String = "",
        prixHT: String = "",
        tva: Tva = selectedArticle.tva
    ) {


        val tvaValue = stringToDouble(tva.tVACode) / 100

        val artc = selectedArticle.article




        val (quantity, price, prix) =  setData(
            discount = discount,
            prixCaisse = prixCaisse,
            qteInput = qteInput,
            operation = operation,
            artc = artc,
        )




        val qt = if (stringToDouble(quantity) <= 0) "" else quantity




        val prixCaiss = calculateAmountTTCNet(
            amount = stringToDouble(price),
            discount = stringToDouble(discount)
        )


        selectedArticle = when (typeOperation) {
            "qty" -> {

                val remise = stringToDouble(selectedArticle.discount)
              //  val lIGBonEntreeQte = stringToDouble(qteInput)
                val lIGBonEntreeQte = stringToDouble(quantity)



              //  val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                val lIGBonEntreePUHT = if(selectedArticle.lTPuHT.isEmpty()/** when add article from + button*/)
                    stringToDouble(selectedArticle.article.aRTPrixUnitaireHT)
                else stringToDouble(selectedArticle.lTPuHT)

                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT * (remise / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (remise / 100)
              //  val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue

                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue


                val mntDiscount = lIGBonEntreeRemise * lIGBonEntreeQte
                selectedArticle.copy(
                    quantity = quantity,
                    prixVente = prix,
                  //  prixCaisse = convertStringToDoubleFormat(prixCaiss.returnZeroIfNegative().toString()),
                    mntDiscount = /*if(mntDiscount<=0.0) "" else */mntDiscount.toString(),
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = if(remise == 0.0) "" else remise.toString(),
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                     lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString(),
                    prixCaisse = prixCaiss.returnZeroIfNegative().toString(),
                )
            }

            "remise" -> {

                val lIGBonEntreeQte = stringToDouble(selectedArticle.quantity)
                val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(stringToDouble(discount) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(discount) / 100)
               // val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double =  lIGBonEntreeMntNetHt * tvaValue

                selectedArticle.copy(
                    prixVente = prix,
                    prixCaisse = prixCaiss.returnZeroIfNegative().toString(),
                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = discount,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            "prixCaisse" -> {

                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }



                val lIGBonEntreeQte = stringToDouble(selectedArticle.quantity)
                val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT * (stringToDouble(rmise) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(rmise) / 100)
                //val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue

                selectedArticle.copy(
                    prixVente = prix,
                    // quantity = qt,
                    prixCaisse = prixCaisse,

                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = rmise,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            "totalPrice" -> {
                val rem = calculateDiscountRateFromTotalTTC(
                    prix = prix,
                    lTMtTTC = prixTotal
                )
                val calPrixCaisse = stringToDouble(prixTotal) / stringToDouble(selectedArticle.quantity)


                val ltMntHt = calculateAmountHT(amount = stringToDouble(prixTotal), tva = artc.aRTTVA)

                selectedArticle.copy(
                    prixVente = prix,
                    quantity = stringToDouble(selectedArticle.quantity).returnOneIfZero().toString(),
                    prixCaisse = calPrixCaisse.toString(),//calculateAmountTTCNet(amount = stringToDouble(prixTotal), discount =  rem).toString(),


                    mntDiscount = (calculateAmountExcludingTax(price = stringToDouble(prix), quantity = stringToDouble(selectedArticle.quantity).returnOneIfZero(), vat = tvaValue)
                            - calculateAmountExcludingTax(price = calPrixCaisse, quantity = stringToDouble(selectedArticle.quantity).returnOneIfZero(), vat =  tvaValue)).toString(),
                    lTMtTTC = prixTotal,
                    discount = rem.toString(),
                    lTMtBrutHT = ltMntHt.toString()

                )
            }

            "tva" -> {

                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }


                val lIGBonEntreeQte = (stringToDouble(selectedArticle.quantity)).returnOneIfZero()
                val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(stringToDouble(rmise) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(rmise) / 100)
               // val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue

                selectedArticle.copy(
                    tva = tva,
                    prixVente = selectedArticle.prixVente,
                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    prixCaisse = calculateAmountTTCNet(
                        amount = stringToDouble(price),
                        discount = stringToDouble(rmise)
                    ).toString(),
                    //quantity = qte,
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = rmise,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                   // tva = Tva(tVACode = artc.aRTTVA.toString()),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            "prixAchatHT" -> {

                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }

                val lIGBonEntreeQte = (stringToDouble(selectedArticle.quantity)).returnOneIfZero()

                val lIGBonEntreeMntBrutHT: Double = stringToDouble(prixHT)
                val lIGBonEntreePUHT = lIGBonEntreeMntBrutHT / lIGBonEntreeQte

              //  val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
            //    val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(stringToDouble(discount) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(discount) / 100)

             //   netht * tva 0.07

              //  val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue

                selectedArticle.copy(
                    tva = tva,
                    prixVente = selectedArticle.prixVente,
                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    prixCaisse = calculateAmountTTCNet(
                        amount = stringToDouble(price),
                        discount = stringToDouble(rmise)
                    ).toString(),
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = rmise,
                    lTMtBrutHT = formatZeroDouble(double = lIGBonEntreeMntBrutHT),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            else -> {
                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }


                val lIGBonEntreeQte = stringToDouble(qt)

                val lIGBonEntreePUHT = stringToDouble(selectedArticle.article.aRTPrixUnitaireHT)

                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(stringToDouble(discount) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(discount) / 100)
               // val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue
                selectedArticle.copy(
                    prixVente = prix,
                   /* mntDiscount = stringToDouble(prix) - calculateAmountTTCNet(
                        amount = stringToDouble(price),
                        discount = stringToDouble(rmise)
                    ),*/

                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    prixCaisse = calculateAmountTTCNet(
                        amount = stringToDouble(price),
                        discount = stringToDouble(rmise)
                    ).toString(),
                    quantity = qt,
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = rmise,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    tva = Tva(tVACode = artc.aRTTVA.toString()),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()

                )
            }
        }


    }


    fun calculatePricesMobility(
        hasPromo: Boolean,
        discount: String = "0.0",
        operation: String = Globals.ADD,
        qteInput: String = "",
        prixCaisse: String = "",
        prixTotal: String = "",
        typeOperation: String = "",
        prixHT: String = "",
        tva: Tva = selectedArticle.tva
    ) {


        val artc = selectedArticle.article

        val tvaValue = artc.aRTTVA / 100

        val (quantity, price, prix) = setData(
            discount = discount,
            prixCaisse = prixCaisse,
            qteInput = qteInput,
            operation = operation,
            artc = artc
        )

        Log.d("ercxxxssdddfff", "typeOperation "+ typeOperation)
        selectedArticle = when (typeOperation) {
            "qty" -> {
                val remise = stringToDouble(selectedArticle.discount)
                //  val lIGBonEntreeQte = stringToDouble(qteInput)
                val lIGBonEntreeQte = stringToDouble(quantity)

                //  val lIGBonEntreeQte = stringToDouble(qteInput)

                val (lIGBonEntreePUTTC, lIGBonEntreePUHT) = setPuTTCAndPUHT(tvaValue = tvaValue)


                val lIGBonEntreeMntTTC: Double = lIGBonEntreePUTTC * lIGBonEntreeQte


                //  val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
              //  val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)



                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT



                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT * (remise / 100))

                // val lIGBonEntreeRemise = lIGBonEntreePUHT * (remise / 100)
                val unitPriceCaisse = lIGBonEntreeMntTTC/ lIGBonEntreeQte
                val lIGBonEntreeRemise = stringToDouble(prix) - unitPriceCaisse


                //  val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue


                val lIGBonEntreeMntTva: Double = setMntTva(
                    tvaValue = tvaValue,
                    lIGBonEntreeMntTTC = lIGBonEntreeMntTTC,
                    lIGBonEntreeMntNetHt = lIGBonEntreeMntNetHt,
                )

                Log.d("ercxxxssdddfff", "lIGBonEntreePUHT "+ lIGBonEntreePUHT)
                Log.d("ercxxxssdddfff", "lIGBonEntreeMntTva "+ lIGBonEntreeMntTva)
                selectedArticle.copy(
                    quantity = quantity,
                    prixVente = prix,
                    //  prixCaisse = convertStringToDoubleFormat(prixCaiss.returnZeroIfNegative().toString()),
                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = if(remise == 0.0) "" else remise.toString(),
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString(),
                    prixCaisse = unitPriceCaisse.returnZeroIfNegative().toString(),
                )
            }

            "remise" -> {
                val prixCaiss = calculateAmountTTCNet(
                    amount = stringToDouble(price),
                    discount = stringToDouble(discount)
                )


                val lIGBonEntreeQte = stringToDouble(selectedArticle.quantity)
                val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(stringToDouble(discount) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte


              //  val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(discount) / 100)
                val lIGBonEntreeRemise = stringToDouble(prix) - prixCaiss

                // val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double =  lIGBonEntreeMntNetHt * tvaValue



                selectedArticle.copy(
                    prixVente =  prix,
                    prixCaisse = prixCaiss.returnZeroIfNegative().toString(),
                     mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = discount,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            "prixCaisse" -> {

                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }



                val lIGBonEntreeQte = stringToDouble(selectedArticle.quantity)
                val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT * (stringToDouble(rmise) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(rmise) / 100)
                //val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue

                selectedArticle.copy(
                    prixVente = prix,
                    // quantity = qt,
                    prixCaisse = prixCaisse,

                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    lTMtTTC = lIGBonEntreeMntTTC.toString(),
                    discount = rmise,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            "totalPrice" -> {
                val rem = calculateDiscountRateFromTotalTTC(
                    prix = prix,
                    lTMtTTC = prixTotal
                )
                val calPrixCaisse = stringToDouble(prixTotal) / stringToDouble(selectedArticle.quantity)


                val ltMntHt = calculateAmountHT(amount = stringToDouble(prixTotal), tva = artc.aRTTVA)

                selectedArticle.copy(
                    prixVente = prix,
                    quantity = stringToDouble(selectedArticle.quantity).returnOneIfZero().toString(),
                    prixCaisse = calPrixCaisse.toString(),//calculateAmountTTCNet(amount = stringToDouble(prixTotal), discount =  rem).toString(),


                    mntDiscount = (calculateAmountExcludingTax(price = stringToDouble(prix), quantity = stringToDouble(selectedArticle.quantity).returnOneIfZero(), vat = artc.aRTTVA)
                            - calculateAmountExcludingTax(price = calPrixCaisse, quantity = stringToDouble(selectedArticle.quantity).returnOneIfZero(), vat =  artc.aRTTVA)).toString(),
                    lTMtTTC = prixTotal,
                    discount = rem.toString(),
                    lTMtBrutHT = ltMntHt.toString()

                )
            }

            "tva" -> {
                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }


                val lIGBonEntreeQte = (stringToDouble(selectedArticle.quantity)).returnOneIfZero()
                val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(stringToDouble(rmise) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(rmise) / 100)
                // val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue

                selectedArticle.copy(
                    tva = tva,
                    prixVente = prix,
                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    prixCaisse = calculateAmountTTCNet(
                        amount = stringToDouble(price),
                        discount = stringToDouble(rmise)
                    ).toString(),
                    //quantity = qte,
                    lTMtTTC = calculateAmountTTCNet(
                        price = calculateAmountTTCNet(
                            amount = stringToDouble(price),
                            discount = stringToDouble(rmise)
                        ),
                        quantity = lIGBonEntreeQte,
                        discount = 0.0
                    ).toString()
                    ,
                    discount = rmise,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    // tva = Tva(tVACode = artc.aRTTVA.toString()),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            "prixAchatHT" -> {

                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }

                val lIGBonEntreeQte = (stringToDouble(selectedArticle.quantity)).returnOneIfZero()

                val lIGBonEntreeMntBrutHT: Double = stringToDouble(prixHT)
                val lIGBonEntreePUHT = lIGBonEntreeMntBrutHT / lIGBonEntreeQte

                //  val lIGBonEntreePUHT = stringToDouble(selectedArticle.lTPuHT)
                //    val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT
                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(stringToDouble(discount) / 100))
                val lIGBonEntreeMntTTC: Double = lIGBonEntreeMntNetHt * (1 + tvaValue)
                val lIGBonEntreePUTTC: Double = (lIGBonEntreeMntNetHt * (1 + tvaValue)) / lIGBonEntreeQte
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (stringToDouble(discount) / 100)

                //   netht * tva 0.07

                //  val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue
                val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue

                selectedArticle.copy(
                    tva = tva,
                    prixVente = prix,
                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    prixCaisse = calculateAmountTTCNet(
                        amount = stringToDouble(price),
                        discount = stringToDouble(rmise)
                    ).toString(),
                    lTMtTTC = calculateAmountTTCNet(
                        price = calculateAmountTTCNet(
                            amount = stringToDouble(price),
                            discount = stringToDouble(rmise)
                        ),
                        quantity = lIGBonEntreeQte,
                        discount = 0.0
                    ).toString(),
                    discount = rmise,
                    lTMtBrutHT = formatZeroDouble(double = lIGBonEntreeMntBrutHT),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }

            else -> {

                val rmise = selectedArticle.discount.ifEmpty {
                    getPricePerStationDiscount(
                        hasPromo = hasPromo,
                        calculatedDiscount = calculateDiscountRate(
                            price = stringToDouble(price),
                            unitPrice = stringToDouble(prix)
                        )
                    )
                }

                val lIGBonEntreeQte = stringToDouble(quantity)


                val remise = stringToDouble(rmise)
                //  val lIGBonEntreeQte = stringToDouble(qteInput)


                val (lIGBonEntreePUTTC, lIGBonEntreePUHT) = setPuTTCAndPUHT(tvaValue = tvaValue)

                val lIGBonEntreeMntTTC: Double = lIGBonEntreePUTTC * lIGBonEntreeQte




               // val lIGBonEntreePUHT = calculateAmountHT(lIGBonEntreePUTTC, tva = tvaValue * 100)



                val lIGBonEntreeMntBrutHT: Double = lIGBonEntreeQte * lIGBonEntreePUHT

                val lIGBonEntreeMntNetHt: Double = lIGBonEntreeMntBrutHT - (lIGBonEntreeMntBrutHT *(remise / 100))
                val lIGBonEntreeRemise = lIGBonEntreePUHT * (remise / 100)
                //  val lIGBonEntreeMntTva: Double = lIGBonEntreeQte * (lIGBonEntreePUHT - lIGBonEntreeRemise) * tvaValue


                val lIGBonEntreeMntTva: Double = setMntTva(
                    tvaValue = tvaValue,
                    lIGBonEntreeMntTTC = lIGBonEntreeMntTTC,
                    lIGBonEntreeMntNetHt = lIGBonEntreeMntNetHt,
                )

                val prixCaisse = calculateAmountTTCNet(
                    amount = stringToDouble(price),
                    discount = stringToDouble(rmise)
                ).toString()

                val ltMtTTC = calculateAmountTTCNet(
                    price = calculateAmountTTCNet(
                        amount = stringToDouble(price),
                        discount = stringToDouble(rmise)
                    ),
                    quantity = stringToDouble(quantity),
                    discount = 0.0
                ).toString()


                val tVACode = setTva(
                    tvaValue = artc.aRTTVA,
                    lIGBonEntreeMntTTC = lIGBonEntreeMntTTC,
                    lIGBonEntreeMntNetHt = lIGBonEntreeMntNetHt
                )

                selectedArticle.copy(
                    prixVente = prix,
                    /* mntDiscount = stringToDouble(prix) - calculateAmountTTCNet(
                         amount = stringToDouble(price),
                         discount = stringToDouble(rmise)
                     ),*/

                    mntDiscount = (lIGBonEntreeRemise * lIGBonEntreeQte).toString(),
                    prixCaisse = prixCaisse,
                    quantity = quantity,
                    lTMtTTC = ltMtTTC,
                    discount = rmise,
                    lTMtBrutHT = lIGBonEntreeMntBrutHT.toString(),
                    mntTva = lIGBonEntreeMntTva.toString(),
                    //tva = Tva(tVACode = artc.aRTTVA.toString()),
                    tva = Tva(tVACode =  tVACode.toString()),
                    lTPuHT = lIGBonEntreePUHT.toString(),
                    lTPuTTC = lIGBonEntreePUTTC.toString(),
                    lTMtNetHT = lIGBonEntreeMntNetHt.toString()
                )
            }
        }
    }


    private fun setTva(
        tvaValue: Double,
        lIGBonEntreeMntTTC: Double,
        lIGBonEntreeMntNetHt: Double,
    ): Double{
       return if(tvaValue==0.0) ((lIGBonEntreeMntTTC - lIGBonEntreeMntNetHt) / lIGBonEntreeMntNetHt) * 100 else tvaValue
    }


        private fun setMntTva(
        tvaValue: Double,
        lIGBonEntreeMntTTC: Double,
        lIGBonEntreeMntNetHt: Double,
    ): Double{
        /**
         * test if if(tvaValue == 0.0) because apparantly some clients puts aRTPrixUnitaireHT but forget to put the tvaValue TODO verify this, if not correct then update how lIGBonEntreeMntTva is calculated (also in qte)
         */
        //  val lIGBonEntreeMntTva: Double = lIGBonEntreeMntNetHt * tvaValue
        return if(tvaValue==0.0) lIGBonEntreeMntTTC - lIGBonEntreeMntNetHt else lIGBonEntreeMntNetHt * tvaValue
    }

    private fun setPuTTCAndPUHT(tvaValue: Double): Pair<Double, Double> {
        val lIGBonEntreePUTTC = if(selectedArticle.lTPuTTC.isEmpty()/** when add article from + button*/)
            selectedArticle.article.pvttc
        else stringToDouble(selectedArticle.lTPuTTC)

        /**
         * test if if(tvaValue == 0.0) because apparantly some clients puts aRTPrixUnitaireHT but forget to put the tvaValue TODO verify this, if not correct then update how lIGBonEntreePUHT is calculated (also in qte)
         */
        val lIGBonEntreePUHT = if(tvaValue == 0.0)  {
            if(selectedArticle.lTPuHT.isEmpty()/** when add article from + button*/)
                stringToDouble(selectedArticle.article.aRTPrixUnitaireHT)
            else stringToDouble(selectedArticle.lTPuHT)
        }
        else calculateAmountHT(lIGBonEntreePUTTC, tva = tvaValue * 100)
        return Pair(lIGBonEntreePUTTC, lIGBonEntreePUHT)
    }
    private fun setData(
        discount: String = "0.0",
        prixCaisse: String = "",
        qteInput: String = "",
        operation: String = Globals.ADD,
        artc: Article,
        ): Triple<String, String, String> {

        if (hasPromo) getPricePerStation(article = artc)
        val selectedArtQte = stringToDouble(selectedArticle.quantity)

        val artcQtyAllowed = getArticleQt(
            quantityInStock = stringToDouble(artc.aRTQteStock),
            controlQuantity = controlQuantity
        )

        val canAddQuanttity = (selectedArtQte + getArticleQt(
            quantityInStock = stringToDouble(selectedArticle.article.aRTQteStock),
            controlQuantity = controlQuantity
        ) <= stringToDouble(selectedArticle.article.aRTQteStock) && controlQuantity) || !controlQuantity


        if (!canAddQuanttity)
            selectedArticle = selectedArticle.copy(quantityError = UiText.StringResource(resId = R.string.stock_qte_is, "${selectedArticle.article.aRTQteStock} ${selectedArticle.article.uNITEARTICLECodeUnite}"))


        val qte = if (operation == NO_OPERATION) qteInput
        else {
            if (operation == Globals.ADD) {
                if (canAddQuanttity) {
                    if (selectedArtQte == 0.0) artcQtyAllowed.toString()
                    else (selectedArtQte + artcQtyAllowed).toString()
                }
                else selectedArtQte.toString()
            }
            else (selectedArtQte - artcQtyAllowed).toString()
        }



        val prix = getPrice(article = artc)

        val price = if (prixCaisse != "0.0" && prixCaisse.isNotEmpty() && discount != "0.0" && discount.isNotEmpty()) prixCaisse
        else prix


 return Triple(qte, price, prix)
    }

    private fun getPricePerStationDiscount(hasPromo: Boolean, calculatedDiscount: Double): String {


        val result = if (!hasPromo) calculatedDiscount else {
            if (prixPerStation == PricePerStation() && prixPerStation.tauxPromo <= 0)
                calculatedDiscount
            else prixPerStation.tauxPromo
        }

        return if(stringToDouble(result.toString()) == 0.0) "" else result.toString()
    }

    private fun calculateDiscountRateFromTotalTTC(
        prix: String,
        lTMtTTC: String
    ): Double {
        var discountRate: Double
        if (lTMtTTC.isNotEmpty() && lTMtTTC != "0.0") {
            discountRate = calculateDiscountRate(
                if (lTMtTTC.isNotEmpty())
                    stringToDouble(lTMtTTC)
                else
                    (stringToDouble(prix) * stringToDouble(selectedArticle.quantity)),
                (stringToDouble(prix) * stringToDouble(selectedArticle.quantity))
            )
            if (java.lang.Double.isNaN(discountRate)) discountRate = 0.0
            if (discountRate < 0) {
                discountRate = 0.0
            }
        } else {
            discountRate = 0.0
        }

        return discountRate

    }


    var selectedArticle by mutableStateOf(SelectedArticle())
        private set


    var totPriceWithoutDicount by mutableDoubleStateOf(0.0)
        private set

    fun setTotalPriceWithoutDicount(value: Double = 0.0) {
        totPriceWithoutDicount = if(value != 0.0)
            value
        else
            totalPriceTTC(listArt = selectedArticleList)

    }


    fun setSelectedArticl(article: Article, tvaList: List<Tva>) {
        selectedArticle = getCurrentSelectdArt(article = article, tvaList = tvaList)
    }

    fun setSelectedArticll(value: SelectedArticle) {
        selectedArticle = value
    }

    fun setSelectedArticlPrixVente(value: String) {
        selectedArticle = selectedArticle.copy(prixVente = value)
        updateSelectedArticleMobilityList()
    }


    var controlQuantity by mutableStateOf(true)
        private set

    fun setControlQte(value: Boolean) {
        Log.d("eddccffggg", "value " +value)
        controlQuantity = value
    }



    fun isArtSelected(article: Article): Boolean = selectedArticleList.any { it.article.aRTCode == article.aRTCode }


    fun getCurrentSelectdArt(article: Article, tvaList: List<Tva>): SelectedArticle =
        selectedArticleList.firstOrNull { it.article.aRTCode == article.aRTCode }
            ?: SelectedArticle(
                article = article,
                quantity = "",
                lTMtBrutHT = article.aRTPrixUnitaireHT,
                tva = tvaList.firstOrNull{ stringToDouble(it.tVACode) == article.aRTTVA }?: Tva()
            )


    fun updateSelectedArticleMobilityList() {
        if (selectedArticle == SelectedArticle() || selectedArticle.quantity.isEmpty()) {
            return
        }
        val replaceCurrentArt =
            selectedArticleList.any { it.article.aRTCode == selectedArticle.article.aRTCode }



        if (replaceCurrentArt) {
            selectedArticleList.replaceAll { if (it.article.aRTCode == selectedArticle.article.aRTCode) selectedArticle else it }
        } else {
            selectedArticleList.add(selectedArticle)
        }

        if (totalDiscount == "0.0") totalDiscount = ""

    }


    fun getArticleQt(
        quantityInStock: Double,
        controlQuantity: Boolean
    ): Double =
        if (quantityInStock < 1 && controlQuantity) quantityInStock else 1.0

    fun resetSelectedMobilityArticles() {
        selectedArticleList.clear()
        selectedArticle = SelectedArticle()
    }

    fun setConsultationSelectedArticleMobilityList(selectedArticle: SelectedArticle) {
        selectedArticleList.add(selectedArticle)
    }

    fun deleteItemToSelectedArticleMobilityList(article: Article) {
        selectedArticleList.removeIf { it.article.aRTCode == article.aRTCode }
    }

    /**
     * ********************************
     */






    fun calculateTotalPrice(items: List<SelectedArticle>): Double {
        return items.sumOf { stringToDouble(it.lTMtTTC) }
    }

    fun calculateTotalDiscountAmount(items: List<SelectedArticle>): Double {
        return items.sumOf { stringToDouble(it.mntDiscount) }
    }

    fun calculateTotalDiscountPercentage(items: List<SelectedArticle>, totalPrice: Double): Double {
        return calculateTotalDiscountAmount(items) / totalPrice * 100
    }

    fun calculateArticlePrice(item: SelectedArticle): Double {
        return stringToDouble(item.lTMtTTC) - stringToDouble(item.mntDiscount)
    }



    fun calculateArticleTax(item: SelectedArticle, taxRate: Double): Double {
        return calculateArticlePrice(item) * taxRate
    }




//    var tvaList by mutableStateOf(emptyList<Tva>())
//        private set
//    private fun getTvaList() {
//        viewModelScope.launch {
//            proInventoryLocalDb.tva.getAll().collect {
//                if(it.isNullOrEmpty()) return@collect
//                tvaList = it
//            }
//        }
//    }



    fun handleBareCodeResult (
        errorMessage: String,
        onBarCodeInfo: (BareCode) -> Unit,
        barCodeInfo: BareCode,
        isAutoScanMode: Boolean,
        useSalePrice: Boolean,
        tvaList: List<Tva>,
        articleMapByBarCode: Map<String, Article>,
        showToast: (message: Any,
                     type: ToastType) -> Unit
    ) {

     //   val scannedArticle = articleMapByBarCode[barCodeInfo.value]
        val scannedArticle = articleMapByBarCode.values.firstOrNull{ it.aRTCodeBar == barCodeInfo.value}

        if (scannedArticle == null) {
            setSelectedArticl(article = Article(), tvaList = tvaList)
            showToast(
                barCodeInfo.value + "\n $errorMessage",
                ToastType.Error
            )


            onBarCodeInfo(BareCode())
            return
        }
     //   context.resources.getString(R.string.qte_more_than_zero)
        if(controlQuantity && getQuantity(article = scannedArticle) < 0.0) {
            showToast("La quantité doit être supérieure à 0", ToastType.Error)
            return
        }
       setSelectedArticl(article = scannedArticle, tvaList = tvaList)

        if (!isAutoScanMode) {
            onShowSetArticleChange(true)
           onBarCodeInfo(BareCode())
            return
        }

        addNewLigneSelectedMobilityArtcle(
            operation = Globals.ADD,
            useSalePrice = useSalePrice
        )

        setTotalPriceWithoutDicount()
            changeTotPriceAfterDiscount()
        onBarCodeInfo(BareCode())

    }




    fun setTotalPrices() {
        val totDiscount = selectedArticleList.sumOf { stringToDouble(it.mntDiscount) }
        val totalPricebeforDiscount = selectedArticleList.sumOf { stringToDouble(it.prixVente) * stringToDouble(it.quantity) }
      //  val totalPriceAfterDiscount = totalPricebeforDiscount - totDiscount
        val totalPriceAfterDiscount = selectedArticleList.sumOf { stringToDouble(it.lTMtTTC)/* * stringToDouble(it.quantity)*/ }


        setTotalPriceWithoutDicount(value = totalPricebeforDiscount)
        changeTotPriceAfterDiscount(value = totalPriceAfterDiscount.toString() )


        setTotalDiscount()


        // selectArtMobilityVM.onTotalDiscountChange(totDiscount.toString())
    }
}