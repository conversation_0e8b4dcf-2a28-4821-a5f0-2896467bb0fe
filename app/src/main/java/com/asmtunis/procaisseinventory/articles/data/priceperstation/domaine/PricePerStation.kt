package com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.PRICE_PERSTATION_TABLE, primaryKeys = ["UNITE_ARTICLE_CodeUnite", "UNITE_ARTICLE_CodeArt", "UNITE_ARTICLE_station"])
@Serializable
data class PricePerStation(
    @SerialName("UNITE_ARTICLE_CodeUnite")
    @ColumnInfo(name = "UNITE_ARTICLE_CodeUnite")
    var uNITEARTICLECodeUnite: String = "",

    @SerialName("UNITE_ARTICLE_CodeArt")
    @ColumnInfo(name = "UNITE_ARTICLE_CodeArt")
    var uNITEARTICLECodeArt: String = "",

    @SerialName("UNITE_ARTICLE_QtePiece")
    @ColumnInfo(name = "UNITE_ARTICLE_QtePiece")
    var uNITEARTICLEQtePiece: String? = null,

    @SerialName("UNITE_ARTICLE_PrixVenteTTCs")
    @ColumnInfo(name = "UNITE_ARTICLE_PrixVenteTTCs")
    var uNITEARTICLEPrixVenteTTCs: String? = null,

    @SerialName("UNITE_ARTICLE_user")
    @ColumnInfo(name = "UNITE_ARTICLE_user")
    var uNITEARTICLEUser: String? = null,

    @SerialName("UNITE_ARTICLE_station")
    @ColumnInfo(name = "UNITE_ARTICLE_station")
    var uNITEARTICLEStation: String = "",

    @SerialName("UNITE_ARTICLE_export")
    @ColumnInfo(name = "UNITE_ARTICLE_export")
    var uNITEARTICLEExport: String? = null,

    @SerialName("UNITE_ARTICLE_DDm")
    @ColumnInfo(name = "UNITE_ARTICLE_DDm")
    var uNITEARTICLEDDm: String? = null,

    @SerialName("prix_is_unitaire")
    @ColumnInfo(name = "prix_is_unitaire")
    var prixIsUnitaire: String? = null,

    @SerialName("PrixG1")
    @ColumnInfo(name = "PrixG1")
    var prixG1: Double = 0.0,

    @SerialName("PrixG2")
    @ColumnInfo(name = "PrixG2")
    var prixG2: Double = 0.0,

    @SerialName("PrixG3")
    @ColumnInfo(name = "PrixG3")
    var prixG3: Double = 0.0,

    @SerialName("Taux_remise")
    @ColumnInfo(name = "Taux_remise")
    var tauxRemise: Double = 0.0,

    @SerialName("Taux_Promo")
    @ColumnInfo(name = "Taux_Promo")
    var tauxPromo: Double = 0.0

)
