package com.asmtunis.procaisseinventory.articles.data.priceperstation.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine.PricePerStation
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.PRICE_PERSTATION_TABLE
import kotlinx.coroutines.flow.Flow

@Dao
interface PricePerStationDAO {
    @get:Query("SELECT * FROM $PRICE_PERSTATION_TABLE")
    val all: Flow<List<PricePerStation>>

    @Query("SELECT COUNT(*) FROM $PRICE_PERSTATION_TABLE")
    fun count(): Int

    @get:Query("SELECT * FROM $PRICE_PERSTATION_TABLE LIMIT 1")
    val one: Flow<PricePerStation?>

    @Query("SELECT * FROM $PRICE_PERSTATION_TABLE where UNITE_ARTICLE_station = :station and UNITE_ARTICLE_CodeArt = :article LIMIT 1")
    fun getOneByArticle(article: String, station: String): Flow<PricePerStation?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: PricePerStation)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<PricePerStation>)

    @Query("DELETE FROM $PRICE_PERSTATION_TABLE")
    fun deleteAll()
}
