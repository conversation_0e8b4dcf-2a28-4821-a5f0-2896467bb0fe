package com.asmtunis.procaisseinventory.articles.data.article.remote.api

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCount
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ClientArticlePrix
import com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticle
import com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticleCodeBare
import com.asmtunis.procaisseinventory.core.model.DataResult
import kotlinx.coroutines.flow.Flow

interface ArticlesApi {




    //   var URL_ADD_ARTICLES_CODE_BAR = "${Globals.BASE_URL}/Article/addArticleCodeBarMobile"


    suspend fun getArticles(baseConfig: String, ddm: String, station: String): Flow<DataResult<List<Article>?>>
    suspend fun getCountArticle(baseConfig: String): Flow<DataResult<ArticleCount?>>
    suspend fun getArticlesPagination(baseConfig: String, page: String, limit: String): Flow<DataResult<PaginationResponseArticle?>>
    suspend fun getArticlesPaginationWithStation(baseConfig: String, page: String, limit: String, ddm: String, station: String): Flow<DataResult<PaginationResponseArticle?>>
    suspend fun getArticlesCodeBarePagination(baseConfig: String, page: String, limit: String): Flow<DataResult<PaginationResponseArticleCodeBare?>>
    suspend fun addArticles(baseConfig: String): Flow<DataResult<List<Article>?>>
    suspend fun getArticlesByStation(baseConfig: String): Flow<DataResult<List<Article>?>>
    suspend fun getClientArticlePrix(baseConfig: String): Flow<DataResult<List<ClientArticlePrix>?>>
    suspend fun getArticleCodeBare(baseConfig: String): Flow<DataResult<List<ArticleCodeBar>?>>
    suspend fun addArticleCodeBarMobile(baseConfig: String): Flow<DataResult<List<ArticleCodeBar>?>>
}
