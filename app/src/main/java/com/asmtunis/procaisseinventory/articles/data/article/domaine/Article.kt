package com.asmtunis.procaisseinventory.articles.data.article.domaine


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.Articles_TABLE, primaryKeys = ["ART_Code"], indices = [Index("ART_Code"), Index("FAM_Lib"), Index("MAR_Designation")])
@Serializable
data class Article(

    @ColumnInfo(name = "ART_Code")
    @SerialName("ART_Code")
    var aRTCode: String = "",

    @ColumnInfo(name = "ART_CodeBar")
    @SerialName("ART_CodeBar")
    var aRTCodeBar: String = "",

    @ColumnInfo(name = "ART_Designation")
    @SerialName("ART_Designation")
    var aRTDesignation: String = "",

    @ColumnInfo(name = "ART_PrixUnitaireHT")
    @SerialName("ART_PrixUnitaireHT")
    var aRTPrixUnitaireHT: String = "",

    @ColumnInfo(name = "ART_TVA")
    @SerialName("ART_TVA")
    var aRTTVA: Double = 0.0,

    @ColumnInfo(name = "ART_QteStock")
    @SerialName("ART_QteStock")
    var aRTQteStock: String = "",//stock in all stations

    @ColumnInfo(name = "pvttc")
    @SerialName("pvttc")
    var pvttc: Double = 0.0,

    @ColumnInfo(name = "MAR_Designation")
    @SerialName("MAR_Designation")
    
    var mARDesignation: String? = null,

    @ColumnInfo(name = "PrixSolde")
    @SerialName("PrixSolde")
    var prixSolde: String? = null,

    @ColumnInfo(name = "TauxSolde")
    @SerialName("TauxSolde")
    var tauxSolde: String? = null,

    @SerialName("FAM_Lib")
    @ColumnInfo(name = "FAM_Lib")
    
    var fAMLib: String? = null,

    @SerialName("SART_Qte")
    @ColumnInfo(name = "SART_Qte")
    var sARTQte: Double = 0.0, // todo this field dont return when connect with pro caisse use station stock (maybe ask to add this field in response)

    @ColumnInfo(name = "SART_CodeSatation")
    @SerialName("SART_CodeSatation")
    
    var sARTCodeSatation: String? = null,

    @ColumnInfo(name = "FAM_Code")
    @SerialName("FAM_Code")
    
    var fAMCode: String? = null,

    @SerialName("UNITE_ARTICLE_QtePiece")
    @ColumnInfo(name = "UNITE_ARTICLE_QtePiece")
    var uNITEARTICLEQtePiece: Int = 0,

    @SerialName("UNITE_ARTICLE_PrixVenteTTC")
    @ColumnInfo(name = "UNITE_ARTICLE_PrixVenteTTC")
    var uNITEARTICLEPrixVenteTTC: Double = 0.0,


    @SerialName("UNITE_ARTICLE_CodeUnite")
    @ColumnInfo(name = "UNITE_ARTICLE_CodeUnite")
    var uNITEARTICLECodeUnite: String? = null,

    @ColumnInfo(name = "COU_Designation")
    @SerialName("COU_Designation")
    
    var cOUDesignation: String? = null,

    @ColumnInfo(name = "ART_TAILLE")
    @SerialName("ART_TAILLE")
    var aRTTAILLE: String? = null,

    @ColumnInfo(name = "Fils_CodeBar")
    @SerialName("Fils_CodeBar")
    
    var filsCodeBar: String? = null,

    @ColumnInfo(name = "Designation")
    @SerialName("Designation")
    
    var designation: String? = null,

    @SerialName("ART_DesgCourte")
    @ColumnInfo(name = "ART_DesgCourte")
    var aRTDesgCourte: String? = null,

    @SerialName("ART_Famille")
    @ColumnInfo(name = "ART_Famille")
    var aRTFamille: String? = null,

    @SerialName("ART_Marque")
    @ColumnInfo(name = "ART_Marque")
    var aRTMarque: String? = null,

    @SerialName("ART_MargeB")
    @ColumnInfo(name = "ART_MargeB")
    var aRTMargeB: String? = null,

    @SerialName("ART_PrixUnitaireHTVA")
    @ColumnInfo(name = "ART_PrixUnitaireHTVA")
    var aRTPrixUnitaireHTVA: String? = null,

    @SerialName("ART_TypePrixUnitaireHTVA")
    @ColumnInfo(name = "ART_TypePrixUnitaireHTVA")
    var aRTTypePrixUnitaireHTVA: String? = null,

    @SerialName("ART_Couleur")
    @ColumnInfo(name = "ART_Couleur")
    var aRTCouleur: String? = null,

    @SerialName("ART_User")
    @ColumnInfo(name = "ART_User")
    var aRTUser: String? = null,

    @SerialName("ART_Station")
    @ColumnInfo(name = "ART_Station")
    var aRTStation: String? = null,

    @SerialName("ART_QTEmin")
    @ColumnInfo(name = "ART_QTEmin")
    var aRTQTEmin: String? = "",

    @SerialName("ART_Fodec")
    @ColumnInfo(name = "ART_Fodec")
    var aRTFodec: String? = null,

    @SerialName("ART_DC")
    @ColumnInfo(name = "ART_DC")
    var aRTDC: String? = null,

    @SerialName("ART_QTEmax")
    @ColumnInfo(name = "ART_QTEmax")
    var aRTQTEmax: String? = "",

    @SerialName("ART_QteDeclaree")
    @ColumnInfo(name = "ART_QteDeclaree")
    var aRTQteDeclaree: String? = "",

    @SerialName("ART_Prix_AchatFactReel")
    @ColumnInfo(name = "ART_Prix_AchatFactReel")
    var aRTPrixAchatFactReel: String? = null,

    @SerialName("ART_Image")
    @ColumnInfo(name = "ART_Image")
    var aRTImage: String? = null,

    @SerialName("ART_DateCr")
    @ColumnInfo(name = "ART_DateCr")
    var aRTDateCr: String? = null,

    @SerialName("ART_NumSerie")
    @ColumnInfo(name = "ART_NumSerie")
    var aRTNumSerie: String? = null,

    @SerialName("ART_IsTaktile")
    @ColumnInfo(name = "ART_IsTaktile")
    var aRTIsTaktile: String? = null,

    @SerialName("ART_PrixGros1")
    @ColumnInfo(name = "ART_PrixGros1")
    var aRTPrixGros1: String? = null,

    @SerialName("ART_QtePrixGros1")
    @ColumnInfo(name = "ART_QtePrixGros1")
    var aRTQtePrixGros1: String? = null,

    @SerialName("ART_PrixGros2")
    @ColumnInfo(name = "ART_PrixGros2")
    var aRTPrixGros2: String? = null,

    @SerialName("ART_QtePrixGros2")
    @ColumnInfo(name = "ART_QtePrixGros2")
    var aRTQtePrixGros2: String? = null,

    @SerialName("ART_PrixGros3")
    @ColumnInfo(name = "ART_PrixGros3")
    var aRTPrixGros3: String? = null,

    @SerialName("ART_QteGros3")
    @ColumnInfo(name = "ART_QteGros3")
    var aRTQteGros3: String? = null,

    @SerialName("ART_CoulBN")
    @ColumnInfo(name = "ART_CoulBN")
    var aRTCoulBN: String? = null,

    @SerialName("ART_PrixUnitaireHTRes")
    @ColumnInfo(name = "ART_PrixUnitaireHTRes")
    var aRTPrixUnitaireHTRes: String? = null,

    @SerialName("ART_PrixUnitaireHTGlobale")
    @ColumnInfo(name = "ART_PrixUnitaireHTGlobale")
    var aRTPrixUnitaireHTGlobale: String? = null,

    @SerialName("ART_export")
    @ColumnInfo(name = "ART_export")
    var aRTExport: String? = "",

    @SerialName("ART_ddm")
    @ColumnInfo(name = "ART_ddm")
    var aRTDdm: String? = null,

    @SerialName("ART_Equivalence")
    @ColumnInfo(name = "ART_Equivalence")
    var aRTEquivalence: String? = null,

    @SerialName("Regularisation")
    @ColumnInfo(name = "Regularisation")
    var regularisation: String? = null,

    @SerialName("ART_Cout_charge")
    @ColumnInfo(name = "ART_Cout_charge")
    var aRTCoutCharge: String? = null,

    @SerialName("ART_codeSerie")
    @ColumnInfo(name = "ART_codeSerie")
    var aRTCodeSerie: String? = null,

    @SerialName("CRPonderer")
    @ColumnInfo(name = "CRPonderer")
    var cRPonderer: String? = null,

    @SerialName("QTE_Restante")
    @ColumnInfo(name = "QTE_Restante")
    var qTERestante: String? = null,

    @SerialName("Coeff_charge")
    @ColumnInfo(name = "Coeff_charge")
    var coeffCharge: String? = null,

    @SerialName("Charge_tot_coeff")
    @ColumnInfo(name = "Charge_tot_coeff")
    var chargeTotCoeff: String? = null,

    @SerialName("Type_Produit")
    @ColumnInfo(name = "Type_Produit")
    var typeProduit: String? = null,

    @SerialName("Type_service")
    @ColumnInfo(name = "Type_service")
    var typeService: String? = null,

    @SerialName("is_bloquer")
    @ColumnInfo(name = "is_bloquer")
    var isBloquer: String? = null,

    @SerialName("ART_CodeFrs")
    @ColumnInfo(name = "ART_CodeFrs")
    var aRTCodeFrs: String? = null,

    @SerialName("ART_Fournisseur")
    @ColumnInfo(name = "ART_Fournisseur")
    var aRTFournisseur: String? = null,

    @SerialName("ART_Poid_Qte")
    @ColumnInfo(name = "ART_Poid_Qte")
    var aRTPoidQte: String? = null,

    @SerialName("Emp_Code")
    @ColumnInfo(name = "Emp_Code")
    var empCode: String? = null,

    @SerialName("Touche_Balance")
    @ColumnInfo(name = "Touche_Balance")
    var toucheBalance: String? = null,

    @SerialName("Type_Balance")
    @ColumnInfo(name = "Type_Balance")
    var typeBalance: String? = null,

    @SerialName("Anc_cout")
    @ColumnInfo(name = "Anc_cout")
    var ancCout: String? = null,

    @SerialName("Art_Session")
    @ColumnInfo(name = "Art_Session")
    var artSession: String? = null,

    @SerialName("Art_Prom")
    @ColumnInfo(name = "Art_Prom")
    var artProm: String? = null,

    @SerialName("Art_NbProm")
    @ColumnInfo(name = "Art_NbProm")
    var artNbProm: String? = null,

    @SerialName("Art_TauxProm")
    @ColumnInfo(name = "Art_TauxProm")
    var artTauxProm: String? = null,

    @SerialName("Remise_Fidelite")
    @ColumnInfo(name = "Remise_Fidelite")
    var remiseFidelite: String? = null,

    @SerialName("photo_Path")
    @ColumnInfo(name = "photo_Path")
    var photoPath: String? = null,

    @SerialName("ddm")
    @ColumnInfo(name = "ddm")
    var ddm: String? = null,

    @SerialName("export")
    @ColumnInfo(name = "export")
    var export: String? = null,

    @SerialName("Ecrivain")
    @ColumnInfo(name = "Ecrivain")
    var ecrivain: String? = null,

    @SerialName("Collection")
    @ColumnInfo(name = "Collection")
    var collection: String? = null,

    @SerialName("is_Calcul_inverse")
    @ColumnInfo(name = "is_Calcul_inverse")
    var isCalculInverse: String? = null,

    @SerialName("is_Tacktil")
    @ColumnInfo(name = "is_Tacktil")
    var isTacktil: String? = null,

    @SerialName("is_Peremption")
    @ColumnInfo(name = "is_Peremption")
    var isPeremption: String? = null,

    @SerialName("Nbr_Jour_Peremption")
    @ColumnInfo(name = "Nbr_Jour_Peremption")
    var nbrJourPeremption: String? = null,

    @SerialName("is_Promo_Qte_Prix")
    @ColumnInfo(name = "is_Promo_Qte_Prix")
    var isPromoQtePrix: String? = null,

    @SerialName("ART_CodeFrs2")
    @ColumnInfo(name = "ART_CodeFrs2")
    var aRTCodeFrs2: String? = null,

    @SerialName("ART_ChezFrs2")
    @ColumnInfo(name = "ART_ChezFrs2")
    var aRTChezFrs2: String? = null,

    @SerialName("ART_CodeFrs3")
    @ColumnInfo(name = "ART_CodeFrs3")
    var aRTCodeFrs3: String? = null,

    @SerialName("ART_ChezFrs3")
    @ColumnInfo(name = "ART_ChezFrs3")
    var aRTChezFrs3: String? = null,

    @SerialName("is_AlertStock")
    @ColumnInfo(name = "is_AlertStock")
    var isAlertStock: String? = null,

    @SerialName("Art_MaxTRemise")
    @ColumnInfo(name = "Art_MaxTRemise")
    var artMaxTRemise: String? = null,

    @SerialName("Art_MntRemise")
    @ColumnInfo(name = "Art_MntRemise")
    var artMntRemise: String? = null,

    @SerialName("Poste")
    @ColumnInfo(name = "Poste")
    var poste: String? = null,

    @SerialName("Art_NbrePoint")
    @ColumnInfo(name = "Art_NbrePoint")
    var artNbrePoint: String? = null,

    @SerialName("Art_SousFamille")
    @ColumnInfo(name = "Art_SousFamille")
    var artSousFamille: String? = null,

    @SerialName("Art_DesigFact")
    @ColumnInfo(name = "Art_DesigFact")
    var artDesigFact: String? = null,

    @SerialName("Art_Champ1")
    @ColumnInfo(name = "Art_Champ1")
    var artChamp1: String? = null,

    @SerialName("Art_Champ2")
    @ColumnInfo(name = "Art_Champ2")
    var artChamp2: String? = null,

    @SerialName("Art_Champ3")
    @ColumnInfo(name = "Art_Champ3")
    var artChamp3: String? = null,

    @SerialName("Art_NbrePiece")
    @ColumnInfo(name = "Art_NbrePiece")
    var artNbrePiece: String? = null,

    @SerialName("is_supplement")
    @ColumnInfo(name = "is_supplement")
    var isSupplement: String? = null,

    @SerialName("is_numSerie")
    @ColumnInfo(name = "is_numSerie")
    var isNumSerie: String? = null,

    @SerialName("couleur")
    @ColumnInfo(name = "couleur")
    var couleur: String? = null,

    @SerialName("periode")
    @ColumnInfo(name = "periode")
    var periode: String? = null,

    @SerialName("IsService")
    @ColumnInfo(name = "IsService")
    var isService: String? = null,

    @SerialName("IsDistributeur")
    @ColumnInfo(name = "IsDistributeur")
    var isDistributeur: String? = null,

    @SerialName("Ing1")
    @ColumnInfo(name = "Ing1")
    var ing1: String? = null,

    @SerialName("Ing2")
    @ColumnInfo(name = "Ing2")
    var ing2: String? = null,

    @SerialName("Ing3")
    @ColumnInfo(name = "Ing3")
    var ing3: String? = null,

    @SerialName("Ing4")
    @ColumnInfo(name = "Ing4")
    var ing4: String? = null,

    @SerialName("Ing5")
    @ColumnInfo(name = "Ing5")
    var ing5: String? = null,

    @SerialName("Ing6")
    @ColumnInfo(name = "Ing6")
    var ing6: String? = null,

    @SerialName("Ing7")
    @ColumnInfo(name = "Ing7")
    var ing7: String? = null,

    @SerialName("Ing8")
    @ColumnInfo(name = "Ing8")
    var ing8: String? = null,

    @SerialName("Ing9")
    @ColumnInfo(name = "Ing9")
    var ing9: String? = null,

    @SerialName("Ing10")
    @ColumnInfo(name = "Ing10")
    var ing10: String? = null,

    @SerialName("Nbrperemption")
    @ColumnInfo(name = "Nbrperemption")
    var nbrperemption: String? = null,

    @SerialName("Coffe_prime")
    @ColumnInfo(name = "Coffe_prime")
    var coffePrime: String? = null,

    @SerialName("DFabrication")
    @ColumnInfo(name = "DFabrication")
    var dFabrication: String? = null,

    @SerialName("ART_Calibre")
    @ColumnInfo(name = "ART_Calibre")
    var aRTCalibre: String? = null,

    @SerialName("ART_Grammage")
    @ColumnInfo(name = "ART_Grammage")
    var aRTGrammage: String? = null,

    @SerialName("ART_Plomb")
    @ColumnInfo(name = "ART_Plomb")
    var aRTPlomb: String? = null,

    @SerialName("ART_Caract")
    @ColumnInfo(name = "ART_Caract")
    var aRTCaract: String? = null,

    @SerialName("ART_ECommerce")
    @ColumnInfo(name = "ART_ECommerce")
    var aRTECommerce: String? = null,

    @SerialName("ART_EMode")
    @ColumnInfo(name = "ART_EMode")
    var aRTEMode: String? = null,

    @SerialName("ART_EPrixTTC")
    @ColumnInfo(name = "ART_EPrixTTC")
    var aRTEPrixTTC: String? = null,

    @SerialName("ART_ESync")
    @ColumnInfo(name = "ART_ESync")
    var aRTESync: String? = null,

    @SerialName("ART_EMemePrix")
    @ColumnInfo(name = "ART_EMemePrix")
    var aRTEMemePrix: String? = null,

    @SerialName("ART_DernPrixHT")
    @ColumnInfo(name = "ART_DernPrixHT")
    var aRTDernPrixHT: String? = null,

    @SerialName("ART_DernRemsie")
    @ColumnInfo(name = "ART_DernRemsie")
    var aRTDernRemsie: String? = null,

    @SerialName("ART_DatePeremption")
    @ColumnInfo(name = "ART_DatePeremption")
    var aRTDatePeremption: String? = null,

    @SerialName("type_operation")
    @ColumnInfo(name = "type_operation")
    var typeOperation: String? = null,

    @SerialName("ART_PrixPublique")
    @ColumnInfo(name = "ART_PrixPublique")
    //  @Expose
    var artPrixPublique: String = "",

    @SerialName("prixGros1")
    @ColumnInfo(name = "prixGros1")
    //  @Expose
    var prixGros1: String = "",
    @SerialName("prixGros2")
    @ColumnInfo(name = "prixGros2")
//@Expose
    var prixGros2: String = "",
    @SerialName("prixGros3")
    @ColumnInfo(name = "prixGros3")
//@Expose
    var prixGros3: String = "",

    @SerialName("colorCode")
    //@Ignore
    var colorCode: Int = 0,







    @SerialName("TAI_Taille")
    @ColumnInfo(name = "TAI_Taille")
    var taiTaille: String = ""



    /*   
       var prix : Double = 0.0,

       
       var remise : Double = 0.0,

       
       var count : Double = 0.0,*/


) : BaseModel()

