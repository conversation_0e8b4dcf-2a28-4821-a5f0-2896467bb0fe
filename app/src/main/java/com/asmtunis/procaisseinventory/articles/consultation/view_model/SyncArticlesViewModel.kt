package com.asmtunis.procaisseinventory.articles.consultation.view_model

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROINVENTORY_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.data.famille.domaine.AddFamilleResponse
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.marque.domaine.AddMarqueResponse
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
    class SyncArticlesViewModel @Inject constructor(
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proInventoryRemote: ProInventoryRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    private val listenNetwork: ListenNetwork,
    ) : ViewModel() {
    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROINVENTORY_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private var  connected  by mutableStateOf(false)

    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()
        init {
                getFamilleListNotSync()
                getMarqueListNotSync()
                getArticleNotSync()
        }



    var familleNotSync : List<Famille> by mutableStateOf(emptyList())
            private set


        var addFamilleState: RemoteResponseState<List<AddFamilleResponse>> by mutableStateOf(RemoteResponseState())
            private set
    var addFamilleSyncObj : String by mutableStateOf("")
        private set
       private fun getFamilleListNotSync(){

           viewModelScope.launch {
               val familleFlow = proInventoryLocalDb.famille.getNotSync().distinctUntilChanged()

               combine(networkFlow, familleFlow, autoSyncFlow) { isConnected, familleList, autoSync ->
                 connected = isConnected
                   autoSyncState = autoSync
                   familleList.ifEmpty { emptyList() }
               }.collect {
                   if (it.isEmpty()) {
                       familleNotSync = emptyList()
                       return@collect
                   }
                   familleNotSync = it


                 if(connected && autoSyncState)  syncFamille(familleNotSync)
               }
           }
        }



        fun syncFamille(familleNotSync: List<Famille>) {
            viewModelScope.launch(dispatcherIO) {
                val baseConfigObj = GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(familleNotSync)
                )
                addFamilleSyncObj = Json.encodeToString(baseConfigObj)
                proInventoryRemote.famille.addFamilleMobile(addFamilleSyncObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            //  viewModelScope.launch(dispatcherIO) {


                            for (famille in result.data!!) {
                                if(famille.code == 10200) {
                                    proInventoryLocalDb.famille.updateSyncFamille(famille.fAMCode)
                                }

                            }
                            //  }
                            addFamilleState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            addFamilleState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            addFamilleState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }



        var marqueNotSync : List<Marque> by mutableStateOf(emptyList())
            private set


        var marqueState: RemoteResponseState<List<AddMarqueResponse>> by mutableStateOf(RemoteResponseState())
            private set
    var marqueSyncObj : String by mutableStateOf("")
        private set
        private fun getMarqueListNotSync(){

            viewModelScope.launch {
                val marqueFlow = proInventoryLocalDb.marque.getNotSync().distinctUntilChanged()


                combine(networkFlow, marqueFlow, autoSyncFlow){ isConnected, marqueList, autoSync ->
                    connected = isConnected
                    autoSyncState = autoSync
                    marqueList.ifEmpty { emptyList() }
                }.collect {
                    if (it.isEmpty()) {
                        marqueNotSync = emptyList()
                        return@collect
                    }
                    marqueNotSync = it
                  if(connected && autoSyncState)  syncMarque(marqueList = marqueNotSync)
                }

            }
        }


        fun syncMarque(marqueList: List<Marque>) {

            viewModelScope.launch(dispatcherIO) {
                val baseConfigObj = GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(marqueList)
                )
                marqueSyncObj = Json.encodeToString(baseConfigObj)
                proInventoryRemote.marque.addMarqueMobile(marqueSyncObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {

                                for (marque in result.data!!) {
                                    if(marque.code == 10200) {
                                        proInventoryLocalDb.marque.updateSyncMarque(marque.mARCode)
                                    }
                                }
                            marqueState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            marqueState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            marqueState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }




        var articleSync : List<Article> by mutableStateOf(emptyList())
            private set


        var articleState: RemoteResponseState<List<Article>> by mutableStateOf(RemoteResponseState())
            private set
        private fun getArticleNotSync(){

             viewModelScope.launch {
                 val articlesFlow = proCaisseLocalDb.articles.getNotSync().distinctUntilChanged()


                 combine(networkFlow, articlesFlow, autoSyncFlow) { isConnected, articlesList, autoSync ->
                     connected = isConnected
                     autoSyncState = autoSync
                     if (!articlesList.isNullOrEmpty()) articlesList else emptyList()
                 }.collect {

                     if (it.isEmpty()) {
                         articleSync = emptyList()
                         return@collect
                     }
                     articleSync = it.map { art-> art.copy(
                         pvttc = 0.0,
                        sARTCodeSatation = null
                     ) }
                     Log.d("rfdddd", "connected "+ connected)
                     Log.d("rfdddd", "autoSyncState "+ autoSyncState)
                    if(connected && autoSyncState) syncArticle()
                 }
             }

            }

    var articleSyncObj : String by mutableStateOf("")
        private set

        fun syncArticle() {
            viewModelScope.launch(dispatcherIO) {
                val baseConfigObj = GenericObject(
                    proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                        ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                    Json.encodeToJsonElement(articleSync)
                )

                articleSyncObj = Json.encodeToString(baseConfigObj)

                proCaisseRemote.articles.addArticles(articleSyncObj).onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                                for (article in result.data!!) {
                                    proCaisseLocalDb.articles.updateSyncArticle(article.aRTCode)
                                }
                            articleState = RemoteResponseState(data = result.data, loading = false, error = null)
                        }

                        is DataResult.Loading -> {
                            articleState = RemoteResponseState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            articleState = RemoteResponseState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }
    }