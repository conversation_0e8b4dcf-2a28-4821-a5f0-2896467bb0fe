package com.asmtunis.procaisseinventory.articles.data.article.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PaginationResponseArticle(
    @SerialName("current_page")
    val currentPage: Int = -1,

    @SerialName("data")
    val data: List<Article>?,

    @SerialName("first_page_url")
    val firstPageUrl: String  = "",

    @SerialName("from")
    val from: Int = -1,

    @SerialName("last_page")
    val lastPage: Int = -1,

    @SerialName("last_page_url")
    val lastPageUrl: String = "",

    @SerialName("next_page_url")
    val nextPageUrl: String = "",

    @SerialName("prev_page_url")
    val prevPageUrl: String = "",

    @SerialName("path")
    val path: String = "",

    @SerialName("per_page")
    val perPage: String = "",



    @SerialName("to")
    val to: Int = -1,

    @SerialName("total")
    val total: String
)
