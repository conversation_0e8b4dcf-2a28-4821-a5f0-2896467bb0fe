package com.asmtunis.procaisseinventory.articles.data.article.local.di

import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ArticleCodeBarDAO
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ArticleDAO
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ClientArticlePrixDAO
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.article_codebar.ArticleBarCodeLocalRepository
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.article_codebar.ArticleBarCodeLocalRepositoryImpl
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.articles.ArticlesLocalRepositoryImpl
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.articles.ArticlesLocalRepository
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.client_article_prix.ClientArticlePrixLocalRepository
import com.asmtunis.procaisseinventory.articles.data.article.local.repository.client_article_prix.ClientArticlePrixLocalRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class ArticleModule {

    @Provides
    @Singleton
    fun provideArticlesDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.articleDAO()

    @Provides
    @Singleton
    @Named("Articles")
    fun provideArticlesRepository(
        articleDAO: ArticleDAO
    ): ArticlesLocalRepository = ArticlesLocalRepositoryImpl(
        articleDAO = articleDAO

    )




    @Provides
    @Singleton
    fun provideClientArticlePrixDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.clientArticlePrixDAO()

    @Provides
    @Singleton
    @Named("ClientArticlePrix")
    fun provideClientArticlePrixRepository(
        clientArticlePrixDAO: ClientArticlePrixDAO
    ): ClientArticlePrixLocalRepository = ClientArticlePrixLocalRepositoryImpl(
        clientArticlePrixDAO = clientArticlePrixDAO

    )





    @Provides
    @Singleton
    fun provideArticleCodeBarDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.articleCodeBarDAO()

    @Provides
    @Singleton
    @Named("ArticleCodeBar")
    fun provideArticleCodeBarRepository(
        articleCodeBarDAO: ArticleCodeBarDAO
    ): ArticleBarCodeLocalRepository = ArticleBarCodeLocalRepositoryImpl(
        articleCodeBarDAO = articleCodeBarDAO

    )
}
