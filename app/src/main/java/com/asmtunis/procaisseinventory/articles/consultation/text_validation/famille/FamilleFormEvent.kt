package com.asmtunis.procaisseinventory.articles.consultation.text_validation.famille

import com.asmtunis.procaisseinventory.data.station.domaine.Station


sealed class FamilleFormEvent {

    data class DesignationFamilleChanged(val designationFamille: String) : FamilleFormEvent()
    data class DesignationFamilleCourteChanged(val designationFamilleCourte: String) : FamilleFormEvent()
    data class OrdreChanged(val ordre: String) : FamilleFormEvent()
    data class StationChanged(val station: Station) : FamilleFormEvent()

    object SubmitAddFamille : FamilleFormEvent()
}