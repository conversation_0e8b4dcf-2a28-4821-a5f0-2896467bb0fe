package com.asmtunis.procaisseinventory.articles.consultation.screens


import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ShortText
import androidx.compose.material.icons.filled.Addchart
import androidx.compose.material.icons.filled.GroupWork
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.PriceCheck
import androidx.compose.material.icons.filled.Print
import androidx.compose.material.icons.filled.ProductionQuantityLimits
import androidx.compose.material.icons.filled.QrCodeScanner
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.twotone.AddCircle
import androidx.compose.material.icons.twotone.Save
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ValidationArticleEvent
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.article.ArticleFormEvent
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.famille.FamilleFormEvent
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.marque.MarqueFormEvent
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.ADMIN
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProInventory
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.ArticleType
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens.TicketRayonViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.BareCodeScannerIcon
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.AddMarqueBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.SaveCloseBtns
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField
import kotlinx.coroutines.launch
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ArticleAddScreen (
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    barCodeViewModel: BarCodeViewModel,
    articlesViewModel: ArticlesViewModel,
    mainViewModel: MainViewModel,
    networkViewModel: NetworkViewModel,
    ticketRayonViewModel: TicketRayonViewModel,
    articleTxtValidVM: ArticleTextValidationViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel
) {
    val uiWindowState = settingViewModel.uiWindowState

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

     val articleMapByBarCode = mainViewModel.articleMapByBarCode

    val marqueFilter = articlesViewModel.marqueFilter

    val marqueList = mainViewModel.marqueList

    val familleList = mainViewModel.listFamille


    val stationList = mainViewModel.stationList
    val tvaList = mainViewModel.tvaList
    val uniteList = mainViewModel.uniteList
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    val article = articlesViewModel.currentArticle
    val articleState = articleTxtValidVM.stateAddNewArticle
    val familleState = articleTxtValidVM.stateAddNewFamille
    val marqueState = articleTxtValidVM.stateAddNewMarque
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val validationAddArticleEvents = articleTxtValidVM.validationAddArticleEvents

    val utilisateur = mainViewModel.utilisateur
    val typeUtilisateur = utilisateur.typeUser
    val isOperateurPatrimoine = typeUtilisateur == Globals.OPERATEUR_PATRIMOINE

    val selectedBaseconfig = dataViewModel.selectedBaseConfig

    val proInventoryAuthorizationList = getProInventoryDataViewModel.authorizationList

    val haveTicketRayonAuthorisation = proInventoryAuthorizationList.any { it.AutoCodeAu == AuthorizationValuesProInventory.TICKET_RAYON_DRAWER_ITEM }

    val typeProduitList = context.resources.getStringArray(R.array.type_produit)


    val isAdmin =  typeUtilisateur.contains(ADMIN)
    val barCodeInfo =  barCodeViewModel.barCodeInfo

    LaunchedEffect(key1 = Unit) {
        if(isOperateurPatrimoine)
        articleTxtValidVM.onEvent(ArticleFormEvent.TypeProduitChanged(ArticleType.PATRIMOINE.value))
        else articleTxtValidVM.onEvent(ArticleFormEvent.TypeProduitChanged(ArticleType.ARTICLE.value))


        if(uniteList.isNotEmpty()) {
            articleTxtValidVM.onEvent(ArticleFormEvent.UniteChanged(uniteList.first()))
        }


        if(marqueList.isNotEmpty()) {
            articleTxtValidVM.onEvent(ArticleFormEvent.MarqueChanged(marqueList.firstOrNull { it.mARCode == "00" }?: marqueList.first()))
        }

    }
    LaunchedEffect(key1 = barCodeInfo) {
            articleTxtValidVM.onEvent(ArticleFormEvent.BarCodeChanged(barCodeInfo.value))

    }



    LaunchedEffect(key1 = validationAddArticleEvents) {
        articlesViewModel.handleAddArticleEvents(
            popBackStack = {
                articleTxtValidVM.onValidationAddArticleEventsChange(ValidationArticleEvent())
                popBackStack()
                           },
            context = context,
            marqueGeneratedCode = mainViewModel.marqueGeneratedCode,
            toaster = toaster,
            validationAddArticleEvents = validationAddArticleEvents,
            utilisateur = utilisateur,
            addNewMarque = {
                mainViewModel.addNewMarque(
                    designationMarque = it
                )
            }
        )
    }

    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }

    Scaffold(
        topBar = {
            AppBar(
                showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                onNavigationClick = { mainViewModel.onShowDismissScreenAlertDialogChange(true) },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = if(isOperateurPatrimoine) stringResource(id = R.string.ajout_patrimoines) else stringResource(id = R.string.cd_add_client_button),
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected
            )
        },
        //    containerColor = colorResource(id = R.color.black),
        floatingActionButton = {

            AnimatedVisibility(
                visible = true,
                enter = slideInVertically {
                    with(density) { 40.dp.roundToPx() }
                } + fadeIn(),
                exit = fadeOut(
                    animationSpec = keyframes {
                        this.durationMillis = 120
                    }
                )
            ) {
                FloatingActionButton(onClick = {

                 val art= articleMapByBarCode[articleState.barCode]

                    if(art != null) {

                        showToast(
                            context = context,
                            toaster = toaster,
                            message = context.resources.getString(R.string.code_bar_exist, articleState.barCode ) + "\n" + art.aRTDesignation,
                            type = ToastType.Error,
                        )

                        return@FloatingActionButton
                    }

                    if(isOperateurPatrimoine) { articleTxtValidVM.onEvent(ArticleFormEvent.SubmitAddPatrimoine) }
                    else { articleTxtValidVM.onEvent(ArticleFormEvent.SubmitAddArticle) }
                }) {
                    Icon(
                        imageVector = Icons.TwoTone.Save,
                        contentDescription = stringResource(id = R.string.cd_addVisite_button)
                    )
                }
            }


        }
    ) { padding ->
        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            setDialogueVisibility = {
                mainViewModel.onShowDismissScreenAlertDialogChange(it)
            },
            customAction = {
                barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                popBackStack()
            },
            confirmText = stringResource(R.string.confirm),
            cancelText = stringResource(R.string.cancel)

        )

        //todo make it abstract see in inv batiment
        if (mainViewModel.addNewMarqueIsVisible) {
            AddMarqueBottomSheet(
                toaster = toaster,
                marqueTxt = marqueState.designationMarque,
                onMarqueTextChange = {
                    articleTxtValidVM.onAddNewMarqueEvent(
                        MarqueFormEvent.DesignationMarqueChanged(it)
                    )
                },
                setVisibilty = {
                    mainViewModel.onAddNewMarqueVisibilityChange(it)
                },
                onSaveClick = {
                    articleTxtValidVM.onAddNewMarqueEvent(MarqueFormEvent.SubmitAddMarque)
                }
            )
        }

        if (articlesViewModel.addNewFamilleIsVisible) {
            ModalBottomSheet(
                sheetState = sheetState,
                onDismissRequest = {
                    scope.launch {
                        sheetState.hide()
                    }
                    articlesViewModel.onAddNewFamilleVisibilityChange(false)
                },
            ) {

                Column(
                    modifier = Modifier
                        // .padding(padding)
                        // .fillMaxSize()
                        .fillMaxWidth()
                        .wrapContentHeight()

                        .wrapContentSize(Alignment.Center),
                    verticalArrangement = Arrangement.Top,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(text = stringResource(id = R.string.addFamille), style = MaterialTheme.typography.titleMedium)
                    // Divider()
                    Spacer(modifier = Modifier.height(12.dp))

                    EditTextField(
                        text = familleState.designationFamille,
                        errorValue = familleState.designationFamilleError?.asString(),
                        label = stringResource(R.string.designation_famille),
                        onValueChange = {
                            articleTxtValidVM.onAddNewFamilleEvent(
                                FamilleFormEvent.DesignationFamilleChanged(it)
                            )
                        },
                        readOnly = false,
                        enabled = true,
                        showTrailingIcon = true,
                        leadingIcon = Icons.Default.GroupWork,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )



                    EditTextField(
                        text = familleState.designationFamilleCourte,
                        errorValue = familleState.designationFamilleCourteError?.asString(),
                        label = stringResource(R.string.designation_famille_courte),
                        onValueChange = {
                            articleTxtValidVM.onAddNewFamilleEvent(
                                FamilleFormEvent.DesignationFamilleCourteChanged(
                                    it
                                )
                            )
                        },
                        readOnly = false,
                        enabled = true,
                        showTrailingIcon = true,
                        leadingIcon = Icons.AutoMirrored.Filled.ShortText,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Next
                    )


                    EditTextField(
                        text = familleState.ordre,
                        errorValue = familleState.ordreError?.asString(),
                        label = stringResource(R.string.ordre_famille),
                        onValueChange = {
                            articleTxtValidVM.onAddNewFamilleEvent(
                                FamilleFormEvent.OrdreChanged(
                                    it
                                )
                            )
                        },
                        readOnly = false,
                        enabled = true,
                        showTrailingIcon = true,
                        leadingIcon = Icons.Default.Home,
                        keyboardType = KeyboardType.Decimal,
                        imeAction = ImeAction.Next
                    )


                    GenericDropdownMenu (
                        modifier = Modifier.fillMaxWidth(0.75f),
                        label = stringResource(R.string.station),
                        designation = familleState.station.sTATDesg,
                        errorValue = familleState.stationError?.asString(),
                        itemList = stationList,
                        itemExpanded = articlesViewModel.stationExpanded,
                        selectedItem = familleState.station,
                        getItemDesignation = {
                            it.sTATDesg
                        },
                        onClick = {
                            articleTxtValidVM.onAddNewFamilleEvent(
                                FamilleFormEvent.StationChanged(
                                    it
                                )
                            )
                            articlesViewModel.onStationExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            articlesViewModel.onStationExpandedChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )
                    Spacer(modifier = Modifier.height(20.dp))

                    SaveCloseBtns(
                        onSaveClick = {
                            articleTxtValidVM.onAddNewFamilleEvent(FamilleFormEvent.SubmitAddFamille)
                        },
                        onCloseClick = {
                            articlesViewModel.onAddNewFamilleVisibilityChange(false)
                        },
                        lableSave = R.string.cd_addVisite_button,
                        lableClose = R.string.cd_close_button,
                    )
                    HorizontalDivider()
                    Spacer(modifier = Modifier.height(50.dp))
                }




            }
        }

        if (ticketRayonViewModel.showBottomSheet) {
            ModalBottomSheet(
                sheetState = sheetState,
                onDismissRequest = {
                    scope.launch {
                        sheetState.hide()
                    }
                    ticketRayonViewModel.bottomSheetVisibility(false)
                },
            ) {
                Column(
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    //  Text(article.aRTDesignation)
                    Row(
                        horizontalArrangement = Arrangement.Start,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier

                            .fillMaxWidth()
                            .clickable {
                                ticketRayonViewModel.addTicketRayon(article = article)

                                showToast(
                                    context = context,
                                    toaster = toaster,
                                    message = context.resources.getString(R.string.cd_add) + "\n" + context.resources.getString(
                                        R.string.ticket_rayon_add_succ
                                    ),
                                    type = ToastType.Success,
                                )
                            }

                    ){
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = stringResource(id = R.string.save_title)
                        )

                        Text(stringResource(id = R.string.add))
                    }
                    Spacer(Modifier.size(16.dp))
                    /* if(article!=Article())  */
                    Row(
                        horizontalArrangement = Arrangement.Start,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier

                            .fillMaxWidth()
                            .clickable {
// todo ::::


                                showToast(
                                    context = context,
                                    toaster = toaster,
                                    message = "to do print",
                                    type = ToastType.Error,
                                )
                            }

                    ){
                        Icon(
                            imageVector = Icons.Default.Print,
                            contentDescription = stringResource(id = R.string.print),

                            )

                        Text(stringResource(id = R.string.print))
                    }






                    //  if(ticketRayon!= TicketRayon() && !ticketRayon.isSync && !fromConsulationArticle)
                    //   LottieAnim(R.raw.connection_error, 25.dp)
                }
            }
        }


        Column(
            modifier = Modifier
                .padding(padding)
                .verticalScroll(scrollState)
                // .fillMaxSize()
                .fillMaxWidth()
                .wrapContentHeight()
                .wrapContentSize(Alignment.Center),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            EditTextField(
                text = articleState.designation,
                errorValue = articleState.designationError?.asString(),
                label =stringResource(R.string.designation),
                onValueChange = {
                    articleTxtValidVM.onEvent(
                        ArticleFormEvent.DesignationChanged(
                            it
                        )
                    )
                },
                readOnly = false,
                enabled = true,
                showTrailingIcon = true,
                leadingIcon = Icons.Default.Addchart,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )


            Row( horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically) {

                EditTextField(
                    modifier = Modifier.fillMaxWidth(0.8f),
                    text = articleState.barCode,
                    errorValue = articleState.barCodeError?.asString(),
                    label = if(isOperateurPatrimoine) stringResource(R.string.code_article) else stringResource(R.string.bar_code),
                    onValueChange = { articleTxtValidVM.onEvent(ArticleFormEvent.BarCodeChanged(it)) },
                    readOnly = false,
                    enabled = true,
                    showTrailingIcon = true,
                    leadingIcon = Icons.Default.QrCodeScanner,
                    keyboardType = KeyboardType.Password,
                    imeAction = ImeAction.Next
                )
                Spacer(modifier = Modifier.width(16.dp))
                   BareCodeScannerIcon(
                       haveCameraDevice = dataViewModel.getHaveCameraDevice(),
                       toaster = toaster,
                    onClick = {
                        openBareCodeScanner(
                            navigate = { navigate(it) },
                            onBarCodeInfo = { barCodeViewModel.onBarCodeInfo(barCode = it) }
                        )
                    }
                )
            }




            if(!isOperateurPatrimoine) {
                EditTextField(
                    text = articleState.prixHT,
                    errorValue = articleState.prixHTError?.asString(),
                    label = stringResource(R.string.purchase_price_title),
                    onValueChange = {
                        articleTxtValidVM.onEvent(
                            ArticleFormEvent.PrixHTChanged(it)
                        )
                    },
                    readOnly = false,
                    enabled = true,
                    showTrailingIcon = true,
                    leadingIcon = Icons.Default.PriceCheck,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )

                EditTextField(
                    text = articleState.prixTTC,
                    errorValue = articleState.prixTTCError?.asString(),
                    label = stringResource(R.string.price_title),
                    onValueChange = {
                        articleTxtValidVM.onEvent(
                            ArticleFormEvent.PrixTTCChanged(
                                it
                            )
                        )
                    },
                    readOnly = false,
                    enabled = true,
                    showTrailingIcon = true,
                    leadingIcon = Icons.Default.PriceCheck,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )

                EditTextField(
                    text = articleState.qteStock,
                    errorValue = articleState.qteStockError?.asString(),
                    label = stringResource(R.string.stock_quantity_title),
                    onValueChange = {
                        articleTxtValidVM.onEvent(
                            ArticleFormEvent.QteStockChanged(
                                it
                            )
                        )
                    },
                    readOnly = false,
                    enabled = true,
                    showTrailingIcon = true,
                    leadingIcon = Icons.Default.ProductionQuantityLimits,
                    keyboardType = KeyboardType.Decimal,
                    imeAction = ImeAction.Next
                )
            }










            Spacer(modifier = Modifier.height(16.dp))


            GenericDropdownMenu (
                modifier= Modifier.fillMaxWidth(0.95f),
                label = stringResource(R.string.type_produit),
                designation = articleState.typeProduit,
                errorValue = articleState.typeProduitError?.asString(),
                itemList = typeProduitList.toList(),
                showTrailingIcon = !isOperateurPatrimoine,
                itemExpanded = articlesViewModel.typeProduitExpanded,
                selectedItem = articleState.typeProduit,
               // getItemTrailing = { it },
                //getItemSyncStatus = { it.isSync },
                getItemDesignation = { it },
                onClick = {
                    articleTxtValidVM.onEvent(ArticleFormEvent.TypeProduitChanged(it))
                    articlesViewModel.onTypeProduitExpandedChange(false)
                },
                onItemExpandedChange = {
                    articlesViewModel.onTypeProduitExpandedChange(it)
                },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }
            )


                Spacer(modifier = Modifier.height(16.dp))
                Row (
                    modifier = Modifier.fillMaxWidth().padding(start = 16.dp, end = 16.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.Top
                ) {
                    if(!isOperateurPatrimoine) {
                        GenericDropdownMenu (
                            modifier = Modifier.fillMaxWidth(0.45f),
                            label = stringResource(R.string.tva),
                            designation = removeTrailingZeroInDouble(articleState.tva.tVACode).ifEmpty { "0" },
                            errorValue = articleState.tvaError?.asString(),
                            itemList = tvaList,
                            itemExpanded = articlesViewModel.tvaExpanded,
                            selectedItem = articleState.tva,
                            getItemDesignation = {
                                removeTrailingZeroInDouble(it.tVACode).ifEmpty { "0" }
                            },
                            getItemTrailing = { "%" },
                            onClick = {
                                articleTxtValidVM.onEvent(
                                    ArticleFormEvent.TvaChanged(
                                        it
                                    )
                                )
                                articlesViewModel.onTvaExpandedChange(false)
                            },
                            onItemExpandedChange = {
                                articlesViewModel.onTvaExpandedChange(it)
                            },
                            lottieAnimEmpty = {
                                LottieAnim(lotti = R.raw.emptystate)
                            },
                            lottieAnimError = {
                                LottieAnim(lotti = R.raw.connection_error, size = it)
                            }
                        )
                        Spacer(modifier = Modifier.width(16.dp))
                    }

                    GenericDropdownMenu (
                        modifier = Modifier.fillMaxWidth(),
                        label = stringResource(R.string.unite),
                        designation = articleState.unite.uNIDesignation?: articleState.unite.uNICode,
                        errorValue = articleState.uniteError?.asString(),
                        itemList = uniteList,
                        itemExpanded = articlesViewModel.uniteExpanded,
                        selectedItem = articleState.unite,
                        getItemDesignation = {
                            it.uNIDesignation?: it.uNICode
                        },
                        onClick = {
                            articleTxtValidVM.onEvent(
                                ArticleFormEvent.UniteChanged(
                                    it
                                )
                            )
                            articlesViewModel.onUniteExpandedChange(false)
                        },
                        onItemExpandedChange = {
                            articlesViewModel.onUniteExpandedChange(it)
                        },
                        lottieAnimEmpty = {
                            LottieAnim(lotti = R.raw.emptystate)
                        },
                        lottieAnimError = {
                            LottieAnim(lotti = R.raw.connection_error, size = it)
                        }
                    )

            }

            Spacer(modifier = Modifier.height(16.dp))


             Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {

                 GenericDropdownMenu (
                 modifier= Modifier.fillMaxWidth(0.75f),
                 label = stringResource(R.string.famille_title),
                 designation = articleState.famille.fAMLib?: articleState.famille.fAMDesgCourte,
                 errorValue = articleState.familleError?.asString(),
                 itemList = familleList,
                 itemExpanded = articlesViewModel.familleExpanded,
                 selectedItem = articleState.famille,
                     getItemTrailing = { it.fAMCode },
                     getItemSyncStatus = { it.isSync },
                     getItemDesignation = { it.fAMLib?: it.fAMDesgCourte },
                 onClick = {
                     articleTxtValidVM.onEvent(
                         ArticleFormEvent.FamilleChanged(
                             it
                         )
                     )
                     articlesViewModel.onFamilleExpandedChange(false)
                 },
                 onItemExpandedChange = {
                     articlesViewModel.onFamilleExpandedChange(it)
                 },
                     lottieAnimEmpty = {
                         LottieAnim(lotti = R.raw.emptystate)
                     },
                     lottieAnimError = {
                         LottieAnim(lotti = R.raw.connection_error, size = it)
                     }
                 )

                Icon(
                    modifier = Modifier.clickable {
                        articleTxtValidVM.resetFamilleVariable()

                        val prefix = mainViewModel.prefixList.firstOrNull { it.pREIdTable == "Famille" }?.pREPrefixe?: "0"
                        articlesViewModel.generteNewFamilleCode(prefix = prefix)
                        articlesViewModel.onAddNewFamilleVisibilityChange(true)
                    },
                    imageVector = Icons.TwoTone.AddCircle,
                    contentDescription = stringResource(id = R.string.cd_addVisite_button)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
             Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                 GenericDropdownMenu(
                     modifier = Modifier.fillMaxWidth(0.75f),
                     label = stringResource(R.string.marque_title),
                     designation = articleState.marque.mARDesignation,
                     errorValue = articleState.marqueError?.asString(),
                     itemList = if(marqueFilter.isNotEmpty()) marqueList.filter { it.mARDesignation.lowercase(Locale.ROOT).contains(marqueFilter.lowercase(Locale.ROOT)) } else marqueList,
                     itemExpanded = articlesViewModel.marqueExpanded,
                     selectedItem = articleState.marque,
                     fiterValue = marqueFilter,
                     onFilterValueChange = { articlesViewModel.onMarqueFilterChange(it) },
                     showFilter = true,
                     onItemExpandedChange = { articlesViewModel.onMarqueExpandedChange(it) },
                     getItemDesignation = { it.mARDesignation },
                     getItemSyncStatus = { it.isSync },
                     getItemTrailing = { it.mARCode },
                     onClick = {
                         articleTxtValidVM.onEvent(
                             ArticleFormEvent.MarqueChanged(it)
                         )
                         articlesViewModel.onMarqueExpandedChange(false)

                     },
                     lottieAnimEmpty = {
                         LottieAnim(lotti = R.raw.emptystate)
                     },
                     lottieAnimError = {
                         LottieAnim(lotti = R.raw.connection_error, size = it)
                     }
                 )

                Icon(
                    modifier = Modifier.clickable {
                        articleTxtValidVM.resetMarqueVariable()
                        mainViewModel.generteNewMarqueCode()
                        mainViewModel.onAddNewMarqueVisibilityChange(true)
                    },
                    imageVector = Icons.TwoTone.AddCircle,
                    contentDescription = stringResource(id = R.string.cd_addVisite_button)
                )

            }

            Spacer(modifier = Modifier.height(16.dp))

            if(!isOperateurPatrimoine) {
                GenericDropdownMenu(
                    modifier = Modifier.fillMaxWidth(0.95f),
                    label = stringResource(R.string.type_prix),
                    designation = articleState.typePrix.type_PrixUnitaireHT,
                    errorValue = articleState.typePrixError?.asString(),
                    itemList = articlesViewModel.typePrixList,
                    itemExpanded = articlesViewModel.typePrixExpanded,
                    selectedItem = articleState.typePrix,
                    getItemDesignation = { it.type_PrixUnitaireHT },
                    onClick = {
                        articleTxtValidVM.onEvent(
                            ArticleFormEvent.TypePrixChanged(
                                it
                            )
                        )
                        articlesViewModel.onTypePrixExpandedChange(false)
                    },
                    onItemExpandedChange = {
                        articlesViewModel.onTypePrixExpandedChange(it)
                    },
                    lottieAnimEmpty = {
                        LottieAnim(lotti = R.raw.emptystate)
                    },
                    lottieAnimError = {
                        LottieAnim(lotti = R.raw.connection_error, size = it)
                    },
                )


            }


            Spacer(modifier = Modifier.height(90.dp))
        }



    }

}

















