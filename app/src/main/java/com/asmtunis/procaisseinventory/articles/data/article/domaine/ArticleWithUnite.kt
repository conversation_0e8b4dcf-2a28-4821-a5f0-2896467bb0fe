package com.asmtunis.procaisseinventory.articles.data.article.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ArticleWithUniteDeprecated(
    @Embedded
    @SerialName("article")
    var article: Article = Article(),

    @Relation(
        parentColumn = "UNITE_ARTICLE_CodeUnite",
        entityColumn = "UNI_Code"
    )
    @SerialName("unite")
    var unite: Unite? = null,

//    @Relation(
//        parentColumn = "ART_Code",
//        entityColumn = "SART_CodeArt"
//    )
//    @SerialName("stationStockArticle")
//    var stationStockArticle: List<StationStockArticle> = emptyList(),

    @Relation(
        parentColumn = "ART_Code",
        entityColumn = "UNITE_ARTICLE_CodeArt"
    )
    @SerialName("uniteArticle")
    var uniteArticle: List<UniteArticle>? = null,


    @Relation(
        parentColumn = "ART_CodeBar",
        entityColumn = "Fils_CodeBar"
    )
    @SerialName("articleCodeBar")
    var articleCodeBar: ArticleCodeBar? = null,


    )