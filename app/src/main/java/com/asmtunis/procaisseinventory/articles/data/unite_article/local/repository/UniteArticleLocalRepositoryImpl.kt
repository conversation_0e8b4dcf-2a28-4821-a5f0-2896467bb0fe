package com.asmtunis.procaisseinventory.articles.data.unite_article.local.repository

import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.articles.data.unite_article.local.dao.UniteArticleDAO
import kotlinx.coroutines.flow.Flow


class UniteArticleLocalRepositoryImpl(
        private val uniteArticleDAO: UniteArticleDAO
    ) : UniteArticleLocalRepository {
    override fun upsert(value: UniteArticle) = uniteArticleDAO.insert(value)

    override fun upsertAll(value: List<UniteArticle>) = uniteArticleDAO.insertAll(value)

    override fun deleteAll() = uniteArticleDAO.deleteAll()

    override fun getAll(): Flow<List<UniteArticle>> = uniteArticleDAO.all
}