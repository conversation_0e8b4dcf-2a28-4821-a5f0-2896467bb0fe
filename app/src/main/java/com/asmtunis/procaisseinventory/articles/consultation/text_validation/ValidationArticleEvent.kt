package com.asmtunis.procaisseinventory.articles.consultation.text_validation

import com.asmtunis.procaisseinventory.articles.consultation.text_validation.article.ArticleFormState
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.famille.FamilleFormState
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.marque.MarqueFormState

open class ValidationArticleEvent {
    data class Article(val addNewtArticle: ArticleFormState) : ValidationArticleEvent()
    data class Famille(val addNewtFamille: FamilleFormState) : ValidationArticleEvent()
    data class Marque(val addNewtMarque: MarqueFormState) : ValidationArticleEvent()
}