package com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.core.Globals.PRIX_PUBLIQUE
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite


data class SelectedArticle(
  //  var articleCodeBar: String,
    var article: Article = Article(),
    var stationStockArticleList: StationStockArticle = StationStockArticle(),
    var uniteArticle: UniteArticle = UniteArticle(),
    var unite: Unite = Unite(),
    var tva: Tva = Tva(),
   // var articleCodeBar: ArticleCodeBar = ArticleCodeBar(),

    var uniteArticleError: UiText? = null,

 //   var stationStockArticle: StationStockArticle =  StationStockArticle(), //for pro inventory )
    var qteStationFromDB: String = "", //TODO VERIFY IF FROM ARTICLE TABLE OR StationStockArticle TABLE !
    var numSerie: String = "", // for inventaire patrimoine
    var quantity: String = "",

    var prixAchatHt: String = "", //for pro inventory (bn transfert)
    var prixAchatHtError: UiText? = null, //for pro inventory (bn transfert)
    var quantityError: UiText? = null,
    // var controlQuantity : Boolean = true,
    var selectedPriceCategory: String = PRIX_PUBLIQUE,


    var prixCaisse: String = "",//Prix Apré remise
    var prixVente: String = "",//Prix Avant remise
    var discount: String = article.tauxSolde?: "", //taux remise set to default with taux sold from article
    var mntDiscount: String = "",//mnt remise
    var discountError: UiText? = null,
    var lTMtTTC: String = "",
    var mtTTCError: UiText? = null,
    var lTMtBrutHT: String = "",
    var lTMtNetHT: String = "",
    var lTPuTTC: String = "",
    var lTPuHT: String = "",

    var tvaError: UiText? = null,
    var mntTva: String = ""
)