package com.asmtunis.procaisseinventory.articles.data.article.remote.api

import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCount
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ClientArticlePrix
import com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticle
import com.asmtunis.procaisseinventory.articles.data.article.domaine.PaginationResponseArticleCodeBare
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class ArticlesApiImpl(private val client: HttpClient) : ArticlesApi {


    override suspend fun getArticles(
        baseConfig: String,
        ddm: String,
        station: String
    ): Flow<DataResult<List<Article>?>> = flow {

        val queryParams = mutableMapOf<String, String>()

        if(ddm!="")
            queryParams["ddm"] = ddm

        queryParams += mapOf("station" to station)

        val result = executePostApiCall<List<Article>?>(
            client = client,
            endpoint = Urls.GET_ARTICLES,
            queryParams = queryParams,
            baseConfig = baseConfig
        )
        emitAll(result) 
    }

    override suspend fun getArticlesPagination(
        baseConfig: String,
        page: String,
        limit: String
    ): Flow<DataResult<PaginationResponseArticle?>> = flow {
        val queryParams = mapOf(
            "page" to page,
            "limit" to limit
        )

        val result = executePostApiCall<PaginationResponseArticle?>(
            client = client,
            endpoint = Urls.GET_ARTICLES,
            queryParams = queryParams,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getArticlesPaginationWithStation(
        baseConfig: String,
        page: String,
        limit: String,
        ddm: String,
        station: String
    ): Flow<DataResult<PaginationResponseArticle?>> = flow {
        var queryParams = emptyMap<String, String>().toMutableMap()

        if(ddm!="")
            queryParams = mapOf("ddm" to ddm).toMutableMap()

        queryParams += mapOf(
            "station" to station,
            "page" to page,
            "limit" to limit
        )



        val result = executePostApiCall<PaginationResponseArticle?>(
            client = client,
            endpoint = Urls.GET_ARTICLES_PAGINATION,
            queryParams = queryParams,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getArticlesCodeBarePagination(
        baseConfig: String,
        page: String,
        limit: String
    ): Flow<DataResult<PaginationResponseArticleCodeBare?>> = flow {
        val queryParams = mapOf(
            "page" to page,
            "limit" to limit
        )

        val result = executePostApiCall<PaginationResponseArticleCodeBare?>(
            client = client,
            endpoint = Urls.GET_ARTICLES_CODE_BARE_PAGINATION,
            queryParams = queryParams,
            baseConfig = baseConfig
        )
        emitAll(result)
    }


    override suspend fun getCountArticle(baseConfig: String): Flow<DataResult<ArticleCount?>> = flow {

        val result = executePostApiCall<ArticleCount?>(
            client = client,
            endpoint = Urls.GET_COUNT_ARTICLE,
            baseConfig = baseConfig
        )
        emitAll(result) 
    }



    override suspend fun addArticles(baseConfig: String): Flow<DataResult<List<Article>?>>  = flow {
      //  val headers = mapOf("Application-name" to PRO_INVENTORY_AUTHORIZATION_TYPE_MENU)

        val result = executePostApiCall<List<Article>?>(
            client = client,
            endpoint = Urls.ADD_ARTICLE_V2,
           // headers = headers,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getArticlesByStation(baseConfig: String): Flow<DataResult<List<Article>?>>  = flow {

        val result = executePostApiCall<List<Article>?>(
            client = client,
            endpoint = Urls.GET_ARTICLES_BY_STATION,
            /*, mapOf(),*/
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getClientArticlePrix(baseConfig: String): Flow<DataResult<List<ClientArticlePrix>?>>  = flow {
        val result = executePostApiCall<List<ClientArticlePrix>?>(
            client = client,
            endpoint = Urls.GET_ARTICLES_CLIENT_PRIX,
            /*, mapOf(),*/
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getArticleCodeBare(baseConfig: String): Flow<DataResult<List<ArticleCodeBar>?>>  = flow {
        val result = executePostApiCall<List<ArticleCodeBar>?>(
            client = client,
            endpoint = Urls.GET_CODE_TEST,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun addArticleCodeBarMobile(baseConfig: String): Flow<DataResult<List<ArticleCodeBar>?>>  {
        TODO("Not yet implemented")
    }
}
