package com.asmtunis.procaisseinventory.data.etablisement.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow

class EtablissementApiImpl(private val client: HttpClient) : EtablissementApi {
    override suspend fun getEtablisements(baseConfig: String): Flow<DataResult<Etablisement>> = flow {
        val result = executePostApiCall<Etablisement>(
            client = client,
            endpoint = Urls.GET_ETABLISSEMENTS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}
