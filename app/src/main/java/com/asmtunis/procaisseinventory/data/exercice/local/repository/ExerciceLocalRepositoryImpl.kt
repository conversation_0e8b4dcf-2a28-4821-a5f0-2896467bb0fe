package com.asmtunis.procaisseinventory.data.exercice.local.repository

import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import com.asmtunis.procaisseinventory.data.exercice.local.dao.ExerciceDAO
import kotlinx.coroutines.flow.Flow


class ExerciceLocalRepositoryImpl(
        private val exerciceDAO: ExerciceDAO
    ) : ExerciceLocalRepository {
    override fun upsertAll(value: List<Exercice>) = exerciceDAO.insertAll(value)
    override fun upsert(value: Exercice)= exerciceDAO.insert(value)

    override fun deleteAll()= exerciceDAO.deleteAll()

    override fun getAll(): Flow<List<Exercice>?> = exerciceDAO.all

    override fun getOneById(code: String): Flow<Exercice?> = exerciceDAO.getOneById(code)
}