package com.asmtunis.procaisseinventory.data.banques.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.BANQUE_TABLE)
@Serializable
data class Banque (
    @PrimaryKey
    @ColumnInfo(name = "BAN_Code")
    @SerialName("BAN_Code")
    var bANCode: String = "",

    @ColumnInfo(name = "BAN_Des")
    @SerialName("BAN_Des")
    var bANDes: String? = null

)
