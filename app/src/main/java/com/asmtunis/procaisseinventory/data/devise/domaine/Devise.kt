package com.asmtunis.procaisseinventory.data.devise.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.DEVISE_TABLE)
@Serializable
data class Devise(
    @PrimaryKey
    @ColumnInfo(name = "Devise")
    @SerialName("Devise")

    var devise: String = "",

    @ColumnInfo(name = "Unite")
    @SerialName("Unite")

    var unite: String? = "",

    @ColumnInfo(name = "Symbole")
    @SerialName("Symbole")

    var symbole: String? = "",

    @ColumnInfo(name = "Principale")
    @SerialName("Principale")

    var principale: String? = "",

    @ColumnInfo(name = "Cours")
    @SerialName("Cours")

    var cours: String? = "",

    @ColumnInfo(name = "Activite")
    @SerialName("Activite")

    var activite: String? = "",

    @ColumnInfo(name = "Nbre_Chiffre_virgule")
    @SerialName("Nbre_Chiffre_virgule")

    var nbreChiffreVirgule: Int = 0


)

