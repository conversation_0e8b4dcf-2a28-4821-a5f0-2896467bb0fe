package com.asmtunis.procaisseinventory.data.devise.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class DeviseApiImpl(private val client: HttpClient) : DeviseApi {
    override suspend fun getDevises(baseConfig: String): Flow<DataResult<List<Devise>>> = flow {

        val result = executePostApiCall<List<Devise>>(
            client = client,
            endpoint = Urls.GET_DEVISES,
            baseConfig = baseConfig
        )
        emitAll(result)
    }
}