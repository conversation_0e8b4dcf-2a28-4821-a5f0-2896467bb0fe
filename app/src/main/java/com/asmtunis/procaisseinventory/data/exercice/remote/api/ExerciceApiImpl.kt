package com.asmtunis.procaisseinventory.data.exercice.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class ExerciceApiImpl(private val client: HttpClient) : ExerciceApi {
    override suspend fun getExercice(baseConfig: String): Flow<DataResult<Exercice>> = flow {

        val result = executePostApiCall<Exercice>(
            client = client,
            endpoint = Urls.GET_EXERCICE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}