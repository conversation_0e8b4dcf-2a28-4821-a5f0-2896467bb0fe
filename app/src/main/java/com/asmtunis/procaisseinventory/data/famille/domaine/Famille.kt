package com.asmtunis.procaisseinventory.data.famille.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProInventoryConstants.FAMILLE_TABLE)
@Serializable
data class Famille(
     @SerialName("FAM_Code")
    @ColumnInfo(name = "FAM_Code")
    @PrimaryKey var fAMCode: String ="",


    @ColumnInfo(name = "FAM_Lib")
    @SerialName("FAM_Lib")
    var fAMLib : String? = "",

    @ColumnInfo(name = "FAM_User")
    @SerialName("FAM_User")
    var fAMUser: String? = "",

    @ColumnInfo(name = "FAM_Station")
    @SerialName("FAM_Station")
    var fAMStation: String? = "",

    @ColumnInfo(name = "FAM_IsTaktile")
    @SerialName("FAM_IsTaktile")
    var fAMIsTaktile: String? = "",

    @ColumnInfo(name = "FAM_Image")
    @SerialName("FAM_Image")
    var fAMImage: String? = "",

    @ColumnInfo(name = "FAM_Couleur")
    @SerialName("FAM_Couleur")
    var fAMCouleur: String? = "",

    @ColumnInfo(name = "FAM_DesgCourte")
    @SerialName("FAM_DesgCourte")
    var fAMDesgCourte: String = "",

    @ColumnInfo(name = "FAM_NumOrdre")
    @SerialName("FAM_NumOrdre")
    var fAMNumOrdre: Int? = -1,

    @ColumnInfo(name = "FAM_export")
    @SerialName("FAM_export")
    var fAMExport: String? = "",

    @ColumnInfo(name = "FAM_DDm")
    @SerialName("FAM_DDm")
    var fAMDDm: String? = "",

    @ColumnInfo(name = "FAM_Alphabetique")
    @SerialName("FAM_Alphabetique")
    var fAMAlphabetique: String? = "",

    @ColumnInfo(name = "photo_Path")
    @SerialName("photo_Path")
    var photoPath: String? = "",

    @ColumnInfo(name = "SousFAM_Code")
    @SerialName("SousFAM_Code")
    var sousFAMCode: String? = "",

    @ColumnInfo(name = "NumAppel")
    @SerialName("NumAppel")
    var numAppel: String? = "",

     @ColumnInfo(name = "DDmM")
     @SerialName("DDmM")
     var dDmM: String? = ""




): BaseModel()