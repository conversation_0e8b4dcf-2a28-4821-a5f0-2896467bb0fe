package com.asmtunis.procaisseinventory.data.unite.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class UniteApiImpl(private val client: HttpClient) : UniteApi {
    override suspend fun getUnite(baseConfig: String): Flow<DataResult<List<Unite>>> = flow {

        val result = executePostApiCall<List<Unite>>(
            client = client,
            endpoint = Urls.GET_UNITE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addUniteMobile(baseConfig: String): Flow<DataResult<List<Unite>>> = flow {
        val result = executePostApiCall<List<Unite>>(
            client = client,
            endpoint = Urls.ADD_UNITE_MOBILE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}