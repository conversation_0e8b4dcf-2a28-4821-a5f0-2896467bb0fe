package com.asmtunis.procaisseinventory.data.carte_resto.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class CarteRestoApiImpl(private val client: HttpClient) : CarteRestoApi {
    override suspend fun getCartesResto(baseConfig: String): Flow<DataResult<List<CarteResto>>> = flow {
        val result = executePostApiCall<List<CarteResto>>(
            client = client,
            endpoint = Urls.GET_CARTES_RESTO,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getCarteRestoByCode(baseConfig: String): Flow<DataResult<CarteResto>> = flow {

        val result = executePostApiCall<CarteResto>(
            client = client,
            endpoint = Urls.GET_CARTES_RESTO_BY_CODE,
            baseConfig = baseConfig
        )

        emitAll(result)

    }
    }