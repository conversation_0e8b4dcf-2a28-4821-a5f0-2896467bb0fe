package com.asmtunis.procaisseinventory.data.exercice.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.EXERCICE_TABLE,primaryKeys = ["Exercice_Code"])

@Serializable
data class Exercice  (
    @SerialName("Exercice_Code")
    @ColumnInfo(name = "Exercice_Code")
    var exerciceCode: String = ""
)//: BaseModel()