package com.asmtunis.procaisseinventory.data.carte_resto.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.CARTE_RESTO_TABLE)
@Serializable
data class CarteResto (
    @PrimaryKey
    @ColumnInfo(name = "Code")
    @SerialName("Code")
    var code: String = "",

    @ColumnInfo(name = "Societe")
    @SerialName("Societe")
    var societe: String = ""
)
