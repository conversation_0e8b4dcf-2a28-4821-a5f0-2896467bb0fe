package com.asmtunis.procaisseinventory.data.facture.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class FactureApiImpl(private val client: HttpClient) : FactureApi {
    override suspend fun getFacture(baseConfig: String): Flow<DataResult<List<Facture>>> = flow {

        val result = executePostApiCall<List<Facture>>(
            client = client,
            endpoint = Urls.GET_FACTURE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}