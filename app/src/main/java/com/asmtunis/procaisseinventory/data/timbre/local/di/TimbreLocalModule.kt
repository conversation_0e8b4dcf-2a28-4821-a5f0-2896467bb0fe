package com.asmtunis.procaisseinventory.data.timbre.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.timbre.local.dao.TimbreDAO
import com.asmtunis.procaisseinventory.data.timbre.local.repository.TimbreLocalRepository
import com.asmtunis.procaisseinventory.data.timbre.local.repository.TimbreLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class TimbreLocalModule {

    @Provides
    @Singleton
    fun provideTimbreDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.timbreDAO()

    @Provides
    @Singleton
    @Named("Timbre")
    fun provideTimbreRepository(
        timbreDAO: TimbreDAO
    ): TimbreLocalRepository = TimbreLocalRepositoryImpl(
        timbreDAO = timbreDAO

    )

}