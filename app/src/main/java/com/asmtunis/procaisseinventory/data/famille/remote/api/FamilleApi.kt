package com.asmtunis.procaisseinventory.data.famille.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.famille.domaine.AddFamilleResponse
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import kotlinx.coroutines.flow.Flow


interface FamilleApi {



    suspend fun getFamilles(baseConfig: String): Flow<DataResult<List<Famille>>>
    suspend fun addFamilleMobile(baseConfig: String): Flow<DataResult<List<AddFamilleResponse>>>
}