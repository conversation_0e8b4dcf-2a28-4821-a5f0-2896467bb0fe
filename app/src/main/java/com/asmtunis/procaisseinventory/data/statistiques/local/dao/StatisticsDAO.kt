package com.asmtunis.procaisseinventory.data.statistiques.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.data.statistiques.domaine.Statistics
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.STATISTIQUES_TABLE
import kotlinx.coroutines.flow.Flow




@Dao
interface StatisticsDAO {
    @get:Query("SELECT * FROM $STATISTIQUES_TABLE")
    val all: Flow<Statistics>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(item: Statistics)

    @Query("DELETE FROM $STATISTIQUES_TABLE")
    fun deleteAll()
}