package com.asmtunis.procaisseinventory.data.sessioncaisse.local.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Upsert
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.SESSION_CAISSE_TABLE
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import kotlinx.coroutines.flow.Flow


@Dao
interface SessionCaisseDAO {
   @get:Query("SELECT * FROM $SESSION_CAISSE_TABLE order by strftime('%Y-%m-%d %H-%M',SC_DateHeureCrea) desc")
   //    @get:Query("SELECT * FROM $SESSION_CAISSE_TABLE")
    val all: Flow<List<SessionCaisse>>

    @get:Query("SELECT * FROM $SESSION_CAISSE_TABLE order by strftime('%Y-%m-%d %H-%M',SC_DateHeureCrea) desc")
    val allMutable: Flow<List<SessionCaisse?>?>?

   // @get:Query("SELECT * FROM $SESSION_CAISSE_TABLE WHERE isSync=0 and  (Status='INSERTED'  or Status='UPDATED')")
    @get:Query("SELECT * FROM $SESSION_CAISSE_TABLE")
    val getNotSync: Flow<List<SessionCaisse>>

    @Query("SELECT * FROM $SESSION_CAISSE_TABLE WHERE SC_Caisse = :code ")
    fun getOneByCode(code: String?): Flow<SessionCaisse>

    @get:Query("SELECT * FROM $SESSION_CAISSE_TABLE LIMIT 1")
    val one: SessionCaisse?

    @Upsert
    fun insert(item: SessionCaisse)

    @Upsert
    fun insertAll(items: List<SessionCaisse>)

    @Query("DELETE FROM $SESSION_CAISSE_TABLE")
    fun deleteAll()
}
