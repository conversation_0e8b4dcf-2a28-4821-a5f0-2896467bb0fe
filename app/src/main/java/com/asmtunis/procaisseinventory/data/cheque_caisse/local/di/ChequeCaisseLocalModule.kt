package com.asmtunis.procaisseinventory.data.cheque_caisse.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.cheque_caisse.local.dao.ChequeCaisseDAO
import com.asmtunis.procaisseinventory.data.cheque_caisse.local.repository.ChequeCaisseLocalRepository
import com.asmtunis.procaisseinventory.data.cheque_caisse.local.repository.ChequeCaisseLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class ChequeCaisseLocalModule {

    @Provides
    @Singleton
    fun provideChequeCaisseDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.chequeCaisseDAO()

    @Provides
    @Singleton
    @Named("ChequeCaisse")
    fun provideChequeCaisseRepository(
        chequeCaisseDAO: ChequeCaisseDAO
    ): ChequeCaisseLocalRepository = ChequeCaisseLocalRepositoryImpl(
        chequeCaisseDAO = chequeCaisseDAO

    )


}