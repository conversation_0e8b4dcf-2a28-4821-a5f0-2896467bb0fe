package com.asmtunis.procaisseinventory.data.facture.local.repository

import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import com.asmtunis.procaisseinventory.data.facture.local.dao.FactureDAO
import kotlinx.coroutines.flow.Flow


class FactureLocalRepositoryImpl(private val factureDAO: FactureDAO) : FactureLocalRepository {
    override fun upsertAll(value: List<Facture>) = factureDAO.insertAll(value)

    override fun upsert(value: Facture) = factureDAO.insert(value)

    override fun deleteAll() = factureDAO.deleteAll()

    override fun getAll(): Flow<List<Facture?>?> = factureDAO.all

    override fun getByFactNumBc(code: Int): Flow<Facture?> = factureDAO.getByFactNumBc(code)
    override fun getByTicket(
        code: String,
        user: String,
        station: String
    ): Flow<Facture?>  = factureDAO.getByTicket(
        code = code,
        user = user,
        station = station
    )


}