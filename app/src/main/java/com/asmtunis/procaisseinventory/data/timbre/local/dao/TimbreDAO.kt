package com.asmtunis.procaisseinventory.data.timbre.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.TIMBRE_TABLE
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import kotlinx.coroutines.flow.Flow


@Dao
interface TimbreDAO {
    @get:Query("SELECT * FROM $TIMBRE_TABLE")
    val all: Flow<List<Timbre>>

    @get:Query("SELECT * FROM $TIMBRE_TABLE WHERE TIMB_Etat='Actif'")
    val actif: Flow<List<Timbre>>

    @Query("SELECT * FROM $TIMBRE_TABLE WHERE TIMB_Code=:code")
    fun getByID(code: String): Flow<Timbre>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(timbres: List<Timbre>)

    @Query("DELETE FROM $TIMBRE_TABLE")
    fun deleteAll()
}
