package com.asmtunis.procaisseinventory.data.cheque_caisse.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.CHEQUE_CAISSE_TABLE
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import kotlinx.coroutines.flow.Flow


@Dao
interface ChequeCaisseDAO {
    @get:Query("SELECT * FROM $CHEQUE_CAISSE_TABLE")
    val all: Flow<List<ChequeCaisse>>

    @Query("SELECT * FROM $CHEQUE_CAISSE_TABLE WHERE reglement_m = :code")
    fun getByReglementM(code: String): Flow<List<ChequeCaisse>>

    @Query("SELECT * FROM $CHEQUE_CAISSE_TABLE WHERE NumCheque = :num")
    fun getOneByCode(num: String): Flow<ChequeCaisse>

    @get:Query("SELECT * FROM $CHEQUE_CAISSE_TABLE LIMIT 1")
    val one: Flow<ChequeCaisse>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: ChequeCaisse)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<ChequeCaisse>)

    @Query("DELETE FROM $CHEQUE_CAISSE_TABLE")
    fun deleteAll()

    @Query("DELETE FROM ${CHEQUE_CAISSE_TABLE} where Reglement_M=:codeM and reglement_exercice = :exercice")
    fun deleteByCodeM(codeM: String, exercice: String)

    @Query("UPDATE $CHEQUE_CAISSE_TABLE SET Reglement=:regCode , Status = 'SELECTED' , IsSync = 1 where Reglement = :regCodeM")
    fun updateRegCodeAndState(regCode: String, regCodeM: String)
}
