package com.asmtunis.procaisseinventory.data.timbre.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.TIMBRE_TABLE)
@Serializable
data class Timbre (
    @PrimaryKey
    @ColumnInfo(name = "TIMB_Code")
    @SerialName("TIMB_Code")
    
    var tIMBCode: String = "",

    @ColumnInfo(name = "TIMB_Value")
    @SerialName("TIMB_Value")
    
    var tIMBValue: String? = "",

    @ColumnInfo(name = "TIMB_Loi")
    @SerialName("TIMB_Loi")
    
    var tIMBLoi: String? = "",

    @ColumnInfo(name = "TIMB_Etat")
    @SerialName("TIMB_Etat")
    
    var tIMBEtat: String? = "",

    @ColumnInfo(name = "TIMB_User")
    @SerialName("TIMB_User")
    
    var tIMBUser: String? = "",

    @ColumnInfo(name = "TIMB_Station")
    @SerialName("TIMB_Station")
    
    var tIMBStation: String? = "",

    @ColumnInfo(name = "TIMB_export")
    @SerialName("TIMB_export")
    
    var tIMBExport: String? = "",

    @ColumnInfo(name = "TIMB_DDm")
    @SerialName("TIMB_DDm")
    
    var tIMBDDm: String? = "",

    @ColumnInfo(name = "TIMB_CodCompta")
    @SerialName("TIMB_CodCompta")
    
    var tIMBCodCompta: String? = "",
    
)
