package com.asmtunis.procaisseinventory.data.unite.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.UNITE_TABLE
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import kotlinx.coroutines.flow.Flow


@Dao
interface UniteDAO {
    @get:Query("SELECT * FROM $UNITE_TABLE")
    val all: Flow<List<Unite>>




    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Unite)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Unite>)

    @Query("DELETE FROM $UNITE_TABLE")
    fun deleteAll()
}