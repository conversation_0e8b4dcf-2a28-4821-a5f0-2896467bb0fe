package com.asmtunis.procaisseinventory.data.carte_resto.local.repository

import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import com.asmtunis.procaisseinventory.data.carte_resto.local.dao.CarteRestoDAO
import kotlinx.coroutines.flow.Flow


class CarteRestoLocalRepositoryImpl(private val carteRestoDAO: CarteRestoDAO) : CarteRestoLocalRepository {
    override fun upsertAll(value: List<CarteResto>) = carteRestoDAO.insertAll(value)

    override fun deleteAll() = carteRestoDAO.deleteAll()

    override fun getAll(): Flow<List<CarteResto>> = carteRestoDAO.all
}