package com.asmtunis.procaisseinventory.data.station.local.station.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import kotlinx.coroutines.flow.Flow


@Dao
interface StationDAO {
    @get:Query("SELECT * FROM Station")
    val all: Flow<List<Station>>

    @get:Query("SELECT STAT_Desg FROM Station")
    val list: Flow<List<String?>>

    @Query("SELECT STAT_Desg FROM Station where (STAT_Code=:stationCode)")
    fun getListWithDesgByCode(stationCode: String?): Flow<List<String?>>

    @Query("SELECT * FROM Station where STAT_Code=:code")
    fun getByCode(code: String): Flow<Station>

    @Query("SELECT * FROM Station where STAT_Code=:code")
    fun getByCodeToList(code: String?): Flow<List<Station>>

    @Query("SELECT STAT_Desg FROM Station where STAT_Code=:code")
    fun getNameByCode(code: String): Flow<String>

    @Query("SELECT COUNT(*) FROM Station")
    fun count(): Flow<Int>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Station)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Station>)

    @Query("DELETE FROM Station")
    fun deleteAll()

    @Query("DELETE FROM Station where STAT_Code=:code")
    fun deleteByCode(code: String)
}
