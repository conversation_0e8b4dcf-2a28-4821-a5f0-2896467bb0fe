package com.asmtunis.procaisseinventory.data.station.local.station_stock_article.repository

import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticleWithStation
import com.asmtunis.procaisseinventory.data.station.local.station_stock_article.dao.StationStockArticleDAO
import kotlinx.coroutines.flow.Flow


class StationStockArticleLocalRepositoryImpl(
        private val stationStockArticleDAO: StationStockArticleDAO
    ) : StationStockArticleLocalRepository {

    override fun upsertAll(value: List<StationStockArticle>) = stationStockArticleDAO.insertAll(value)

    override fun upsert(value: StationStockArticle) = stationStockArticleDAO.insert(value)

    override fun deleteAll() = stationStockArticleDAO.deleteAll()
    override fun updateQtePerStation(newQteStation: String, sartQteDeclare:String, codeArticle: String, codeStation: String)
    = stationStockArticleDAO.updateQtePerStation(newQteStation, sartQteDeclare, codeArticle, codeStation)

    override fun qtePerStation(codeStation: String, codeArticle: String): Flow<String>
    = stationStockArticleDAO.qtePerStation(codeStation, codeArticle)

    override fun getStationStockArticle(codeStation: String, codeArticle: String): Flow<StationStockArticle?>
    = stationStockArticleDAO.getStationStockArticle(codeStation, codeArticle)

    override fun getAllStationStockArticle(): Flow<List<StationStockArticle>?> = stationStockArticleDAO.getAllStationStockArticle()

    override fun getStationListByProduct(codeArticle: String): Flow<List<StationStockArticleWithStation>?> =
        stationStockArticleDAO.getStationListByProduct(codeArticle)
}