package com.asmtunis.procaisseinventory.data.unite.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.unite.local.dao.UniteDAO
import com.asmtunis.procaisseinventory.data.unite.local.repository.UniteLocalRepository
import com.asmtunis.procaisseinventory.data.unite.local.repository.UniteLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class UniteLocalModule {

    @Provides
    @Singleton
    fun provideUniteDao(
        dataBase: ProCaisseDataBase
    ) = dataBase.uniteDAO()

    @Provides
    @Singleton
    @Named("Unite")
    fun provideUniteRepository(
        uniteDAO: UniteDAO
    ): UniteLocalRepository = UniteLocalRepositoryImpl(
        uniteDAO = uniteDAO
    )

}