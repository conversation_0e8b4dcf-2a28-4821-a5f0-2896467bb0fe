package com.asmtunis.procaisseinventory.data.banques.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.banques.local.dao.BanqueDAO
import com.asmtunis.procaisseinventory.data.banques.local.repository.BanqueLocalRepository
import com.asmtunis.procaisseinventory.data.banques.local.repository.BanqueLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class BanqueLocalModule {

    @Provides
    @Singleton
    fun provideBanqueDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.banqueDAO()

    @Provides
    @Singleton
    @Named("Banque")
    fun provideBanqueRepository(
        banqueDAO: BanqueDAO
    ): BanqueLocalRepository = BanqueLocalRepositoryImpl(banqueDAO = banqueDAO)
}