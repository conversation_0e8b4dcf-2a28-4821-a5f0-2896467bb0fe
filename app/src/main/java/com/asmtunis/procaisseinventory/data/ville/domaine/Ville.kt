package com.asmtunis.procaisseinventory.data.ville.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.TIMBRE_VILLE)
@Serializable
data class Ville (
    @PrimaryKey
    @ColumnInfo(name = "Carte_Fidelite")
    @SerialName("Carte_Fidelite")
   
    var carteFidelite: String = "",

    @SerialName("Is_Tacktil")
   
    var isTacktil: String? = null,

    @SerialName("D_Code_Article")
   
    var dCodeArticle: String? = null,

    @SerialName("F_Code_Article")
   
    var fCodeArticle: String? = null,

    @SerialName("D_Poid_Article")
   
    var dPoidArticle: String? = null,

    @SerialName("F_Poid_Article")
   
    var fPoidArticle: String? = null,

    @SerialName("D_Prix_Article")
   
    var dPrixArticle: String? = null,

    @SerialName("F_Prix_Article")
   
    var fPrixArticle: String? = null,

    @SerialName("MP_Modification")
   
    var mPModification: String? = null,

    @SerialName("Deux_Ecran")
   
    var deuxEcran: String? = null,

    @SerialName("is_Commande")
   
    var isCommande: String? = null,

    @SerialName("is_Direct")
   
    var isDirect: String? = null,

    @SerialName("is_Article_Session")
   
    var isArticleSession: String? = null,

    @SerialName("is_MP_Caisse")
   
    var isMPCaisse: String? = null,

    @SerialName("is_calc_monnaie")
   
    var isCalcMonnaie: String? = null,

    @SerialName("is_2vente")
   
    var is2vente: String? = null,

    @SerialName("is_promotion_nb_art")
   
    var isPromotionNbArt: String? = null,

    @SerialName("is_prix_gros")
   
    var isPrixGros: String? = null,

    @SerialName("Mode_Ticket")
   
    var modeTicket: String? = null,

    @SerialName("Mode_Ticket_Solde")
   
    var modeTicketSolde: String? = null,

    @SerialName("balance")
   
    var balance: String? = null,

    @SerialName("Transfert")
   
    var transfert: String? = null,

    @SerialName("Caisse_sans_Ticket")
   
    var caisseSansTicket: String? = null,

    @SerialName("Mp_Supprimer_Article")
   
    var mpSupprimerArticle: String? = null,

    @SerialName("MP_Liste_Ticket")
   
    var mPListeTicket: String? = null,

    @SerialName("MP_En_Instance")
   
    var mPEnInstance: String? = null,

    @SerialName("Stock_Article_Caisse")
   
    var stockArticleCaisse: String? = null,

    @SerialName("Article_Inactif")
   
    var articleInactif: String? = null,

    @SerialName("Facturation")
   
    var facturation: String? = null,

    @SerialName("MP_Modif_Qte")
   
    var mPModifQte: String? = null,

    @SerialName("Gestion_Inventaire")
   
    var gestionInventaire: String? = null,

    @SerialName("Bon_retour")
   
    var bonRetour: String? = null,

    @SerialName("Carte_Fidelite_Point")
   
    var carteFidelitePoint: String? = null,

    @SerialName("Aff_RFid_Ticket")
   
    var affRFidTicket: String? = null,

    @SerialName("BotArticleTaktil")
   
    var botArticleTaktil: String? = null,

    @SerialName("BotfamilleTaktil")
   
    var botfamilleTaktil: String? = null,

    @SerialName("Librerie")
   
    var librerie: String? = null,

    @SerialName("GridStock")
   
    var gridStock: String? = null,

    @SerialName("GridRupture")
   
    var gridRupture: String? = null,

    @SerialName("GridCA_Aricle")
   
    var gridCAAricle: String? = null,

    @SerialName("GridCA_Client")
   
    var gridCAClient: String? = null,

    @SerialName("GridCA_MargeFamille")
   
    var gridCAMargeFamille: String? = null,

    @SerialName("GridCA_MargeFournisseur")
   
    var gridCAMargeFournisseur: String? = null,

    @SerialName("GridCA_MargeFamille2")
   
    var gridCAMargeFamille2: String? = null,

    @SerialName("GridCA_MargeMarque")
   
    var gridCAMargeMarque: String? = null,

    @SerialName("Grid_AvoirArticle")
   
    var gridAvoirArticle: String? = null,

    @SerialName("Grid_BonComArticle")
   
    var gridBonComArticle: String? = null,

    @SerialName("Ordre_Fam_Alphabetique")
   
    var ordreFamAlphabetique: String? = null,

    @SerialName("Version_Light")
   
    var versionLight: String? = null,

    @SerialName("MP_Acompte_Annuler")
   
    var mPAcompteAnnuler: String? = null,

    @SerialName("MP_Acompte_Depasse_Ticket_Annuler")
   
    var mPAcompteDepasseTicketAnnuler: String? = null,

    @SerialName("Bot_Taktil_Fam_Frs")
   
    var botTaktilFamFrs: String? = null,

    @SerialName("Vente_Credit")
   
    var venteCredit: String? = null,

    @SerialName("StockDormant")
   
    var stockDormant: String? = null,

    @SerialName("GridStockDormant")
   
    var gridStockDormant: String? = null,

    @SerialName("MP_ClientCaisse")
   
    var mPClientCaisse: String? = null,

    @SerialName("Import_Export")
   
    var importExport: String? = null,

    @SerialName("Famille_Mere")
   
    var familleMere: String? = null,

    @SerialName("Aff_PEnc_Caisse")
   
    var affPEncCaisse: String? = null,

    @SerialName("Reg_Frs")
   
    var regFrs: String? = null,

    @SerialName("Calcul_Inverse_Qte_EEnc")
   
    var calculInverseQteEEnc: String? = null,

    @SerialName("Peremption")
   
    var peremption: String? = null,

    @SerialName("Etat_Reg_Fact_Nadhir")
   
    var etatRegFactNadhir: String? = null,

    @SerialName("Carte_prepayee")
   
    var cartePrepayee: String? = null,

    @SerialName("Reg_PointMerci")
   
    var regPointMerci: String? = null,

    @SerialName("Taux_Reduction_Carte_Resto")
   
    var tauxReductionCarteResto: String? = null,

    @SerialName("Tiroir_RJ11")
   
    var tiroirRJ11: String? = null,

    @SerialName("Reg_BonAchat")
   
    var regBonAchat: String? = null,

    @SerialName("Garantie_Ticket")
   
    var garantieTicket: String? = null,

    @SerialName("Transfert_DUX")
   
    var transfertDUX: String? = null,

    @SerialName("Facturation_BL")
   
    var facturationBL: String? = null,

    @SerialName("Facturation_BE")
   
    var facturationBE: String? = null,

    @SerialName("MP_Remise_Ticket")
   
    var mPRemiseTicket: String? = null,

    @SerialName("MP_Duplicata_Ticket")
   
    var mPDuplicataTicket: String? = null,

    @SerialName("Impression_ModeReg_Ticket")
   
    var impressionModeRegTicket: String? = null,

    @SerialName("Mp_Supprimer_Depence")
   
    var mpSupprimerDepence: String? = null,

    @SerialName("Mp_Ouvrir_Tiroire")
   
    var mpOuvrirTiroire: String? = null,

    @SerialName("MP_TRemise_LigArticle")
   
    var mPTRemiseLigArticle: String? = null,

    @SerialName("Splash")
   
    var splash: String? = null,

    @SerialName("Alert_Stock")
   
    var alertStock: String? = null,

    @SerialName("FactureA5_Senegal")
   
    var factureA5Senegal: String? = null,

    @SerialName("L_Reg_Frs")
   
    var lRegFrs: String? = null,

    @SerialName("L_Vente_Credit")
   
    var lVenteCredit: String? = null,

    @SerialName("L_balance")
   
    var lBalance: String? = null,

    @SerialName("L_Facturation")
   
    var lFacturation: String? = null,

    @SerialName("L_Carte_prepayee")
   
    var lCartePrepayee: String? = null,

    @SerialName("RG_Ticket_TTC")
   
    var rGTicketTTC: String? = null,

    @SerialName("Gestion_dechets")
   
    var gestionDechets: String? = null,

    @SerialName("Mp_Gestion_dechets")
   
    var mpGestionDechets: String? = null,

    @SerialName("Ticket_Cadeau")
   
    var ticketCadeau: String? = null,

    @SerialName("MP_Ticket_Cadeau")
   
    var mPTicketCadeau: String? = null,

    @SerialName("poste_preparation")
   
    var postePreparation: String? = null,

    @SerialName("MP_Liste_Commande")
   
    var mPListeCommande: String? = null,

    @SerialName("Max_TauxRemise")
   
    var maxTauxRemise: String? = null,

    @SerialName("MP_Clot_Caisse")
   
    var mPClotCaisse: String? = null,

    @SerialName("Commentaire_Ticket")
   
    var commentaireTicket: String? = null,

    @SerialName("Vente_WebService")
   
    var venteWebService: String? = null,

    @SerialName("Point_Fidelite")
   
    var pointFidelite: String? = null,

    @SerialName("Numero_Tallon")
   
    var numeroTallon: String? = null,

    @SerialName("SousFamille")
   
    var sousFamille: String? = null,

    @SerialName("WS_commande")
   
    var wSCommande: String? = null,

    @SerialName("WS_commande_service")
   
    var wSCommandeService: String? = null,

    @SerialName("CommandeA4")
   
    var commandeA4: String? = null,

    @SerialName("NbrImpCom")
   
    var nbrImpCom: String? = null,

    @SerialName("RegEsp_DemImp")
   
    var regEspDemImp: String? = null,

    @SerialName("Fusionligneticket")
   
    var fusionligneticket: String? = null,

    @SerialName("Numero_serie")
   
    var numeroSerie: String? = null,

    @SerialName("Supplement")
   
    var supplement: String? = null,

    @SerialName("Gestion_Tier")
   
    var gestionTier: String? = null,

    @SerialName("Gestion_bancaire")
   
    var gestionBancaire: String? = null,

    @SerialName("TB_Article")
   
    var tBArticle: String? = null,

    @SerialName("TB_Frs")
   
    var tBFrs: String? = null,

    @SerialName("TB_Client")
   
    var tBClient: String? = null,

    @SerialName("Mailing")
   
    var mailing: String? = null,

    @SerialName("Carnet_Auto")
   
    var carnetAuto: String? = null,

    @SerialName("Distributeur")
   
    var distributeur: String? = null,

    @SerialName("Esthetique")
   
    var esthetique: String? = null,

    @SerialName("Wait_Esthetique")
   
    var waitEsthetique: String? = null,

    @SerialName("BalanceCom")
   
    var balanceCom: String? = null,

    @SerialName("EmploiPrestataire")
   
    var emploiPrestataire: String? = null,

    @SerialName("CalandAuto")
   
    var calandAuto: String? = null,

    @SerialName("Merchandiser")
   
    var merchandiser: String? = null,

    @SerialName("SMS")
   
    var sms: String? = null,

    @SerialName("RegEsp_AvecImp")
   
    var regEspAvecImp: String? = null,

    @SerialName("RegEsp_SansImp")
   
    var regEspSansImp: String? = null,

    @SerialName("Forcement_MAJ")
   
    var forcementMAJ: String? = null,

    @SerialName("Clot_CaisseAuto")
   
    var clotCaisseAuto: String? = null,

    @SerialName("PPrepParProduit")
   
    var pPrepParProduit: String? = null,

    @SerialName("MP_Clotu_Caisse")
   
    var mPClotuCaisse: String? = null,

    @SerialName("CBParTalon")
   
    var cBParTalon: String? = null,

    @SerialName("Commercial_Oblig")
   
    var commercialOblig: String? = null,

    @SerialName("Commercial_ObligMP")
   
    var commercialObligMP: String? = null,

    @SerialName("Detruite_Serv_Pr")
   
    var detruiteServPr: String? = null,

    @SerialName("Article_Poid_Qte")
   
    var articlePoidQte: String? = null,

    @SerialName("ValiditeBAE")
   
    var validiteBAE: String? = null,

    @SerialName("BARecu_TchC")
   
    var bARecuTchC: String? = null,

    @SerialName("Talon_ServiceArticle")
   
    var talonServiceArticle: String? = null,

    @SerialName("Arrond_MntTick")
   
    var arrondMntTick: String? = null,

    @SerialName("Remise_DetaTick")
   
    var remiseDetaTick: String? = null,

    @SerialName("Blocage_OvrSession")
   
    var blocageOvrSession: String? = null,

    @SerialName("Ingredients")
   
    var ingredients: String? = null,

    @SerialName("Tarif_Client")
   
    var tarifClient: String? = null,

    @SerialName("NbrImpTicket")
   
    var nbrImpTicket: String? = null,

    @SerialName("NoteTicket")
   
    var noteTicket: String? = null,

    @SerialName("FreeShop")
   
    var freeShop: String? = null,

    @SerialName("Notif_AnnivClt")
   
    var notifAnnivClt: String? = null,

    @SerialName("Affichage_SF_Article")
   
    var affichageSFArticle: String? = null,

    @SerialName("CodeArt_MSFF")
   
    var codeArtMSFF: String? = null,

    @SerialName("Ticket_Obligatoire")
   
    var ticketObligatoire: String? = null,

    @SerialName("Supp_Bt_DevalINV")
   
    var suppBtDevalINV: String? = null,

    @SerialName("MNT_MinCheque")
   
    var mNTMinCheque: String? = null,

    @SerialName("MNT_MinCB")
   
    var mNTMinCB: String? = null,

    @SerialName("Article_Site")
   
    var articleSite: String? = null,

    @SerialName("BlocStkNeg")
   
    var blocStkNeg: String? = null,

    @SerialName("PrixG_Taux")
   
    var prixGTaux: String? = null,

    @SerialName("Sans_PUTicket")
   
    var sansPUTicket: String? = null,

    @SerialName("Crd_ET_Cmd")
   
    var crdETCmd: String? = null,

    @SerialName("PieceEgPrix_PEM")
   
    var pieceEgPrixPEM: String? = null,

    @SerialName("Armurerie")
   
    var armurerie: String? = null,

    @SerialName("Supp_Bt_DechC")
   
    var suppBtDechC: String? = null,

    @SerialName("Supp_Bt_DepC")
   
    var suppBtDepC: String? = null,

    @SerialName("MP_Rec_Caisse")
   
    var mPRecCaisse: String? = null,

    @SerialName("Prest_Imprimante")
   
    var prestImprimante: String? = null,

    @SerialName("Dressing")
   
    var dressing: String? = null,

    @SerialName("Boucherie")
   
    var boucherie: String? = null,

    @SerialName("MP_Ret_Caisse")
   
    var mPRetCaisse: String? = null,

    @SerialName("BCI_Nonvalor")
   
    var bCINonvalor: String? = null,

    @SerialName("MP_Clt_Caisse")
   
    var mPCltCaisse: String? = null,

    @SerialName("WS_Produit")
   
    var wSProduit: String? = null,

    @SerialName("Devis")
   
    var devis: String? = null,

    @SerialName("Facture_Frs")
   
    var factureFrs: String? = null,

    @SerialName("Article_Ecommerce")
   
    var articleEcommerce: String? = null,

    @SerialName("BSI")
   
    var bsi: String? = null,

    @SerialName("MAJ_PVGrid")
   
    var mAJPVGrid: String? = null,

    @SerialName("Porcelaine")
   
    var porcelaine: String? = null,

    @SerialName("Tracabilite")
   
    var tracabilite: String? = null,

    @SerialName("Libra_Poidauto")
   
    var libraPoidauto: String? = null,

    @SerialName("Chemin_demande")
   
    var cheminDemande: String? = null,

    @SerialName("Chemin_reponse")
   
    var cheminReponse: String? = null,

    @SerialName("Chemin_ecoute")
   
    var cheminEcoute: String? = null,

    @SerialName("FArticle")
   
    var fArticle: String? = null,

    @SerialName("PromPerdProd")
   
    var promPerdProd: String? = null,

    @SerialName("Libra_Type")
   
    var libraType: String? = null,

    @SerialName("DF_DLC_Art")
   
    var dFDLCArt: String? = null,

    @SerialName("Compta")
   
    var compta: String? = null,

    @SerialName("BCWeb")
   
    var bCWeb: String? = null,

    @SerialName("Chargement_BCW")
   
    var chargementBCW: String? = null,

    @SerialName("PFact_Stat")
   
    var pFactStat: String? = null,

    @SerialName("PBL_SUser")
   
    var pBLSUser: String? = null,

    @SerialName("Edition")
   
    var edition: String? = null,

    @SerialName("Sans_LogTicket")
   
    var sansLogTicket: String? = null,

    @SerialName("Sans_VendTicket")
   
    var sansVendTicket: String? = null,

    @SerialName("Imp_TVATicket")
   
    var impTVATicket: String? = null,

    @SerialName("Drive")
   
    var drive: String? = null,

    @SerialName("Dibal_Ticketauto")
   
    var dibalTicketauto: String? = null,

    @SerialName("DibalChemin_rep")
   
    var dibalCheminRep: String? = null,

    @SerialName("DibalChemin_rgi")
   
    var dibalCheminRgi: String? = null,

    @SerialName("MP_Credit")
   
    var mPCredit: String? = null,

    @SerialName("Commercial_sans")
   
    var commercialSans: String? = null,

    @SerialName("isPiece_unite")
   
    var isPieceUnite: String? = null,

    @SerialName("CreationAutoArt")
   
    var creationAutoArt: String? = null,

    @SerialName("OuvertureAutoArt")
   
    var ouvertureAutoArt: String? = null,

    @SerialName("WS_AutoArt")
   
    var wSAutoArt: String? = null,

    @SerialName("MP_Supp_BL")
   
    var mPSuppBL: String? = null,

    @SerialName("BL_Lot_PG")
   
    var bLLotPG: String? = null,

    @SerialName("Flouci")
   
    var flouci: String? = null,

    @SerialName("Mbre_Famille")
   
    var mbreFamille: String? = null,

    @SerialName("Pointg_Tick")
   
    var pointgTick: String? = null,

    @SerialName("MNT_MinRC")
   
    var mNTMinRC: String? = null,

    @SerialName("Aff_CAPrest")
   
    var affCAPrest: String? = null,

    @SerialName("Aff_Cred_FC")
   
    var affCredFC: String? = null,

    @SerialName("MP_DateLiv")
   
    var mPDateLiv: String? = null,

    @SerialName("Imp_EtatOrdre")
   
    var impEtatOrdre: String? = null,

    @SerialName("PgClt_Auto")
   
    var pgCltAuto: String? = null,

    @SerialName("RsClt_Auto")
   
    var rsCltAuto: String? = null,

    @SerialName("MPC_PGRS")
   
    var mpcPgrs: String? = null,

    @SerialName("BlocSkNg_BL")
   
    var blocSkNgBL: String? = null,

    @SerialName("BlocSkNg_BT")
   
    var blocSkNgBT: String? = null,

    @SerialName("MGUpdateBL")
   
    var mGUpdateBL: String? = null,

    @SerialName("MGUpdateBT")
   
    var mGUpdateBT: String? = null,

    @SerialName("NbrImpTick")
   
    var nbrImpTick: String? = null,

    @SerialName("ProMode")
   
    var proMode: String? = null,

    @SerialName("FactDirect")
   
    var factDirect: String? = null,

    @SerialName("PackArticle")
   
    var packArticle: String? = null,

    @SerialName("ComposArt")
   
    var composArt: String? = null,

    @SerialName("CreatRapArt")
   
    var creatRapArt: String? = null,

    @SerialName("D17")
   
    var d17: String? = null,

    @SerialName("DibalSup200")
   
    var dibalSup200: String? = null,

    @SerialName("ChDibalSup200")
   
    var chDibalSup200: String? = null,

    @SerialName("Bijouterie")
   
    var bijouterie: String? = null,

    @SerialName("SyncVPN")
   
    var syncVPN: String? = null,

    @SerialName("FideVPN")
   
    var fideVPN: String? = null,

    @SerialName("EcomApiBC")
   
    var ecomApiBC: String? = null,

    @SerialName("FactBaseTVA")
    var factBaseTVA: String? = null,


    @SerialName("FactClotSessPDA")
    var factClotSessPDA: String? = null,


    @SerialName("ImpTalentCom")
   
    var impTalentCom: String? = null,

    @SerialName("MPUpdateBE")
   
    var mPUpdateBE: String? = null,

    @SerialName("MPUpdateBR")
   
    var mPUpdateBR: String? = null,

    @ColumnInfo(name = "MaxEspPassagM")
    @SerialName("MaxEspPassagM")
   
    var maxEspPassagM : Double = 0.0
)
