package com.asmtunis.procaisseinventory.data.famille.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.famille.local.dao.FamilleDAO
import com.asmtunis.procaisseinventory.data.famille.local.repository.FamilleLocalRepository
import com.asmtunis.procaisseinventory.data.famille.local.repository.FamilleLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class FamilleLocalModule {

    @Provides
    @Singleton
    fun provideFamilleDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.familleDAO()

    @Provides
    @Singleton
    @Named("Famille")
    fun provideFamilleRepository(
        familleDAO: FamilleDAO
    ): FamilleLocalRepository = FamilleLocalRepositoryImpl(
        familleDAO = familleDAO
    )

}