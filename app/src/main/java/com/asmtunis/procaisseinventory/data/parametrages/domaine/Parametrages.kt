package com.asmtunis.procaisseinventory.data.parametrages.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
@Entity(tableName = ProCaisseConstants.PARAMETRAGE_TABLE)
data class Parametrages(
    @PrimaryKey
    @ColumnInfo(name = "id")
    var id: Long = 1,


    @ColumnInfo(name = "PARAM_MSGACC")
    @SerialName("PARAM_MSGACC")

    var pARAMMSGACC: String = "",

    @ColumnInfo(name = "PARAM_MSGMERCI")
    @SerialName("PARAM_MSGMERCI")

    var pARAMMSGMERCI: String = "",

    @ColumnInfo(name = "PARAM_PortTiroire")
    @SerialName("PARAM_PortTiroire")

    var pARAMPortTiroire: String = "",

    @ColumnInfo(name = "PARAM_PortAff")
    @SerialName("PARAM_PortAff")

    var pARAMPortAff: String = "",

    @ColumnInfo(name = "PARAM_Logo")
    @SerialName("PARAM_Logo")

    var pARAMLogo: String = "",

    @ColumnInfo(name = "PARAM_Imp_CB")
    @SerialName("PARAM_Imp_CB")

    var pARAMImpCB: String = "",

    @ColumnInfo(name = "Nbre_Jour")
    @SerialName("Nbre_Jour")

    var nbreJour: Int = 0,

    @ColumnInfo(name = "PARAM_Logopath")
    @SerialName("PARAM_Logopath")

    var pARAMLogopath: String = "",

    @ColumnInfo(name = "NomApplication")
    @SerialName("NomApplication")

    var nomApplication: String = "",

    @ColumnInfo(name = "ProModeM")
    @SerialName("ProModeM")

    var proModeM: Int = 0
)


