package com.asmtunis.procaisseinventory.data.devise.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.devise.local.dao.DeviseDAO
import com.asmtunis.procaisseinventory.data.devise.local.repository.DeviseLocalRepository
import com.asmtunis.procaisseinventory.data.devise.local.repository.DeviseLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

 
@Module
@InstallIn(SingletonComponent::class)
class DeviseLocalModule {

    @Provides
    @Singleton
    fun provideDeviseDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.deviseDAO()

    @Provides
    @Singleton
    @Named("Devise")
    fun provideDeviseRepository(
        deviseDAO: DeviseDAO
    ): DeviseLocalRepository = DeviseLocalRepositoryImpl(deviseDAO = deviseDAO)
}