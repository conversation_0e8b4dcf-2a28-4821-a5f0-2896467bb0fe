package com.asmtunis.procaisseinventory.data.devise.remote.di

import com.asmtunis.procaisseinventory.data.devise.remote.api.DeviseApi
import com.asmtunis.procaisseinventory.data.devise.remote.api.DeviseApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object DeviseRemoteModule {

    @Provides
    @Singleton
    fun provideDeviseApi(client: HttpClient): DeviseApi = DeviseApiImpl(client)

}