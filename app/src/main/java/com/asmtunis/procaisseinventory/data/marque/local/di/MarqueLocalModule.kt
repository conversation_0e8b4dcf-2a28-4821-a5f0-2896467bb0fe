package com.asmtunis.procaisseinventory.data.marque.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.marque.local.dao.MarqueDAO
import com.asmtunis.procaisseinventory.data.marque.local.repository.MarqueLocalRepository
import com.asmtunis.procaisseinventory.data.marque.local.repository.MarqueLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class MarqueLocalModule {

    @Provides
    @Singleton
    fun provideMarqueDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.marqueDAO()

    @Provides
    @Singleton
    @Named("Marque")
    fun provideMarqueRepository(
        marqueDAO: MarqueDAO
    ): MarqueLocalRepository = MarqueLocalRepositoryImpl(
        marqueDAO = marqueDAO
    )

}