package com.asmtunis.procaisseinventory.data.statistiques.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.statistiques.domaine.Statistics
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class StatisticsApiImpl(private val client: HttpClient) : StatisticsApi {
    override suspend fun getStatistics(baseConfig: String): Flow<DataResult<Statistics>> = flow {
        val result = executePostApiCall<Statistics>(
            client = client,
            endpoint = Urls.GET_STATISTICS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
}