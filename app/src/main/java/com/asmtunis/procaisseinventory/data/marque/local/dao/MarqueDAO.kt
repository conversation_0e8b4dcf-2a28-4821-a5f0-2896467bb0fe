package com.asmtunis.procaisseinventory.data.marque.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.MARQUE_TABLE
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import kotlinx.coroutines.flow.Flow


@Dao
interface MarqueDAO {
    @get:Query("SELECT * FROM $MARQUE_TABLE")
    val all: Flow<List<Marque>?>

    @Query("SELECT * FROM $MARQUE_TABLE WHERE isSync=0 and  (Status=:status) ")
    fun getByStatus(status: String): Flow<List<Marque>>

    @Query("SELECT * FROM $MARQUE_TABLE WHERE (Status=:status) ")
    fun getByStatusForced(status: String): Flow<List<Marque>>

    @Query("SELECT ifnull(MAX(cast(substr(MAR_Code,length(:prefix) + 1 ,length('MAR_Code'))as integer)),0)+1 FROM $MARQUE_TABLE WHERE substr(MAR_Code, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>

    @Query("SELECT COUNT(*) FROM $MARQUE_TABLE where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='DELETED')")
    fun count(): Flow<Int>

    @get:Query("SELECT * FROM $MARQUE_TABLE LIMIT 1")
    val one: Flow<Marque>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Marque)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Marque>)

    @Query("UPDATE $MARQUE_TABLE SET isSync=1 , Status='SELECTED'")
    fun updateStatus()

    @Query("UPDATE $MARQUE_TABLE SET isSync = 1, Status= 'SELECTED' where MAR_Code = :marCode")
    fun updateSyncMarque(marCode : String)

    @Query("DELETE FROM $MARQUE_TABLE")
    fun deleteAll()

    @get:Query("SELECT * FROM $MARQUE_TABLE WHERE isSync=0  and  (Status='INSERTED'  or Status='UPDATED')")
    val nonSync: Flow<List<Marque>>

    @get:Query("SELECT MAR_Designation FROM $MARQUE_TABLE WHERE MAR_Designation IS  NOT NULL")
    val list: Flow<List<String>>
}