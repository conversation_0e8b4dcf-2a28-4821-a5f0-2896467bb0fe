package com.asmtunis.procaisseinventory.data.facture.local.repository

import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import kotlinx.coroutines.flow.Flow


interface FactureLocalRepository {

    fun upsertAll(value: List<Facture>)
    fun upsert(value: Facture)


    fun deleteAll()

    fun getAll(): Flow<List<Facture?>?>
    fun getByFactNumBc(code: Int): Flow<Facture?>

    fun getByTicket(code: String, user: String, station: String): Flow<Facture?>

}