package com.asmtunis.procaisseinventory.data.etablisement.remote.di

import com.asmtunis.procaisseinventory.data.etablisement.remote.api.EtablissementApi
import com.asmtunis.procaisseinventory.data.etablisement.remote.api.EtablissementApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object EtablissementRemoteModule {

    @Provides
    @Singleton
    fun provideEtablissementApi(client: HttpClient): EtablissementApi = EtablissementApiImpl(client)

}