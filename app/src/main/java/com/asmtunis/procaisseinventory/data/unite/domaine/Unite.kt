package com.asmtunis.procaisseinventory.data.unite.domaine


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.UNITE_TABLE
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = UNITE_TABLE)
@Serializable
data class Unite(
    @PrimaryKey
    @SerialName("UNI_Code")
    @ColumnInfo(name = "UNI_Code")
    val uNICode: String = "",

    @SerialName("DDmM")
    @ColumnInfo(name = "DDmM")
    val dDmM: String? = "",

    @SerialName("exportM")
    @ColumnInfo(name = "exportM")
    val exportM: String? = "",

    @SerialName("UNI_DDm")
    @ColumnInfo(name = "UNI_DDm")
    val uNIDDm: String? = "",

    @SerialName("UNI_Designation")
    @ColumnInfo(name = "UNI_Designation")
    val uNIDesignation: String? = "",

    @SerialName("UNI_export")
    @ColumnInfo(name = "UNI_export")
    val uNIExport: String? = "",

    @SerialName("UNI_Station")
    @ColumnInfo(name = "UNI_Station")
    val uNIStation: String? = "",

    @SerialName("UNI_User")
    @ColumnInfo(name = "UNI_User")
    val uNIUser: String? = ""
): BaseModel()