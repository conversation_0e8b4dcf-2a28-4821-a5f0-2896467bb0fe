package com.asmtunis.procaisseinventory.data.ville.local.repository

import com.asmtunis.procaisseinventory.data.ville.domaine.Ville
import com.asmtunis.procaisseinventory.data.ville.local.dao.VilleDAO
import kotlinx.coroutines.flow.Flow


class VilleLocalRepositoryImpl(private val villeDAO: VilleDAO) : VilleLocalRepository {
    override fun upsertAll(value: List<Ville>)  = villeDAO.insertAll(value)

    override fun deleteAll() = villeDAO.deleteAll()

    override fun getAll(): Flow<List<Ville>?> = villeDAO.villes


}