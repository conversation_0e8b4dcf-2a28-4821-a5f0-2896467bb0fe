package com.asmtunis.procaisseinventory.data.marque.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.marque.domaine.AddMarqueResponse
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import kotlinx.coroutines.flow.Flow


interface MarqueApi {



    suspend fun getMarques(baseConfig: String): Flow<DataResult<List<Marque>>>
    suspend fun addMarqueMobile(baseConfig: String): Flow<DataResult<List<AddMarqueResponse>>>



}