package com.asmtunis.procaisseinventory.data.station.domaine

import androidx.room.Embedded
import androidx.room.Relation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class StationStockArticleWithStation (
    @Embedded
    @SerialName("StationStockArticle")
    var stationStockArticle: StationStockArticle? = null,

    @Relation(
        parentColumn = "SART_CodeSatation",
        entityColumn = "STAT_Code"
    )
    @SerialName("Station")
    var station: Station? = null
)