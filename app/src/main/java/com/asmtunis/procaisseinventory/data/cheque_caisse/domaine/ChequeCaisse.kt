package com.asmtunis.procaisseinventory.data.cheque_caisse.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


//@Entity(tableName = ProCaisseConstants.CHEQUE_CAISSE_TABLE,primaryKeys = ["NumCheque", "Reglement"])
@Entity(tableName = ProCaisseConstants.CHEQUE_CAISSE_TABLE)
@Serializable
data class ChequeCaisse (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @ColumnInfo(name = "NumCheque")
    @SerialName("NumCheque")
    
    var numCheque: String = "",

    @ColumnInfo(name = "Reglement")
    @SerialName("Reglement")
    
    var reglement: String = "",

    @ColumnInfo(name = "Reglement_M")
    @SerialName("Reglement_M")
    
    var reglementM: String? = "",


    @ColumnInfo(name = "CC_user")
    @SerialName("CC_user")
    
    var codeUtilisateur: String? = "",

    @ColumnInfo(name = "reglement_idsession")
    @SerialName("reglement_idsession")
    
    var reglementidsession: String? = "",

    @ColumnInfo(name = "reglement_exercice")
    @SerialName("reglement_exercice")
    
    var reglementExercice: String? = "",

    @ColumnInfo(name = "EcheanceCheque")
    @SerialName("EcheanceCheque")
    var echeanceCheque: String? = "",

    @ColumnInfo(name = "Banque")
    @SerialName("Banque")
    var banque: String? = "",

    @ColumnInfo(name = "Montant")
    @SerialName("Montant")
    
    //var montant : Double = 0.0 TODO IF THERE IS KTOR PB RESTORE THIS LINE
    var montant : String = ""
    ) : BaseModel()
