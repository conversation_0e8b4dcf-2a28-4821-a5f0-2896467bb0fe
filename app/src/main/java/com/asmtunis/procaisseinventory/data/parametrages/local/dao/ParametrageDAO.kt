package com.asmtunis.procaisseinventory.data.parametrages.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import kotlinx.coroutines.flow.Flow


@Dao
interface ParametrageDAO {
    @Query("SELECT COUNT(*) FROM ${ProCaisseConstants.PARAMETRAGE_TABLE}")
    fun count(): Int

    @get:Query("SELECT * FROM ${ProCaisseConstants.PARAMETRAGE_TABLE} LIMIT 1")
    val one: Flow<Parametrages?>



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Parametrages)


    @Query("DELETE FROM ${ProCaisseConstants.PARAMETRAGE_TABLE}")
    fun deleteAll()
}