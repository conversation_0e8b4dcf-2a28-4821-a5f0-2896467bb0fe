
package com.asmtunis.procaisseinventory.data.cheque_caisse.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class ChequeCaisseApiImpl(private val client: HttpClient) : ChequeCaisseApi {
    override suspend fun getChequeCaisseByReglements(baseConfig: String): Flow<DataResult<List<List<ChequeCaisse>>>> = flow {

        val result = executePostApiCall<List<List<ChequeCaisse>>>(
            client = client,
            endpoint = Urls.GET_CHEQUE_BY_REGLEMENTS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getChequeCaisseByReglement(baseConfig: String): Flow<DataResult<List<ChequeCaisse>>> = flow {

        val result = executePostApiCall<List<ChequeCaisse>>(
            client = client,
            endpoint = Urls.GET_CHEQUE_BY_REGLEMENT,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }