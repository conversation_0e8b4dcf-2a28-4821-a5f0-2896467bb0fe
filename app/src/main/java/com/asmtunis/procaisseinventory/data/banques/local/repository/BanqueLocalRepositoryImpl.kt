package com.asmtunis.procaisseinventory.data.banques.local.repository

import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import com.asmtunis.procaisseinventory.data.banques.local.dao.BanqueDAO
import kotlinx.coroutines.flow.Flow


class BanqueLocalRepositoryImpl(private val banqueDAO: BanqueDAO) : BanqueLocalRepository {
    override fun upsertAll(value: List<Banque>) = banqueDAO.insertAll(value)

    override fun deleteAll() = banqueDAO.deleteAll()

    override fun getAll(): Flow<List<Banque>> = banqueDAO.all
}