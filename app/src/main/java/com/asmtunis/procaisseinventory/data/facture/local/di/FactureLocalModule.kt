package com.asmtunis.procaisseinventory.data.facture.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.facture.local.dao.FactureDAO
import com.asmtunis.procaisseinventory.data.facture.local.repository.FactureLocalRepository
import com.asmtunis.procaisseinventory.data.facture.local.repository.FactureLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class FactureLocalModule {

    @Provides
    @Singleton
    fun provideFactureDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.factureDAO()

    @Provides
    @Singleton
    @Named("Facture")
    fun provideFactureRepository(
        factureDAO: FactureDAO
    ): FactureLocalRepository = FactureLocalRepositoryImpl(factureDAO = factureDAO)
}