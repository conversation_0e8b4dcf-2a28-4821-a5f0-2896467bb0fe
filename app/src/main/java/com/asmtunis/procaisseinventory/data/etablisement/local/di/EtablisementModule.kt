package com.asmtunis.procaisseinventory.data.etablisement.local.di

import com.asmtunis.procaisseinventory.data.etablisement.local.dao.EtablisementDAO
import com.asmtunis.procaisseinventory.data.etablisement.local.repository.EtablisementLocalRepositoryImpl
import com.asmtunis.procaisseinventory.data.etablisement.local.repository.EtablisementLocalRepository
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class EtablisementModule {

    @Provides
    @Singleton
    fun provideEtablissementDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.etablisementDAO()

    @Provides
    @Singleton
    @Named("Etablissement")
    fun provideEtablissementRepository(
        etablisementDAO: EtablisementDAO
    ): EtablisementLocalRepository = EtablisementLocalRepositoryImpl(
        etablisementDAO = etablisementDAO

    )


}
