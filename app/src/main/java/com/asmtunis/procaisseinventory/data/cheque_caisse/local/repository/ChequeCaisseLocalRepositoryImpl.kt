package com.asmtunis.procaisseinventory.data.cheque_caisse.local.repository

import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import com.asmtunis.procaisseinventory.data.cheque_caisse.local.dao.ChequeCaisseDAO
import kotlinx.coroutines.flow.Flow


class ChequeCaisseLocalRepositoryImpl(private val chequeCaisseDAO: ChequeCaisseDAO) : ChequeCaisseLocalRepository {
    override fun upsertAll(value: List<ChequeCaisse>) = chequeCaisseDAO.insertAll(value)
    override fun updateRegCodeAndState(regCode: String, regCodeM: String)  = chequeCaisseDAO.updateRegCodeAndState(regCode, regCodeM)

    override fun deleteAll() = chequeCaisseDAO.deleteAll()
    override fun deleteByCodeM(codeM: String, exercice: String) = chequeCaisseDAO.deleteByCodeM(codeM = codeM, exercice = exercice)

    override fun getAll(): Flow<List<ChequeCaisse>> = chequeCaisseDAO.all

    override fun getByReglementM(code: String): Flow<List<ChequeCaisse>> = chequeCaisseDAO.getByReglementM(code)

}