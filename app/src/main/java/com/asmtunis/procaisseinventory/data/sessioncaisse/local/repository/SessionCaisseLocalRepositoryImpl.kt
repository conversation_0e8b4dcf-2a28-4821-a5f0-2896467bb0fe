package com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository

import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.dao.SessionCaisseDAO
import kotlinx.coroutines.flow.Flow

class SessionCaisseLocalRepositoryImpl(
    private val sessionCaisseDAO: SessionCaisseDAO
) : SessionCaisseLocalRepository {
    override fun upsert(value: SessionCaisse) = sessionCaisseDAO.insert(value)

    override fun upsertAll(value: List<SessionCaisse>) = sessionCaisseDAO.insertAll(value)

    override fun deleteAll() = sessionCaisseDAO.deleteAll()

    override fun getAll(): Flow<List<SessionCaisse>> = sessionCaisseDAO.all

    override fun getNotSync(): Flow<List<SessionCaisse>> = sessionCaisseDAO.getNotSync

    override fun getByCode(code: String): Flow<SessionCaisse> = sessionCaisseDAO.getOneByCode(code)
}
