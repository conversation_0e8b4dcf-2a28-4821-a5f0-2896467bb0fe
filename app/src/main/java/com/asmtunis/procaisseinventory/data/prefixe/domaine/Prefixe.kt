package com.asmtunis.procaisseinventory.data.prefixe.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.PREFIX_TABLE,primaryKeys = ["PRE_Id_table"])

@Serializable
data class Prefixe  (
    @ColumnInfo(name = "PRE_Id_table")
    @SerialName("PRE_Id_table")
    var pREIdTable: String = "",

    @ColumnInfo(name = "PRE_Prefixe")
    @SerialName("PRE_Prefixe")
    var pREPrefixe: String? = "",

    @ColumnInfo(name = "PRE_Designation")
    @SerialName("PRE_Designation")
    var pREDesignation: String = ""
)