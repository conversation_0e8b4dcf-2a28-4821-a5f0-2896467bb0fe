package com.asmtunis.procaisseinventory.data.carte_resto.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import kotlinx.coroutines.flow.Flow


interface CarteRestoApi {



    suspend fun getCartesResto(baseConfig: String): Flow<DataResult<List<CarteResto>>>
    suspend fun getCarteRestoByCode(baseConfig: String): Flow<DataResult<CarteResto>>

}