package com.asmtunis.procaisseinventory.data.station.local.station.repository

import com.asmtunis.procaisseinventory.data.station.domaine.Station
import kotlinx.coroutines.flow.Flow




interface StationLocalRepository {
    fun upsertAll(value: List<Station>)
    fun upsert(value: Station)
    fun deleteAll()
    fun deleteByCode(code: String)

    fun getByCode(code: String) : Flow<Station?>
    fun getAll(): Flow<List<Station>>

}