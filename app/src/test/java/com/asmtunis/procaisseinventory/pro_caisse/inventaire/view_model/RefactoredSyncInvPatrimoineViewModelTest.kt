package com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.AssetOperationState
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.LigneBonCommandeWithImageList
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.repository.InventaireLocalRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runBlockingTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
class RefactoredSyncInvPatrimoineViewModelTest {
    
    private val testDispatcher = TestCoroutineDispatcher()
    
    @Mock
    private lateinit var proCaisseRemote: ProCaisseRemote
    
    @Mock
    private lateinit var proCaisseLocalDb: ProCaisseLocalDb
    
    @Mock
    private lateinit var listenNetwork: ListenNetwork
    
    @Mock
    private lateinit var inventaireLocalRepository: InventaireLocalRepository
    
    private lateinit var viewModel: RefactoredSyncInvPatrimoineViewModel
    
    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
        Dispatchers.setMain(testDispatcher)
        
        // Setup mock behavior
        val networkFlow = MutableStateFlow(true)
        `when`(listenNetwork.isConnected).thenReturn(networkFlow)
        
        val autoSyncFlow = MutableStateFlow(true)
        `when`(proCaisseLocalDb.dataStore.getBoolean(any(), any())).thenReturn(autoSyncFlow)
        
        `when`(proCaisseLocalDb.invePatrimoine).thenReturn(inventaireLocalRepository)
        
        // Create empty flows for all asset types
        val emptyFlow: Flow<Map<BonCommande, List<LigneBonCommandeWithImageList>>> = flowOf(emptyMap())
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.AFFECTATION.typePat, Constants.PATRIMOINE))
            .thenReturn(emptyFlow)
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.AFFECTATION.typePat, Constants.IMMOBILISATION))
            .thenReturn(emptyFlow)
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.ENTREE.typePat, Constants.PATRIMOINE))
            .thenReturn(emptyFlow)
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.ENTREE.typePat, Constants.IMMOBILISATION))
            .thenReturn(emptyFlow)
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.SORTIE.typePat, Constants.PATRIMOINE))
            .thenReturn(emptyFlow)
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.SORTIE.typePat, Constants.IMMOBILISATION))
            .thenReturn(emptyFlow)
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.INVENTAIRE.typePat, Constants.PATRIMOINE))
            .thenReturn(emptyFlow)
        `when`(inventaireLocalRepository.notSynced(TypePatrimoine.INVENTAIRE.typePat, Constants.IMMOBILISATION))
            .thenReturn(emptyFlow)
        
        // Initialize the ViewModel
        viewModel = RefactoredSyncInvPatrimoineViewModel(
            defaultDispatcher = testDispatcher,
            dispatcherIO = testDispatcher,
            mainImmediateDispatcher = testDispatcher,
            mainDispatcher = testDispatcher,
            proCaisseRemote = proCaisseRemote,
            proCaisseLocalDb = proCaisseLocalDb,
            listenNetwork = listenNetwork
        )
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        testDispatcher.cleanupTestCoroutines()
    }
    
    @Test
    fun `initial state should be idle for all operations`() = testDispatcher.runBlockingTest {
        val state = viewModel.viewModelState.value
        
        assertTrue(state.affectationState is AssetOperationState.Idle)
        assertTrue(state.affectationBatimentState is AssetOperationState.Idle)
        assertTrue(state.depInState is AssetOperationState.Idle)
        assertTrue(state.depInBatimentState is AssetOperationState.Idle)
        assertTrue(state.depOutState is AssetOperationState.Idle)
        assertTrue(state.depOutBatimentState is AssetOperationState.Idle)
        assertTrue(state.inventaireState is AssetOperationState.Idle)
        assertTrue(state.inventaireBatimentState is AssetOperationState.Idle)
    }
    
    @Test
    fun `syncAssetOperation should update state to loading`() = testDispatcher.runBlockingTest {
        // Setup mock response
        val successResponse = DataResult.Success<List<InvPatBatchResponse>>(emptyList())
        `when`(proCaisseRemote.inventairePatrimoine.addBatchInvPat(any())).thenReturn(flowOf(successResponse))
        
        // Call the method
        viewModel.syncAssetOperation(TypePatrimoine.AFFECTATION.typePat, Constants.PATRIMOINE)
        
        // Advance the test dispatcher to allow the coroutine to run
        testDispatcher.advanceUntilIdle()
        
        // Verify the state is updated to loading
        val state = viewModel.viewModelState.value.affectationState
        assertTrue(state is AssetOperationState.Loading)
        assertEquals("${TypePatrimoine.AFFECTATION.typePat}-${Constants.PATRIMOINE}", state.operationType)
    }
    
    // Helper function for Mockito's any() matcher
    private fun <T> any(): T {
        return org.mockito.Mockito.any<T>()
    }
}
