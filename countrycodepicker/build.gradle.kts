plugins {
    id ("com.android.library")
  //  id ("kotlin-android")

    alias(libs.plugins.kotlin)

    alias(libs.plugins.compose.compiler)

   // alias(libs.plugins.serializationX)
   // id ("org.jetbrains.kotlin.plugin.serialization")
}

android {
    compileSdk = libs.versions.targetSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()

        testInstrumentationRunner ="androidx.test.runner.AndroidJUnitRunner"
       // consumerProguardFiles "consumer-rules.pro"

    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles (getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = libs.versions.jvmTarget.get()
     //   freeCompilerArgs += "-Xopt-in=kotlin.RequiresOptIn"
    }

    /*  packagingOptions {
           resources {
               excludes += "/META-INF/{AL2.0,LGPL2.1}"
           }
       }*/
    namespace = "com.asmtunis.asm.countrycodepicker"


    testOptions {
        unitTests.isIncludeAndroidResources = true
        unitTests.all {
            it.useJUnitPlatform()
        }
    }
}
dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

    implementation (libs.core.ktx)
    implementation (libs.appcompat)
    implementation (libs.material)
  // testImplementation (libs.junit)
    implementation(libs.kotlin.test.junit)
    androidTestImplementation (libs.androidx.test.ext.junit)
    androidTestImplementation (libs.espresso.core)

    implementation (libs.libphonenumber)


    implementation(platform(libs.compose.bom))
    implementation(libs.bundles.compose)


    // kotlin serialization
   // implementation (libs.kotlinx.serialization)

}

