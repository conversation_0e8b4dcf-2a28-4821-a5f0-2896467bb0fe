package com.asmtunis.countrycodepicker.data.utils

import androidx.compose.ui.text.intl.Locale
import com.asmtunis.countrycodepicker.data.CountryData
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber

fun getDefaultLangCode(/*context: Context*/): String {
   // val localeCode: TelephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
    val countryCode = "tn"//localeCode.networkCountryIso
    val defaultLocale = Locale.current.language
    return countryCode.ifBlank { defaultLocale }
}

fun getDefaultPhoneCode(/*context: Context*/): String {
    val defaultCountry = getDefaultLangCode(/*context*/)
    val defaultCode: CountryData = getLibCountries().first() { it.countryCode == defaultCountry }
    return defaultCode.phoneCode.ifBlank { "+216" }
}

fun checkPhoneNumber(phone: String, fullPhoneNumber: String, countryCode: String): Boolean {
    val number: Phonenumber.PhoneNumber?
    if (phone.length > 6) {
        return try {
            number = PhoneNumberUtil.getInstance().parse(
                fullPhoneNumber,
                Phonenumber.PhoneNumber.CountryCodeSource.UNSPECIFIED.name
            )
            PhoneNumberUtil.getInstance().isValidNumberForRegion(number, countryCode.uppercase())
        } catch (ex: Exception) {
            false
        }
    }
    return false
}
