package com.asmtunis.countrycodepicker.data.utils

import com.asmtunis.asm.countrycodepicker.R


fun getCountryName(countryName: String): Int {
    return when (countryName) {
        "ad" -> R.string.andorra
        "ae" -> R.string.united_arab_emirates
        "af" -> R.string.afghanistan
        "ag" -> R.string.antigua_and_barbuda
        "ai" -> R.string.anguilla
        "al" -> R.string.albania
        "am" -> R.string.armenia
        "ao" -> R.string.angola
        "aq" -> R.string.antarctica
        "ar" -> R.string.argentina
        "as" -> R.string.american_samoa
        "at" -> R.string.austria
        "au" -> R.string.australia
        "aw" -> R.string.aruba
        "ax" -> R.string.aland_islands
        "az" -> R.string.azerbaijan
        "ba" -> R.string.bosnia
        "bb" -> R.string.barbados
        "bd" -> R.string.bangladesh
        "be" -> R.string.belgium
        "bf" -> R.string.burkina_faso
        "bg" -> R.string.bulgaria
        "bh" -> R.string.bahrain
        "bi" -> R.string.burundi
        "bj" -> R.string.benin
        "bl" -> R.string.saint_barhelemy
        "bm" -> R.string.bermuda
        "bn" -> R.string.brunei_darussalam
        "bo" -> R.string.bolivia
        "br" -> R.string.brazil
        "bs" -> R.string.bahamas
        "bt" -> R.string.bhutan
        "bw" -> R.string.botswana
        "by" -> R.string.belarus
        "bz" -> R.string.belize
        "ca" -> R.string.canada
        "cc" -> R.string.cocos
        "cd" -> R.string.congo_democratic
        "cf" -> R.string.central_african
        "cg" -> R.string.congo
        "ch" -> R.string.switzerland
        "ci" -> R.string.cote_dlvoire
        "ck" -> R.string.cook_islands
        "cl" -> R.string.chile
        "cm" -> R.string.cameroon
        "cn" -> R.string.china
        "co" -> R.string.colombia
        "cr" -> R.string.costa_rica
        "cu" -> R.string.cuba
        "cv" -> R.string.cape_verde
        "cw" -> R.string.curacao
        "cx" -> R.string.christmas_island
        "cy" -> R.string.cyprus
        "cz" -> R.string.czech_republic
        "de" -> R.string.germany
        "dj" -> R.string.djibouti
        "dk" -> R.string.denmark
        "dm" -> R.string.dominica
        "do" -> R.string.dominician_republic
        "dz" -> R.string.algeria
        "ec" -> R.string.ecuador
        "ee" -> R.string.estonia
        "eg" -> R.string.egypt
        "er" -> R.string.eritrea
        "es" -> R.string.spain
        "et" -> R.string.ethiopia
        "fi" -> R.string.finland
        "fj" -> R.string.fiji
        "fk" -> R.string.falkland_islands
        "fm" -> R.string.micro
        "fo" -> R.string.faroe_islands
        "fr" -> R.string.france
        "ga" -> R.string.gabon
        "gb" -> R.string.united_kingdom
        "gd" -> R.string.grenada
        "ge" -> R.string.georgia
        "gf" -> R.string.french_guyana
        "gg" -> R.string.guernsey
        "gh" -> R.string.ghana
        "gi" -> R.string.gibraltar
        "gl" -> R.string.greenland
        "gm" -> R.string.gambia
        "gn" -> R.string.guinea
        "gp" -> R.string.guadeloupe
        "gq" -> R.string.equatorial_guinea
        "gr" -> R.string.greece
        "gt" -> R.string.guatemala
        "gu" -> R.string.guam
        "gw" -> R.string.guinea_bissau
        "gy" -> R.string.guyana
        "hk" -> R.string.hong_kong
        "hn" -> R.string.honduras
        "hr" -> R.string.croatia
        "ht" -> R.string.haiti
        "hu" -> R.string.hungary
        "id" -> R.string.indonesia
        "ie" -> R.string.ireland
        "il" -> R.string.israil
        "im" -> R.string.isle_of_man
        "is" -> R.string.iceland
        "in" -> R.string.india
        "io" -> R.string.british_indian_ocean
        "iq" -> R.string.iraq
        "ir" -> R.string.iran
        "it" -> R.string.italia
        "je" -> R.string.jersey
        "jm" -> R.string.jamaica
        "jo" -> R.string.jordan
        "jp" -> R.string.japan
        "ke" -> R.string.kenya
        "kg" -> R.string.kyrgyzstan
        "kh" -> R.string.cambodia
        "ki" -> R.string.kiribati
        "km" -> R.string.comoros
        "kn" -> R.string.saint_kitts
        "kp" -> R.string.north_korea
        "kr" -> R.string.south_korea
        "kw" -> R.string.kuwait
        "ky" -> R.string.cayman_islands
        "kz" -> R.string.kazakhstan
        "la" -> R.string.laos
        "lb" -> R.string.lebanon
        "lc" -> R.string.saint_lucia
        "li" -> R.string.liechtenstein
        "lk" -> R.string.siri_lanka
        "lr" -> R.string.liberia
        "ls" -> R.string.lesotho
        "lt" -> R.string.lithuania
        "lu" -> R.string.luxembourg
        "lv" -> R.string.latvia
        "ly" -> R.string.libya
        "ma" -> R.string.marocco
        "mc" -> R.string.monaco
        "md" -> R.string.moldova
        "me" -> R.string.montenegro
        "mf" -> R.string.saint_martin
        "mg" -> R.string.madagascar
        "mh" -> R.string.marshall_islands
        "mk" -> R.string.north_macedonia
        "ml" -> R.string.mali
        "mm" -> R.string.myanmar
        "mn" -> R.string.mongolia
        "mo" -> R.string.macau
        "mp" -> R.string.northern_mariana
        "mq" -> R.string.martinique
        "mr" -> R.string.mauriatana
        "ms" -> R.string.montserrat
        "mt" -> R.string.malta
        "mu" -> R.string.mauritius
        "mv" -> R.string.maldives
        "mw" -> R.string.malawi
        "mx" -> R.string.mexico
        "my" -> R.string.malaysia
        "mz" -> R.string.mozambique
        "na" -> R.string.namibia
        "nc" -> R.string.new_caledonia
        "ne" -> R.string.niger
        "nf" -> R.string.norfolk
        "ng" -> R.string.nigeria
        "ni" -> R.string.nicaragua
        "nl" -> R.string.netherlands
        "no" -> R.string.norway
        "np" -> R.string.nepal
        "nr" -> R.string.nauru
        "nu" -> R.string.niue
        "nz" -> R.string.new_zealand
        "om" -> R.string.oman
        "pa" -> R.string.panama
        "pe" -> R.string.peru
        "pf" -> R.string.french_polynesia
        "pg" -> R.string.papua_new_guinea
        "ph" -> R.string.philippinies
        "pk" -> R.string.pakistan
        "pl" -> R.string.poland
        "pm" -> R.string.saint_pierre
        "pn" -> R.string.pitcairn
        "pr" -> R.string.puerto_rico
        "ps" -> R.string.state_of_palestine
        "pt" -> R.string.portugal
        "pw" -> R.string.palau
        "py" -> R.string.paraguay
        "qa" -> R.string.qatar
        "re" -> R.string.reunion
        "ro" -> R.string.romania
        "rs" -> R.string.serbia
        "ru" -> R.string.russia
        "rw" -> R.string.rwanda
        "sa" -> R.string.saudi_arabia
        "sb" -> R.string.solomon_islands
        "sc" -> R.string.seychelles
        "sd" -> R.string.sudan
        "se" -> R.string.sweden
        "sg" -> R.string.singapore
        "sh" -> R.string.saint_helena
        "si" -> R.string.slovenia
        "sk" -> R.string.slovakia
        "sl" -> R.string.sierra_leone
        "sm" -> R.string.san_marino
        "sn" -> R.string.senegal
        "so" -> R.string.somali
        "sr" -> R.string.suriname
        "ss" -> R.string.south_sudan
        "st" -> R.string.sao_tome
        "sv" -> R.string.el_salvador
        "sx" -> R.string.sint_maarten
        "sy" -> R.string.syrian
        "sz" -> R.string.swaziland
        "tc" -> R.string.turks_and_caicos
        "td" -> R.string.chad
        "tg" -> R.string.togo
        "th" -> R.string.thailand
        "tj" -> R.string.taijikistan
        "tk" -> R.string.tokelau
        "tl" -> R.string.timor_leste
        "tm" -> R.string.turkmenistan
        "tn" -> R.string.tunisia
        "to" -> R.string.tonga
        "tr" -> R.string.turkey
        "tt" -> R.string.trinidad_and_tobago
        "tv" -> R.string.tuvalu
        "tw" -> R.string.taiwan
        "tz" -> R.string.tazmania
        "ua" -> R.string.ukraina
        "ug" -> R.string.uganda
        "us" -> R.string.united_states_america
        "uy" -> R.string.uruguay
        "uz" -> R.string.uzbekistan
        "va" -> R.string.holy_see
        "vc" -> R.string.saint_vincent
        "ve" -> R.string.venezuela
        "vg" -> R.string.virgin_islands
        "vi" -> R.string.virgin_island_us
        "vn" -> R.string.vietnam
        "vu" -> R.string.vanuatu
        "wf" -> R.string.walli_and_fatuna
        "ws" -> R.string.samoa
        "xk" -> R.string.kosovo
        "ye" -> R.string.yemen
        "yt" -> R.string.mayotte
        "za" -> R.string.south_africa
        "zm" -> R.string.zambia
        "zw" -> R.string.zimbabwe
        else -> R.string.unkown
    }
}