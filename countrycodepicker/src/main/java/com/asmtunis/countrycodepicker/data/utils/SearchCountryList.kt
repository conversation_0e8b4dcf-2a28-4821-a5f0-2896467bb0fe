package com.asmtunis.countrycodepicker.data.utils

import com.asmtunis.countrycodepicker.data.CountryData

fun List<CountryData>.searchCountry(key: String, countryName: (String) -> String): MutableList<CountryData> {
    val tempList = mutableListOf<CountryData>()

    this.forEach {
        if (countryName(it.countryCode).lowercase().contains(key.lowercase())) {
            tempList.add(it)
        }
    }
    return tempList
}