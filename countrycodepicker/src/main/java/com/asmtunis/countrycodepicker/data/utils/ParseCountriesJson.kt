package com.asmtunis.countrycodepicker.data.utils

object ParseCountriesJson {

  /*  fun getJsonDataFromAsset(context: Context, fileName: String): String? {
        return try {
            val inputStream = context.assets.open(fileName)
            val size = inputStream.available()
            val buffer = ByteArray(size)
            inputStream.read(buffer)
            inputStream.close()
            String(buffer, Charsets.UTF_8)
        } catch (ex: Exception) {
            ex.printStackTrace()
            null
        }
    }

    fun parseCountriesJson(context: Context): List<CountryData>? {
        val jsonString = getJsonDataFromAsset(context, "countries.json")
        return jsonString?.let {
            Json.decodeFromString<List<CountryData>>(it)
        }
    }*/

}