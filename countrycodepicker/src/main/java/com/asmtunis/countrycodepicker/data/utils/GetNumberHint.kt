package com.asmtunis.countrycodepicker.data.utils

import com.asmtunis.asm.countrycodepicker.R
import java.util.Locale


fun getNumberHint(countryCode: String): Int {
    return when (countryCode.lowercase(Locale.ROOT)) {
        "ad" -> R.string.andorra_hint
        "ae" -> R.string.united_arab_emirates_hint
        "af" -> R.string.afganistan_hint
        "ag" -> R.string.antigua_and_barbuda_hint
        "ai" -> R.string.anguilla_hint
        "al" -> R.string.albania_hint
        "am" -> R.string.armenia_hint
        "ao" -> R.string.angola_hint
        "aq" -> R.string.antarctica_hint
        "ar" -> R.string.argentina_hint
        "as" -> R.string.american_samoa_hint
        "at" -> R.string.austria_hint
        "au" -> R.string.australia_hint
        "aw" -> R.string.aruba_hint
        "ax" -> R.string.aland_islands_hint
        "az" -> R.string.azerbaijan_hint
        "ba" -> R.string.bosnia_hint
        "bb" -> R.string.barbados_hint
        "bd" -> R.string.bangladesh_hint
        "be" -> R.string.belgium_hint
        "bf" -> R.string.burkina_faso_hint
        "bg" -> R.string.bulgaria_hint
        "bh" -> R.string.bahrain_hint
        "bi" -> R.string.burundi_hint
        "bj" -> R.string.benin_hint
        "bl" -> R.string.saint_barhelemy_hint
        "bm" -> R.string.bermuda_hint
        "bn" -> R.string.brunei_darussalam_hint
        "bo" -> R.string.bolivia_hint
        "br" -> R.string.brazil_hint
        "bs" -> R.string.bahamas_hint
        "bt" -> R.string.bhutan_hint
        "bw" -> R.string.botswana_hint
        "by" -> R.string.belarus_hint
        "bz" -> R.string.belize_hint
        "ca" -> R.string.canada_hint
        "cc" -> R.string.cocos_hint
        "cd" -> R.string.congo_democratic_hint
        "cf" -> R.string.central_african_hint
        "cg" -> R.string.congo_hint
        "ch" -> R.string.switzerland_hint
        "ci" -> R.string.cote_dlvoire_hint
        "ck" -> R.string.cook_islands_hint
        "cl" -> R.string.chile_hint
        "cm" -> R.string.cameroon_hint
        "cn" -> R.string.china_hint
        "co" -> R.string.colombia_hint
        "cr" -> R.string.costa_rica_hint
        "cu" -> R.string.cuba_hint
        "cv" -> R.string.cape_verde_hint
        "cw" -> R.string.curacao_hint
        "cx" -> R.string.christmas_island_hint
        "cy" -> R.string.cyprus_hint
        "cz" -> R.string.czech_republic_hint
        "de" -> R.string.germany_hint
        "dj" -> R.string.djibouti_hint
        "dk" -> R.string.denmark_hint
        "dm" -> R.string.dominica_hint
        "do" -> R.string.dominician_republic_hint
        "dz" -> R.string.algeria_hint
        "ec" -> R.string.ecuador_hint
        "ee" -> R.string.estonia_hint
        "eg" -> R.string.egypt_hint
        "er" -> R.string.eritrea_hint
        "es" -> R.string.spain_hint
        "et" -> R.string.ethiopia_hint
        "fi" -> R.string.finland_hint
        "fj" -> R.string.fiji_hint
        "fk" -> R.string.falkland_islands_hint
        "fm" -> R.string.micro_hint
        "fo" -> R.string.faroe_islands_hint
        "fr" -> R.string.france_hint
        "ga" -> R.string.gabon_hint
        "gb" -> R.string.united_kingdom_hint
        "gd" -> R.string.grenada_hint
        "ge" -> R.string.georgia_hint
        "gf" -> R.string.french_guyana_hint
        "gg" -> R.string.guernsey_hint
        "gh" -> R.string.ghana_hint
        "gi" -> R.string.unkown
        "gl" -> R.string.greenland_hint
        "gm" -> R.string.gambia_hint
        "gn" -> R.string.guinea_hint
        "gp" -> R.string.guadeloupe_hint
        "gq" -> R.string.equatorial_guinea_hint
        "gr" -> R.string.greece_hint
        "gt" -> R.string.guatemala_hint
        "gu" -> R.string.guam_hint
        "gw" -> R.string.guinea_bissau_hint
        "gy" -> R.string.guyana_hint
        "hk" -> R.string.hong_kong_hint
        "hn" -> R.string.honduras_hint
        "hr" -> R.string.croatia_hint
        "ht" -> R.string.haiti_hint
        "hu" -> R.string.hungary_hint
        "id" -> R.string.indonesia_hint
        "ie" -> R.string.ireland_hint
        "il" -> R.string.israil_hint
        "im" -> R.string.isle_of_man
        "is" -> R.string.iceland
        "in" -> R.string.india_hint
        "io" -> R.string.british_indian_ocean
        "iq" -> R.string.iraq_hint
        "ir" -> R.string.iran_hint
        "it" -> R.string.italia_hint
        "je" -> R.string.jersey_hint
        "jm" -> R.string.jamaica_hint
        "jo" -> R.string.jordan_hint
        "jp" -> R.string.japan_hint
        "ke" -> R.string.kenya_hint
        "kg" -> R.string.kyrgyzstan_hint
        "kh" -> R.string.cambodia_hint
        "ki" -> R.string.kiribati
        "km" -> R.string.comoros_hint
        "kn" -> R.string.saint_kitts_hint
        "kp" -> R.string.north_korea_hint
        "kr" -> R.string.south_korea_hint
        "kw" -> R.string.kuwait_hint
        "ky" -> R.string.cayman_islands_hint
        "kz" -> R.string.kazakhstan_hint
        "la" -> R.string.laos_hint
        "lb" -> R.string.lebanon_hint
        "lc" -> R.string.saint_lucia_hint
        "li" -> R.string.liechtenstein
        "lk" -> R.string.siri_lanka_hint
        "lr" -> R.string.liberia_hint
        "ls" -> R.string.lesotho_hint
        "lt" -> R.string.lithuania_hint
        "lu" -> R.string.luxembourg_hint
        "lv" -> R.string.latvia_hint
        "ly" -> R.string.libya_hint
        "ma" -> R.string.marocco_hint
        "mc" -> R.string.monaco_hint
        "md" -> R.string.moldova_hint
        "me" -> R.string.montenegro_hint
        "mf" -> R.string.saint_martin_hint
        "mg" -> R.string.madagascar_hint
        "mh" -> R.string.marshall_islands_hint
        "mk" -> R.string.north_macedonia_hint
        "ml" -> R.string.mali_hint
        "mm" -> R.string.myanmar_hint
        "mn" -> R.string.mongolia_hint
        "mo" -> R.string.macau_hint
        "mp" -> R.string.northern_mariana_hint
        "mq" -> R.string.martinique_hint
        "mr" -> R.string.mauriatana_hint
        "ms" -> R.string.montserrat_hint
        "mt" -> R.string.malta_hint
        "mu" -> R.string.mauritius_hint
        "mv" -> R.string.maldives_hint
        "mw" -> R.string.malawi_hint
        "mx" -> R.string.mexico_hint
        "my" -> R.string.malaysia_hint
        "mz" -> R.string.mozambique_hint
        "na" -> R.string.namibia_hint
        "nc" -> R.string.new_caledonia_hint
        "ne" -> R.string.niger_hint
        "nf" -> R.string.norfolk_hint
        "ng" -> R.string.nigeria_hint
        "ni" -> R.string.nicaragua
        "nl" -> R.string.netherlands_hint
        "no" -> R.string.norway_hint
        "np" -> R.string.nepal_hint
        "nr" -> R.string.nauru_hint
        "nu" -> R.string.niue_hint
        "nz" -> R.string.new_zealand_hint
        "om" -> R.string.oman_hint
        "pa" -> R.string.panama_hint
        "pe" -> R.string.peru_hint
        "pf" -> R.string.french_polynesia_hint
        "pg" -> R.string.papua_new_guinea_hint
        "ph" -> R.string.philippinies_hint
        "pk" -> R.string.pakistan_hint
        "pl" -> R.string.poland_hint
        "pm" -> R.string.saint_pierre_hint
        "pn" -> R.string.pitcairn
        "pr" -> R.string.puerto_rico_hint
        "ps" -> R.string.state_of_palestine_hint
        "pt" -> R.string.portugal_hint
        "pw" -> R.string.palau_hint
        "py" -> R.string.paraguay_hint
        "qa" -> R.string.qatar_hint
        "re" -> R.string.reunion_hint
        "ro" -> R.string.romania_hint
        "rs" -> R.string.serbia_hint
        "ru" -> R.string.russia_hint
        "rw" -> R.string.rwanda_hint
        "sa" -> R.string.saudi_arabia_hint
        "sb" -> R.string.solomon_islands_hint
        "sc" -> R.string.seychelles_hint
        "sd" -> R.string.sudan_hint
        "se" -> R.string.sweden_hint
        "sg" -> R.string.singapore_hint
        "sh" -> R.string.saint_helena_hint
        "si" -> R.string.slovenia_hint
        "sk" -> R.string.slovakia_hint
        "sl" -> R.string.sierra_leone_hint
        "sm" -> R.string.san_marino_hint
        "sn" -> R.string.senegal_hint
        "so" -> R.string.somali_hint
        "sr" -> R.string.suriname_hint
        "ss" -> R.string.south_sudan_hint
        "st" -> R.string.sao_tome_hint
        "sv" -> R.string.el_salvador_hint
        "sx" -> R.string.sint_maarten_hint
        "sy" -> R.string.syrian_hint
        "sz" -> R.string.swaziland_hint
        "tc" -> R.string.turks_and_caicos_hint
        "td" -> R.string.chad_hint
        "tg" -> R.string.togo_hint
        "th" -> R.string.thailand_hint
        "tj" -> R.string.taijikistan_hint
        "tk" -> R.string.tokelau_hint
        "tl" -> R.string.timor_leste_hint
        "tm" -> R.string.turkmenistan_hint
        "tn" -> R.string.tunisia_hint
        "to" -> R.string.tonga_hint
        "tr" -> R.string.turkey_hint
        "tt" -> R.string.trinidad_and_tobago_hint
        "tv" -> R.string.tuvalu_hint
        "tw" -> R.string.taiwan_hint
        "tz" -> R.string.tazmania_hint
        "ua" -> R.string.ukraina_hint
        "ug" -> R.string.uganda_hint
        "us" -> R.string.united_states_america_hint
        "uy" -> R.string.uruguay_hint
        "uz" -> R.string.uzbekistan_hint
        "va" -> R.string.holy_see
        "vc" -> R.string.saint_vincent_hint
        "ve" -> R.string.venezuela_hint
        "vg" -> R.string.virgin_islands_hint
        "vi" -> R.string.virgin_island_us
        "vn" -> R.string.vietnam_hint
        "vu" -> R.string.vanuatu_hint
        "wf" -> R.string.walli_and_fatuna_hint
        "ws" -> R.string.samoa_hint
        "xk" -> R.string.kosovo_hint
        "ye" -> R.string.yemen_hint
        "yt" -> R.string.mayotte_hint
        "za" -> R.string.south_africa_hint
        "zm" -> R.string.zambia_hint
        "zw" -> R.string.zimbabwe_hint
        else -> R.string.unkown
    }
}