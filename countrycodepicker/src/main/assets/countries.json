{"countries": [{"code": "ad", "phoneCode": "+376", "names": "Andorra"}, {"code": "ae", "phoneCode": "+971", "names": "United Arab Emirates (UAE)"}, {"code": "af", "phoneCode": "+93", "names": "Afghanistan"}, {"code": "ag", "phoneCode": "+1", "names": "Antigua and Barbuda"}, {"code": "ai", "phoneCode": "+1", "names": "<PERSON><PERSON><PERSON>"}, {"code": "al", "phoneCode": "+355", "names": "Albania"}, {"code": "am", "phoneCode": "+374", "names": "Armenia"}, {"code": "ao", "phoneCode": "+244", "names": "Angola"}, {"code": "aq", "phoneCode": "+672", "names": "Antarctica"}, {"code": "ar", "phoneCode": "+54", "names": "Argentina"}, {"code": "as", "phoneCode": "+1", "names": "American Samoa"}, {"code": "at", "phoneCode": "+43", "names": "Austria"}, {"code": "au", "phoneCode": "+61", "names": "Australia"}, {"code": "aw", "phoneCode": "+297", "names": "Aruba"}, {"code": "ax", "phoneCode": "+358", "names": "Åland Islands"}, {"code": "az", "phoneCode": "+994", "names": "Azerbaijan"}, {"code": "ba", "phoneCode": "+387", "names": "Bosnia And Herzegovina"}, {"code": "bb", "phoneCode": "+1", "names": "Barbados"}, {"code": "bd", "phoneCode": "+880", "names": "Bangladesh"}, {"code": "be", "phoneCode": "+32", "names": "Belgium"}, {"code": "bf", "phoneCode": "+226", "names": "Burkina Faso"}, {"code": "bg", "phoneCode": "+359", "names": "Bulgaria"}, {"code": "bh", "phoneCode": "+973", "names": "Bahrain"}, {"code": "bi", "phoneCode": "+257", "names": "Burundi"}, {"code": "bj", "phoneCode": "+229", "names": "Benin"}, {"code": "bl", "phoneCode": "+590", "names": "<PERSON>"}, {"code": "bm", "phoneCode": "+1", "names": "Bermuda"}, {"code": "bn", "phoneCode": "+673", "names": "Brunei Darussalam"}, {"code": "bo", "phoneCode": "+591", "names": "Bolivia, Plurinational State Of"}, {"code": "br", "phoneCode": "+55", "names": "Brazil"}, {"code": "bs", "phoneCode": "+1", "names": "Bahamas"}, {"code": "bt", "phoneCode": "+975", "names": "Bhutan"}, {"code": "bw", "phoneCode": "+267", "names": "Botswana"}, {"code": "by", "phoneCode": "+375", "names": "Belarus"}, {"code": "bz", "phoneCode": "+501", "names": "Belize"}, {"code": "ca", "phoneCode": "+1", "names": "Canada"}, {"code": "cc", "phoneCode": "+61", "names": "Cocos (keeling) Islands"}, {"code": "cd", "phoneCode": "+243", "names": "Congo, The Democratic Republic Of The"}, {"code": "cf", "phoneCode": "+236", "names": "Central African Republic"}, {"code": "cg", "phoneCode": "+242", "names": "Congo"}, {"code": "ch", "phoneCode": "+41", "names": "Switzerland"}, {"code": "ci", "phoneCode": "+225", "names": "Côte D'ivoire"}, {"code": "ck", "phoneCode": "+682", "names": "Cook Islands"}, {"code": "cl", "phoneCode": "+56", "names": "Chile"}, {"code": "cm", "phoneCode": "+237", "names": "Cameroon"}, {"code": "cn", "phoneCode": "+86", "names": "China"}, {"code": "co", "phoneCode": "+57", "names": "Colombia"}, {"code": "cr", "phoneCode": "+506", "names": "Costa Rica"}, {"code": "cu", "phoneCode": "+53", "names": "Cuba"}, {"code": "cv", "phoneCode": "+238", "names": "Cape Verde"}, {"code": "cw", "phoneCode": "+599", "names": "Curaçao"}, {"code": "cx", "phoneCode": "+61", "names": "Christmas Island"}, {"code": "cy", "phoneCode": "+357", "names": "Cyprus"}, {"code": "cz", "phoneCode": "+420", "names": "Czech Republic"}, {"code": "de", "phoneCode": "+49", "names": "Germany"}, {"code": "dj", "phoneCode": "+253", "names": "Djibouti"}, {"code": "dk", "phoneCode": "+45", "names": "Denmark"}, {"code": "dm", "phoneCode": "+1", "names": "Dominica"}, {"code": "do", "phoneCode": "+1", "names": "Dominican Republic"}, {"code": "dz", "phoneCode": "+213", "names": "Algeria"}, {"code": "ec", "phoneCode": "+593", "names": "Ecuador"}, {"code": "ee", "phoneCode": "+372", "names": "Estonia"}, {"code": "eg", "phoneCode": "+20", "names": "Egypt"}, {"code": "er", "phoneCode": "+291", "names": "Eritrea"}, {"code": "es", "phoneCode": "+34", "names": "Spain"}, {"code": "et", "phoneCode": "+251", "names": "Ethiopia"}, {"code": "fi", "phoneCode": "+358", "names": "Finland"}, {"code": "fj", "phoneCode": "+679", "names": "Fiji"}, {"code": "fk", "phoneCode": "+500", "names": "Falkland Islands (malvinas)"}, {"code": "fm", "phoneCode": "+691", "names": "Micronesia, Federated States Of"}, {"code": "fo", "phoneCode": "+298", "names": "Faroe Islands"}, {"code": "fr", "phoneCode": "+33", "names": "France"}, {"code": "ga", "phoneCode": "+241", "names": "Gabon"}, {"code": "gb", "phoneCode": "+44", "names": "United Kingdom"}, {"code": "gd", "phoneCode": "+1", "names": "Grenada"}, {"code": "ge", "phoneCode": "+995", "names": "Georgia"}, {"code": "gf", "phoneCode": "+594", "names": "French Guyana"}, {"code": "gh", "phoneCode": "+233", "names": "Ghana"}, {"code": "gi", "phoneCode": "+350", "names": "Gibraltar"}, {"code": "gl", "phoneCode": "+299", "names": "Greenland"}, {"code": "gm", "phoneCode": "+220", "names": "Gambia"}, {"code": "gn", "phoneCode": "+224", "names": "Guinea"}, {"code": "gp", "phoneCode": "+450", "names": "Guadeloupe"}, {"code": "gq", "phoneCode": "+240", "names": "Equatorial Guinea"}, {"code": "gr", "phoneCode": "+30", "names": "Greece"}, {"code": "gt", "phoneCode": "+502", "names": "Guatemala"}, {"code": "gu", "phoneCode": "+1", "names": "Guam"}, {"code": "gw", "phoneCode": "+245", "names": "Guinea-bissau"}, {"code": "gy", "phoneCode": "+592", "names": "Guyana"}, {"code": "hk", "phoneCode": "+852", "names": "Hong Kong"}, {"code": "hn", "phoneCode": "+504", "names": "Honduras"}, {"code": "hr", "phoneCode": "+385", "names": "Croatia"}, {"code": "ht", "phoneCode": "+509", "names": "Haiti"}, {"code": "hu", "phoneCode": "+36", "names": "Hungary"}, {"code": "id", "phoneCode": "+62", "names": "Indonesia"}, {"code": "ie", "phoneCode": "+353", "names": "Ireland"}, {"code": "il", "phoneCode": "+972", "names": "Israel"}, {"code": "im", "phoneCode": "+44", "names": "Isle Of Man"}, {"code": "in", "phoneCode": "+91", "names": "India"}, {"code": "iq", "phoneCode": "+964", "names": "Iraq"}, {"code": "ir", "phoneCode": "+98", "names": "Iran"}, {"code": "is", "phoneCode": "+354", "names": "Iceland"}, {"code": "it", "phoneCode": "+39", "names": "Italy"}]}