kotlin version: 2.1.0
error message: java.lang.AssertionError: Storage corrupted C:\Users\<USER>\AndroidStudioProjects\ProCaisseMobility\app\build\kspCaches\debug\symbolLookups\lookups.tab_i
	at org.jetbrains.kotlin.com.intellij.openapi.diagnostic.DefaultLogger.error(DefaultLogger.java:83)
	at org.jetbrains.kotlin.com.intellij.openapi.diagnostic.Logger.error(Logger.java:436)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.catchCorruption(PersistentEnumeratorBase.java:672)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.doEnumerate(PersistentEnumeratorBase.java:265)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.enumerate(PersistentEnumeratorBase.java:278)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.enumerate(PersistentMapImpl.java:490)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.access$300(PersistentMapImpl.java:57)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl$2.onDropFromCache(PersistentMapImpl.java:284)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl$2.onDropFromCache(PersistentMapImpl.java:267)
	at org.jetbrains.kotlin.com.intellij.util.containers.SLRUMap.lambda$new$0(SLRUMap.java:37)
	at org.jetbrains.kotlin.com.intellij.util.containers.LinkedCustomHashMap.put(LinkedCustomHashMap.java:137)
	at org.jetbrains.kotlin.com.intellij.util.containers.SLRUMap.put(SLRUMap.java:80)
	at org.jetbrains.kotlin.com.intellij.util.containers.SLRUCache.get(SLRUCache.java:36)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.doAppendData(PersistentMapImpl.java:540)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.appendData(PersistentMapImpl.java:514)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMap.appendData(PersistentHashMap.java:140)
	at org.jetbrains.kotlin.incremental.storage.AppendableLazyStorage.append(LazyStorage.kt:128)
	at org.jetbrains.kotlin.incremental.storage.AppendableSetBasicMap.append(BasicMap.kt:167)
	at org.jetbrains.kotlin.incremental.TrackedLookupMap.append(LookupStorage.kt:317)
	at org.jetbrains.kotlin.incremental.LookupStorage.addAll(LookupStorage.kt:124)
	at org.jetbrains.kotlin.incremental.BuildUtilKt.update(buildUtil.kt:134)
	at com.google.devtools.ksp.LookupStorageWrapperImpl.update(IncrementalContext.kt:231)
	at com.google.devtools.ksp.common.IncrementalContextBase.updateLookupCache(IncrementalContextBase.kt:133)
	at com.google.devtools.ksp.common.IncrementalContextBase.updateCaches(IncrementalContextBase.kt:365)
	at com.google.devtools.ksp.common.IncrementalContextBase.access$updateCaches(IncrementalContextBase.kt:62)
	at com.google.devtools.ksp.common.IncrementalContextBase$updateCachesAndOutputs$1.invoke(IncrementalContextBase.kt:476)
	at com.google.devtools.ksp.common.IncrementalContextBase$updateCachesAndOutputs$1.invoke(IncrementalContextBase.kt:428)
	at com.google.devtools.ksp.common.IncrementalContextBase.closeFilesOnException(IncrementalContextBase.kt:408)
	at com.google.devtools.ksp.common.IncrementalContextBase.updateCachesAndOutputs(IncrementalContextBase.kt:428)
	at com.google.devtools.ksp.AbstractKotlinSymbolProcessingExtension.doAnalysis(KotlinSymbolProcessingExtension.kt:376)
	at org.jetbrains.kotlin.cli.jvm.compiler.TopDownAnalyzerFacadeForJVM.analyzeFilesWithJavaIntegration(TopDownAnalyzerFacadeForJVM.kt:112)
	at org.jetbrains.kotlin.cli.jvm.compiler.TopDownAnalyzerFacadeForJVM.analyzeFilesWithJavaIntegration$default(TopDownAnalyzerFacadeForJVM.kt:75)
	at org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.analyze$lambda$9(KotlinToJVMBytecodeCompiler.kt:356)
	at org.jetbrains.kotlin.cli.common.messages.AnalyzerWithCompilerReport.analyzeAndReport(AnalyzerWithCompilerReport.kt:112)
	at org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.analyze(KotlinToJVMBytecodeCompiler.kt:347)
	at org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.repeatAnalysisIfNeeded(KotlinToJVMBytecodeCompiler.kt:264)
	at org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.repeatAnalysisIfNeeded(KotlinToJVMBytecodeCompiler.kt:264)
	at org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.runFrontendAndGenerateIrUsingClassicFrontend(KotlinToJVMBytecodeCompiler.kt:177)
	at org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.compileModules$cli(KotlinToJVMBytecodeCompiler.kt:102)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:169)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:43)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:102)
	at org.jetbrains.kotlin.cli.common.CLICompiler.exec(CLICompiler.kt:316)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1554)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base/java.lang.reflect.Method.invoke(Unknown Source)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport$1.run(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(Unknown Source)
	at java.base/java.security.AccessController.doPrivileged(Unknown Source)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
Caused by: org.jetbrains.kotlin.com.intellij.util.io.CorruptedException: Storage corrupted C:\Users\<USER>\AndroidStudioProjects\ProCaisseMobility\app\build\kspCaches\debug\symbolLookups\lookups.tab_i
	at org.jetbrains.kotlin.com.intellij.util.io.IntToIntBtree$BtreeIndexNodeView.locate(IntToIntBtree.java:911)
	at org.jetbrains.kotlin.com.intellij.util.io.IntToIntBtree$BtreeIndexNodeView.access$100(IntToIntBtree.java:308)
	at org.jetbrains.kotlin.com.intellij.util.io.IntToIntBtree.get(IntToIntBtree.java:177)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentBTreeEnumerator.enumerateImpl(PersistentBTreeEnumerator.java:584)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.lambda$doEnumerate$0(PersistentEnumeratorBase.java:266)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.catchCorruption(PersistentEnumeratorBase.java:654)
	... 56 more


